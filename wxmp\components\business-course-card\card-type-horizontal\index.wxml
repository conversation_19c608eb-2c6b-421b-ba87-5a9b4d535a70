<wxs module="utils">
  function findFour(arr) {
    if (arr.length > 3) {
      return arr.slice(0, 3);
    } else {
      return arr;
    }
  }
  module.exports = {
    findFour: findFour,
  };
</wxs>
<view class="course-horizontal-card" style="height: {{ collectionHeight !== undefined ? collectionHeight + 'px' : '' }}" wx:if="{{item}}" bindtap="goDetail" data-item="{{item}}">
  <view style="display: flex;">
    <image src="{{item.mp_img}}" class="left-bg" wx:if="{{item.show_cover === 1}}"></image>
    <view class="fonts">
      <view class="title-h">
        <!-- 卡片标题 -->
        <course-card-title isSearch="{{isSearch}}" title="{{item.name}}" titleEllipsis="2" />

        <!-- 首页展示描述 -->
        <view class="label-text text-ellipsis-2" wx:if="{{isInHome}}">
          {{item.summary}}
        </view>
      </view>


      <!-- 课程列表时的底部样式 -->
      <view class="fonts-bootom">
        <block wx:if="{{!isInHome}}">
          <!-- 老师列表 -->
          <view class="teacher-list">
            <block wx:if="{{item.show_teacher === 1 && item.teachers.length>0}}">
              <image class="img" wx:for="{{utils.findFour(item.teachers)}}" wx:key="index" src="{{item.portrait}}"></image>
            </block>
          </view>
          <view class="right-box">
            <!-- 价格文本 -->
            <course-card-price-text isQualification="{{!!item.is_bought}}" usability="{{item.usability}}" price="{{item.pintuan?item.pintuan.group_price:item.product.price}}" oldPrice="{{item.product.o_price}}" couponPrice="{{item.coupon_price || ''}}" isHasGroupon="{{!!item.pintuan}}" isJoinGroupon="{{item.pintuan.record_info.status===1}}" desc="{{item.participants}}" />
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 首页时展示 -->
  <view class="fonts-bootom" style="display: flex; justify-content: space-between;align-items: center;margin-top: 30rpx;" wx:if="{{isInHome}}">
    <!-- 价格文本 -->
    <course-card-price-text usability="{{item.usability}}" isQualification="{{!!item.is_bought}}" price="{{item.pintuan?item.pintuan.group_price:item.product.price}}" oldPrice="{{item.product.o_price}}" couponPrice="{{item.coupon_price || ''}}" isHasGroupon="{{!!item.pintuan}}" isJoinGroupon="{{item.pintuan.record_info.status===1}}" isInHome="{{true}}" isHideDescText="{{true}}" />
    <!-- 描述 -->

    <view style="display: flex; align-items: center;" wx:if="{{!isQualification}}">
      <text class="study-num">{{item.participants}}人已学</text>
      <!-- 按钮 -->
      <course-card-button usability="{{item.usability}}" isQualification="{{!!item.is_bought}}"></course-card-button>
    </view>
  </view>
</view>
<!-- <view class="course-horizontal-card" style="display: none;">
  <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/course_bg.png" class="left-bg"></image>
  <view class="fonts">
    <view class="title text-ellipsis-1">2020重庆邮政 · 考前冲刺 重庆事业</view>
    <view class="label-text">
      配新大纲全套资料
    </view>
    <view class="fonts-bootom">
      <view class="teacher-list">
      </view>

      <view class="right-box">
        <view class="money">
          <span class="coupons">领劵立减100元</span><span class="symbol">￥</span><span class="num">168</span>
        </view>
        <view class="study-num">101人已学</view>
      </view>
    </view>
  </view>
</view> -->