// pages/my/addEducation/index.js
const API = require("@/config/api")
const UTIL = require("@/utils/util")

Page({
  /**
   * 页面的初始数据
   */
  data: {
    educationId: null,
    // 表单数据
    formData: {
      degree: "", // 学历名称，用于显示
      degree_id: "", // 学历ID，用于提交
      major: "",
      diploma: "",
      major_id: "",
      id: "",
    },
    educationIdForSelector: "",
    showMajorField: true,

    // 学历选择器
    degreeShow: false,
    degreeColumns: [],
    degreeSelectedIndex: 0,

    // 专业选择器
    majorShow: false,
    majorList: [],
    selectedMajorIds: [], // 新增：存储专业ID数组用于回显

    // 学位选择器
    diplomaShow: false,
    diplomaColumns: [],
    diplomaSelectedIndex: 0,

    // 表单状态
    isFormComplete: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    this.initSelectors()

    // 如果传入了id，说明是编辑模式
    if (options.id) {
      try {
        const res = await UTIL.request(API.getEducationInfo, { id: options.id })
        if (res && res.data) {
          const educationData = res.data
          // 设置表单数据
          this.setData({
            "formData.id": educationData.id,
            "formData.degree": educationData.education_text,
            "formData.degree_id": educationData.education,
            "formData.major": educationData.major_text,
            "formData.major_id": educationData.major_id,
            "formData.diploma": educationData.degree_text,
            educationIdForSelector: educationData.education,
            educationId: options.id,
          })

          // 如果有专业ID，需要获取专业路径的ID数组用于回显
          if (
            educationData.grand_id &&
            educationData.parent_id &&
            educationData.major_id
          ) {
            this.setData({
              selectedMajorIds: [
                educationData.grand_id,
                educationData.parent_id,
                educationData.major_id,
              ],
            })
          }

          // 获取专业列表
          const param = {
            pid: 0,
            education_id: educationData.education,
          }
          const majorRes = await UTIL.request(API.getMajorList, param)
          const hasMajorOptions =
            majorRes && majorRes.data && majorRes.data.length > 0
          this.setData({
            showMajorField: hasMajorOptions,
            majorList: hasMajorOptions ? majorRes.data : [],
          })
        }
      } catch (error) {
        console.error("获取教育经历详情失败:", error)
        wx.showToast({
          title: "获取详情失败",
          icon: "none",
        })
      }
    }

    this.checkFormComplete()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  /**
   * 初始化选择器数据
   */
  initSelectors() {
    const APP = getApp()
    // 学历数据
    const degreeData =
      APP.globalData?.serverConfig?.education_list?.map((item) => ({
        text: item.name,
        value: item.id,
        id: item.id,
      })) || []

    // 学位数据
    const diplomaData =
      APP.globalData?.serverConfig?.degree_list?.map((item) => ({
        text: item.name,
        value: item.id,
        id: item.id,
      })) || []

    this.setData({
      degreeColumns: [{ values: degreeData }],
      diplomaColumns: [{ values: diplomaData }],
    })
  },

  /**
   * 表单项点击事件
   */
  onFormItemTap(e) {
    console.log("addEducation 点击事件触发", e.currentTarget.dataset)

    const { field } = e.currentTarget.dataset
    const formData = this.data.formData

    console.log("点击的字段:", field, "表单数据:", formData)

    if (field === "degree") {
      this.openDegreeSelector()
    } else if (field === "major") {
      if (!this.data.formData.degree) {
        wx.showToast({
          title: "请先选择学历",
          icon: "none",
        })
        return
      }
      this.openMajorSelector()
    } else if (field === "diploma") {
      this.openDiplomaSelector()
    }
  },

  /**
   * 打开学历选择器
   */
  openDegreeSelector() {
    // 设置回显索引
    let selectedIndex = 0
    if (this.data.formData.degree) {
      const degreeData = this.data.degreeColumns[0].values
      const index = degreeData.findIndex(
        (item) => item.text === this.data.formData.degree
      )
      if (index >= 0) {
        selectedIndex = index
      }
    }

    this.setData({
      degreeSelectedIndex: selectedIndex,
      degreeShow: true,
    })

    // 设置picker的默认索引
    setTimeout(() => {
      const picker = this.selectComponent("#degreePicker")
      if (picker && picker.setIndexes) {
        picker.setIndexes([selectedIndex])
      }
    }, 50)
  },

  /**
   * 关闭学历选择器
   */
  onDegreeClose() {
    this.setData({
      degreeShow: false,
    })
  },

  /**
   * 确认学历选择
   */
  async onDegreeConfirm(e) {
    const { value, index } = e.detail
    if (!value || value.length === 0) return

    const selectedItem = value[0]

    this.setData({
      "formData.degree": selectedItem.text,
      "formData.degree_id": selectedItem.id,
      degreeShow: false,
      educationIdForSelector: selectedItem.id,
      "formData.major": "",
      "formData.major_id": "",
    })

    // 请求专业数据
    try {
      const param = {
        pid: 0,
        education_id: selectedItem.id,
      }
      const res = await UTIL.request(API.getMajorList, param)

      // 根据返回数据决定是否显示专业选项
      const hasMajorOptions = res && res.data && res.data.length > 0
      this.setData({
        showMajorField: hasMajorOptions,
        majorList: hasMajorOptions ? res.data : [],
        // 学历改变时，重置专业选择器状态
        selectedMajorIds: [],
        "formData.major": "",
        "formData.major_id": "",
      })
    } catch (error) {
      console.error("获取专业数据失败:", error)
      this.setData({
        showMajorField: false,
        majorList: [],
        selectedMajorIds: [],
        "formData.major": "",
        "formData.major_id": "",
      })
      wx.showToast({
        title: "获取专业数据失败",
        icon: "none",
      })
    }

    // 检查表单完整性
    this.checkFormComplete()
  },

  /**
   * 打开专业选择器
   */
  openMajorSelector() {
    this.setData({
      majorShow: true,
    })
  },

  /**
   * 关闭专业选择器
   */
  onMajorClose() {
    this.setData({
      majorShow: false,
    })
  },

  /**
   * 确认专业选择
   */
  onMajorConfirm(e) {
    console.log("专业选择确认:", e.detail)
    const { major, text, majorIds } = e.detail

    this.setData({
      "formData.major": text,
      "formData.major_id": major.id, // 假设返回的数据中有id
      selectedMajorIds: majorIds || [], // 保存专业ID数组用于回显
      majorShow: false,
    })

    // 检查表单完整性
    this.checkFormComplete()
  },

  /**
   * 打开学位选择器
   */
  openDiplomaSelector() {
    // 设置回显索引
    let selectedIndex = 0
    if (this.data.formData.diploma) {
      const diplomaData = this.data.diplomaColumns[0].values
      const index = diplomaData.findIndex(
        (item) => item.text === this.data.formData.diploma
      )
      if (index >= 0) {
        selectedIndex = index
      }
    }

    this.setData({
      diplomaSelectedIndex: selectedIndex,
      diplomaShow: true,
    })

    // 设置picker的默认索引
    setTimeout(() => {
      const picker = this.selectComponent("#diplomaPicker")
      if (picker && picker.setIndexes) {
        picker.setIndexes([selectedIndex])
      }
    }, 50)
  },

  /**
   * 关闭学位选择器
   */
  onDiplomaClose() {
    this.setData({
      diplomaShow: false,
    })
  },

  /**
   * 确认学位选择
   */
  onDiplomaConfirm(e) {
    const { value, index } = e.detail
    if (!value || value.length === 0) return

    const selectedItem = value[0]

    this.setData({
      "formData.diploma": selectedItem.text,
      diplomaShow: false,
    })

    // 检查表单完整性
    this.checkFormComplete()
  },

  /**
   * 检查表单完整性
   */
  checkFormComplete() {
    const { degree } = this.data.formData
    let isComplete = !!degree // 学历必填

    // 只有在显示专业字段时才校验专业
    if (this.data.showMajorField) {
      isComplete = isComplete && !!this.data.formData.major
    }

    this.setData({
      isFormComplete: isComplete,
    })
  },

  /**
   * 保存教育经历
   */
  async onAddJobs() {
    const { degree, major, id, degree_id } = this.data.formData

    // 验证必填项
    if (!degree) {
      wx.showToast({
        title: "请选择学历",
        icon: "none",
      })
      return
    }

    // 只在显示专业字段时才校验专业
    if (this.data.showMajorField && !major) {
      wx.showToast({
        title: "请选择专业",
        icon: "none",
      })
      return
    }

    // 构造保存参数
    const params = {
      education: this.data.educationIdForSelector,
      degree: degree_id,
      major_id: this.data.formData.major_id,
    }

    // 编辑模式需要传入id
    if (id) {
      params.id = id
    }

    try {
      const res = await UTIL.request(API.saveEducation, params)
      if (res.data === true) {
        wx.showToast({
          title: "保存成功",
          icon: "none",
        })

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        throw new Error(res.error?.message || "保存失败")
      }
    } catch (error) {
      console.error("保存教育经历失败:", error)
      wx.showToast({
        title: error.message || "保存失败",
        icon: "none",
      })
    }
  },
})
