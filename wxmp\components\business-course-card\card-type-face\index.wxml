<!-- 大图模式 -->
<view class="course-card face-list-card" catchtap="goDetail" data-item="{{item}}" wx:if="{{item.img_data.type === 2}}">
  <view>
    <image src="{{item.img_data.url}}" class="bg-img"></image>
  </view>
  <view class="bottom-box">
    <view class="left-box">
      <view class="title">
        {{item.name}}
      </view>
      <view class="time" wx:if="{{item.name.length<16}}">{{item.summary}}</view>
    </view>
    <view class="see-btn">查看详情</view>
  </view>
</view>

<!-- 小图模式 -->
<view class=" course-card face-defaul-card" catchtap="goDetail" data-item="{{item}}" wx:elif="{{item.img_data.type === 1}}">
  <view style="display: flex;">
    <image src="{{item.img_data.url}}" class="banner-img"></image>
    <view>
      <view class="title-h">
        <!-- 卡片标题 -->
        <course-card-title title="{{item.name}}" titleEllipsis="2" subTitle="" />
      </view>
      <!-- 首页展示描述 -->
      <view class="label-text text-ellipsis-1" wx:if="{{isInHome}}">
        {{item.summary}}
      </view>
    </view>
  </view>
  <view class="font-bottom">
    <text class="study-num">{{item.participants}}人已学</text>
    <!-- 按钮 -->
    <course-card-button text="查看详情"></course-card-button>
  </view>
</view>

<!-- 无图模式 -->
<view class="course-card" catchtap="goDetail" data-item="{{item}}" wx:elif="{{item.img_data.type === 0 || !item.img_data}}">
  <!-- 卡片标题 -->
  <view style="height:{{isInHome?'130rpx':''}}">
    <course-card-title title="{{item.name}}" subTitle="{{item.summary}}" showLabel="{{!isInHome}}" />
  </view>
  <view class="label-list">
    <view class="try-listen" style="color: {{ite.font_color}};background: {{ite.color}};" wx:for="{{item.new_tags}}" wx:key="index" wx:for-item="ite">
      {{ite.tag}}
    </view>
  </view>
  <view class="font-bottom">
    <text class="study-num">{{item.participants}}人已学</text>
    <!-- 按钮 -->
    <course-card-button text="查看详情"></course-card-button>
  </view>
</view>