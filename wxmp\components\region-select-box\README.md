# 地区选择组件 (region-select-box)

一个支持三级联动的地区选择组件，可以在页面或弹窗中使用。

## 功能特性

- ✅ 三级联动选择（省-市-区县）
- ✅ 已选择地区标签展示
- ✅ 支持单个移除和批量清空
- ✅ 支持页面和弹窗两种使用模式
- ✅ 加载状态和空状态处理
- ✅ 完整的事件系统

## 属性 (Properties)

| 属性名          | 类型    | 默认值 | 必填 | 说明             |
| --------------- | ------- | ------ | ---- | ---------------- |
| selectedRegions | Array   | []     | 否   | 已选择的地区列表 |
| provinceList    | Array   | []     | 否   | 省份列表         |
| cityList        | Array   | []     | 否   | 城市列表         |
| districtList    | Array   | []     | 否   | 区县列表         |
| currentProvince | Object  | null   | 否   | 当前选中的省份   |
| currentCity     | Object  | null   | 否   | 当前选中的城市   |
| loading         | Object  | {}     | 否   | 加载状态对象     |
| inPopup         | Boolean | false  | 否   | 是否在弹窗中使用 |
| popupHeight     | String  | '80vh' | 否   | 弹窗模式下的高度 |

## 事件 (Events)

| 事件名               | 参数              | 说明             |
| -------------------- | ----------------- | ---------------- |
| provinceClick        | {province, index} | 省份点击事件     |
| cityClick            | {city, index}     | 城市点击事件     |
| districtClick        | {district, index} | 区县点击事件     |
| districtLongPress    | {district, index} | 区县长按事件     |
| removeSelectedRegion | {index}           | 移除已选地区事件 |
| clearAllSelected     | -                 | 清空所有选择事件 |
| confirmSelection     | -                 | 确认选择事件     |

## 使用方式

### 1. 在页面中使用

```json
// page.json
{
  "usingComponents": {
    "region-select-box": "/components/region-select-box/index"
  }
}
```

```xml
<!-- page.wxml -->
<region-select-box
  selectedRegions="{{selectedRegions}}"
  provinceList="{{provinceList}}"
  cityList="{{cityList}}"
  districtList="{{districtList}}"
  currentProvince="{{currentProvince}}"
  currentCity="{{currentCity}}"
  loading="{{loading}}"
  bind:provinceClick="handleProvinceClick"
  bind:cityClick="handleCityClick"
  bind:districtClick="handleDistrictClick"
  bind:districtLongPress="handleDistrictLongPress"
  bind:removeSelectedRegion="removeSelectedRegion"
  bind:clearAllSelected="clearAllSelected"
  bind:confirmSelection="confirmSelection"
/>
```

### 2. 在弹窗中使用

```xml
<!-- 使用van-popup -->
<van-popup show="{{showRegionPopup}}" position="bottom" round bind:close="closeRegionPopup">
  <region-select-box
    inPopup="{{true}}"
    popupHeight="70vh"
    selectedRegions="{{selectedRegions}}"
    provinceList="{{provinceList}}"
    cityList="{{cityList}}"
    districtList="{{districtList}}"
    currentProvince="{{currentProvince}}"
    currentCity="{{currentCity}}"
    loading="{{loading}}"
    bind:provinceClick="handleProvinceClick"
    bind:cityClick="handleCityClick"
    bind:districtClick="handleDistrictClick"
    bind:districtLongPress="handleDistrictLongPress"
    bind:removeSelectedRegion="removeSelectedRegion"
    bind:clearAllSelected="clearAllSelected"
    bind:confirmSelection="confirmSelection"
  />
</van-popup>
```

### 3. 事件处理示例

```javascript
// page.js
Page({
  data: {
    selectedRegions: [],
    provinceList: [],
    cityList: [],
    districtList: [],
    currentProvince: null,
    currentCity: null,
    loading: {
      province: false,
      city: false,
      district: false,
    },
  },

  // 处理省份点击
  handleProvinceClick(e) {
    const { province, index } = e.detail
    this.selectProvince(province)
  },

  // 处理城市点击
  handleCityClick(e) {
    const { city, index } = e.detail
    this.selectCity(city)
  },

  // 处理区县点击
  handleDistrictClick(e) {
    const { district, index } = e.detail
    this.toggleDistrict(district)
  },

  // 处理区县长按
  handleDistrictLongPress(e) {
    const { district, index } = e.detail
    if (district.name === "全部") {
      this.invertSelectDistricts()
    }
  },

  // 移除已选择的地区
  removeSelectedRegion(e) {
    const { index } = e.detail
    // 实现移除逻辑
  },

  // 清空所有选择
  clearAllSelected() {
    // 实现清空逻辑
  },

  // 确认选择
  confirmSelection() {
    // 实现确认逻辑
  },
})
```

## 注意事项

1. **弹窗模式**: 设置 `inPopup="{{true}}"` 时，组件会调整样式以适应弹窗环境
2. **高度控制**: 在弹窗中使用时，可通过 `popupHeight` 属性控制组件高度
3. **事件处理**: 所有用户交互都通过事件向父组件传递，保持组件的纯净性
4. **数据格式**: 确保传入的数据格式与原页面保持一致

## 兼容性

- 支持微信小程序
- 兼容各种弹窗组件（van-popup、自定义弹窗等）
- 保持与原有功能 100%兼容
