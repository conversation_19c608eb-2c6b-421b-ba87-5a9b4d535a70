const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Component({
  options: {
    addGlobalClass: true, //使用全局样式
    isLoad: false, // 页面是否加载完成
    multipleSlots: true,
  },
  properties: {
    isEdit: {
      type: Boolean,
      value: false,
    },
    noticeData: {
      type: Object,
      value: {},
    },
  },
  data: {},
  methods: {
    // 点击卡片
    onCardTap(e) {
      if (this.data.isEdit) {
        // 编辑模式下，点击选中/取消选中
        this.selectNotice()
      } else {
        // 非编辑模式下，跳转到详情页
        this.goDetail()
      }
    },

    // 选中公告
    selectNotice() {
      this.triggerEvent("selectNotice", {
        noticeId: this.data.noticeData.id,
      })
    },

    // 跳转到详情页
    goDetail() {
      ROUTER.navigateTo({
        path: "/pages/notice/detail/index",
        params: {
          id: this.data.noticeData.id,
        },
      })
    },
  },
})
