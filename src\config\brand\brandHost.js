const BRAND_HOST = [
  {
    brandKey: "skb",
    url: "share.shikaobang.cn",
    openidKey: "share_skb",
    openidBrandkey: "skb",
  },
  {
    brandKey: "skb",
    url: "share.test.shikaobang.cn",
    openidKey: "share_skb_test",
    openidBrandkey: "skb",
  },
  {
    brandKey: "jbcgk",
    url: "share.jbcgk.com",
    openidKey: "share_jbcgk",
    openidBrandkey: "jbcgk",
  },
  {
    brandKey: "jbcgk",
    url: "share.test.jbcgk.com",
    openidKey: "share_jbcgk_test",
    openidBrandkey: "jbcgk",
  },
  {
    brandKey: "gpjs",
    url: "share.guopeichina.com",
    openidKey: "share_gpjiaoshi",
    openidBrandkey: "gpjiaoshi",
  },
  {
    brandKey: "gpjs",
    url: "share.test.guopeichina.com",
    openidKey: "share_gpjiaoshi_test",
    openidBrandkey: "gpjiaoshi",
  },
  {
    brandKey: "gpjs",
    url: "share.jbcjiaoshi.com",
    openidKey: "share_gpjs",
    openidBrandkey: "gpjiaoshi",
  },
  {
    brandKey: "gpjs",
    url: "share.test.jbcjiaoshi.com",
    openidKey: "share_gpjs_test",
    openidBrandkey: "gpjiaoshi",
  },
  {
    brandKey: "xuandiao",
    url: "share.xuandiaobang.com",
    openidKey: "share_xuandiao",
    openidBrandkey: "xuandiao",
  },
  {
    brandKey: "xuandiao",
    url: "share.test.xuandiaobang.com",
    openidKey: "share_xuandiao_test",
    openidBrandkey: "xuandiao",
  },
  {
    brandKey: "jbczsb",
    url: "share.jbczsb.com",
    openidKey: "share_jbczsb",
    openidBrandkey: "jbczsb",
  },
  {
    brandKey: "jbczsb",
    url: "share.test.jbczsb.com",
    openidKey: "share_jbczsb_test",
    openidBrandkey: "jbczsb",
  },
  {
    brandKey: "jbcyk",
    url: "share.yianjiaoyu.com",
    openidKey: "share_jbcyk",
    openidBrandkey: "jbcyk",
  },
  {
    brandKey: "jbcyk",
    url: "share.test.yianjiaoyu.com",
    openidKey: "share_jbcyk_test",
    openidBrandkey: "jbcyk",
  },
  {
    brandKey: "jbcshangan",
    url: "share.jbcshangan.com",
    openidKey: "share_jbcshangan",
    openidBrandkey: "jbcshangan",
  },
  {
    brandKey: "jbcshangan",
    url: "share.test.jbcshangan.com",
    openidKey: "share_jbcshangan_test",
    openidBrandkey: "jbcshangan",
  },
]

export default BRAND_HOST
