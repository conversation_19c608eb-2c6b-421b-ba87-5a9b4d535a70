<wxs module="utils" src="./index.wxs"></wxs>
<view class="big-data" bindtap="onPageClick" data-path="root">
  <!-- 固定头部导航栏 -->
  <view class="fixed-header {{showFixedHeader ? 'show' : 'hide'}}" style="padding-top: {{statusBarHeight}}px;">
    <view class="header-content">
      <view class="header-left" catch:tap="onBackClick">
        <view class="back-icon"></view>
      </view>
      <view class="header-title">进面分数线</view>
      <view class="header-right"></view>
    </view>
  </view>

  <image class="back-btn" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data-top_back.png" mode="" catch:tap="onBackClick" />
  <view class="top-bg">
    <image class="bg-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/interview_bg.png" mode="" />
    <view class="top-area">
      <view class="title">进面分数线</view>
      <view class="select-one">
        <view class="select-item" bindtap="onExamTypeClick">
          <view class="text">{{selectedExamTypeText}}</view>
          <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
        <view class="select-item" bindtap="onRegionClick">
          <view class="text">{{selectedRegionText}}</view>
          <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
      </view>
      <view class="select-item w100" bindtap="onSpecificExamClick">
        <view class="text">{{selectedSpecificExamText}}</view>
        <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
      </view>
    </view>
  </view>
  <view class="main-content">
    <!-- Tab导航区域 -->
    <view class="tab-container">
      <scroll-view class="tab-scroll" scroll-x="true" scroll-with-animation="true" scroll-left="{{scrollLeft}}" show-scrollbar="{{false}}" enhanced="{{true}}">
        <view class="tab-list">
          <view wx:for="{{tabList}}" wx:key="index" class="tab-item {{currentTab === index ? 'active' : ''}}" data-index="{{index}}" bindtap="onTabClick" id="tab-{{index}}">
            {{item.name}}
          </view>
        </view>
      </scroll-view>
    </view>
    <!-- 内容区域 -->
    <view class="content-container">
      <view class="content-area">
        <!-- 招聘公告标题 -->
        <view class="announcement-title" catch:tap="goDetail">
          <text class="text">巫溪县事业单位2025念第二季度公开遴选工作人员27名公告遴选工作人员27名公告</text>
          <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/announcement_title_arrow.png" mode="" />
        </view>

        <view class="data-flex-box">
          <view class="flex-item">
            <view class="num">145.8</view>
            <view class="text">最低进面分数线</view>
          </view>
          <view class="flex-item">
            <view class="num cred">145.8</view>
            <view class="text">最高进面分数线</view>
          </view>
          <view class="flex-item">
            <view class="num corange">145.8</view>
            <view class="text">平均进面分数线</view>
          </view>
        </view>

        <view class="data-desc {{dataDescExpanded ? 'expanded' : 'collapsed'}}">
          <view class="data-desc-wrapper">
            <text class="data-desc-text">数据说明：本站发布的招考资讯均来源于招录官方网站，由事考帮整理编辑，如遇报考疑问请咨询招考官方，若涉及版权或错误信息，请提交反馈到本站予以更新或删除。数据仅供参考，具体请以官方公告为准。</text>
            <view wx:if="{{dataDescExpanded}}" class="expand-btn inline" bindtap="onToggleDataDesc">
              <text class="blue-text">收起</text>
              <image class="arrow-icon rotated" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_down.png" mode="" />
            </view>
          </view>
          <view wx:if="{{!dataDescExpanded}}" class="expand-btn" bindtap="onToggleDataDesc">
            <text class="blue-text">展开</text>
            <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_down.png" mode="" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 各地域报名数据 -->
  <view class="area-box" catch:tap="hideTooltip">
    <view class="title">各地域进面数据</view>
    <view catch:tap>
      <score-all-echarts id="score-bar-id-multiple" scoreData="{{scoreData}}" chartType="interview-score"></score-all-echarts>
    </view>
  </view>

  <view class="job-search" id="job-search-section">
    <view class="title">岗位查询</view>
    <view class="search-box">
      <image class="bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_bg.png" mode="" />
      <!-- <view class="top-area">
        <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_top_icon.png" mode="" />
        <view class="text">2025年重庆市公务员</view>
      </view> -->
      <view class="white-box">
        <view class="select-item" bindtap="onSearchRegionClick">
          <view class="left">
            <view class="sp5">
              <text>地</text>
              <text>区：</text>
            </view>
            <view>{{selectedSearchRegionText}}</view>
          </view>
          <image class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
        <view class="select-item" bindtap="onSearchUnitClick">
          <view class="left">
            <view class="sp5">
              用人单位：
            </view>
            <view>{{selectedSearchUnitText}}</view>
          </view>
          <image class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
        <view class="select-item input-item">
          <view class="left">
            <view class="sp5">
              <text>职</text>
              <text>位：</text>
            </view>
            <input class="position-input" type="text" placeholder="请输入职位名称或代码" value="{{positionInput}}" bindinput="onPositionInput" placeholder-class="input-placeholder" />
          </view>
        </view>
        <view class="search-btn" bindtap="onSearchClick">
          <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_search_icon.png" mode="" />
          <text>查询</text>
        </view>
        <view class="delete-text" bindtap="onClearClick">清除</view>
      </view>
    </view>
  </view>

  <!-- 查询结果表格区域 -->
  <view class="search-result-table" wx:if="{{showSearchResult}}">
    <view class="table-header">
      <view class="header-item header-position">职位名称</view>
      <view class="header-item header-unit">工作单位</view>
      <view class="header-item header-ratio">竞争比数据</view>
    </view>
    <view class="table-body">
      <view wx:for="{{searchResultList}}" wx:key="index" class="table-row">
        <view class="table-item table-position">
          <text class="position-name">{{item.positionName}}</text>
        </view>
        <view class="table-item table-unit">
          <text class="unit-name">{{item.unitName}}</text>
        </view>
        <view class="table-item table-ratio">
          <text class="ratio-value">{{item.competitionRatio}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作区域 -->
  <tabbar-box>
    <view class="bottom-box">
      <view class="left">
        <view class="icon-item" bind:tap="goJobs">
          <image class="icon-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_service.png" mode="" />
          <view class="icon-text">选岗咨询</view>
        </view>
        <view class="icon-item" bind:tap="checkNews">
          <image class="icon-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/news.png" mode="" />
          <view class="icon-text">查看公告</view>
        </view>
      </view>
      <view class="right-btn" bind:tap="goWeb">岗位报考查询</view>
    </view>
  </tabbar-box>

  <!-- 下拉框弹窗 -->
  <van-popup show="{{ optPopShow }}" round position="bottom" bind:close="OptClose">
    <van-picker id="myPicker" show-toolbar title="{{ optTitle }}" columns="{{ optList }}" value-key="key" columns-field-names="{{ {text: 'name', value: 'key'} }}" default-index="{{ defaultIndex }}" toolbar-class="top-area" column-class="column-area" active-class="active-item" bind:cancel="OptClose" bind:confirm="onConfirm" />
  </van-popup>
</view>

<!-- 底部导航栏 -->
<!-- <home-tabbar active="my" /> -->