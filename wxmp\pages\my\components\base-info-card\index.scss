.base-info-card {
  padding: 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-top: 32rpx;
  .title-area {
    display: flex;
    align-items: center;
    .title {
      font-size: 30rpx;
      color: #3c3d42;
      font-weight: bold;
      margin-right: 16rpx;
    }
    .tips {
      padding: 4rpx 12rpx;
      box-sizing: border-box;
      background: rgba(255, 106, 77, 0.05);
      border-radius: 8rpx;
      font-size: 20rpx;
      color: #ff6a4d;
    }
  }
  .form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 48rpx;
    .left {
      font-size: 26rpx;
      color: #666666;
      display: flex;
      .cred {
        margin-left: 8rpx;
        margin-top: 2rpx;
        color: #e60003;
      }
    }
    .right {
      display: flex;
      align-items: center;
      .text {
        color: #c2c5cc;
        font-size: 26rpx;
        margin-right: 4rpx;
        &.has-value {
          color: #3c3d42;
          font-size: 26rpx;
        }
      }
      .arrow-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}
