# 优先级 D 规则：谨慎使用 {#priority-d-rules-use-with-caution}

::: warning 注意
此 Vue.js 风格指南已过时，需要重新审查。如果您有任何问题或建议，请[提交 issue](https://github.com/vuejs/docs/issues/new)。
:::

Vue 的某些功能存在是为了适应罕见的边缘情况或从遗留代码库更平滑地迁移。但是，当过度使用时，它们可能会使您的代码更难维护，甚至成为错误的来源。这些规则揭示了潜在的风险功能，描述了何时以及为什么应该避免它们。

## `scoped` 中的元素选择器 {#element-selectors-with-scoped}

**元素选择器应该避免在 `scoped` 中出现。**

在 `scoped` 样式中，类选择器比元素选择器更好，因为大量的元素选择器是很慢的。

::: details 详细说明
为了给样式设置作用域，Vue 会为元素添加一个独一无二的 attribute，例如 `data-v-f3f3eg9`。然后修改选择器，使得在匹配选择器的元素中，只有带这个 attribute 才会真正生效（比如 `button[data-v-f3f3eg9]`）。

问题在于大量的元素和 attribute 组合的选择器（比如 `button[data-v-f3f3eg9]`）会比类和 attribute 组合的选择器慢，所以应该尽可能选用类选择器。
:::

<div class="style-example style-example-bad">
<h3>不好</h3>

```vue-html
<template>
  <button>×</button>
</template>

<style scoped>
button {
  background-color: red;
}
</style>
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```vue-html
<template>
  <button class="btn btn-close">×</button>
</template>

<style scoped>
.btn-close {
  background-color: red;
}
</style>
```

</div>

## 隐性的父子组件通信 {#implicit-parent-child-communication}

**应该优先通过 prop 和事件进行父子组件之间的通信，而不是 `this.$parent` 或变更 prop。**

一个理想的 Vue 应用是 prop 向下传递，事件向上传递的。遵循这一约定会让你的组件更易于理解。然而，在一些边界情况下 prop 的变更或 `this.$parent` 能够简化两个深度耦合的组件。

问题在于，这种做法在很多*简单*的场景下可能会更方便。但请当心，不要为了一时方便（少写代码）而牺牲数据流向的简明性（易于理解）。

<div class="options-api">

<div class="style-example style-example-bad">
<h3>不好</h3>

```js
app.component("TodoItem", {
  props: {
    todo: {
      type: Object,
      required: true,
    },
  },

  template: '<input v-model="todo.text">',
})
```

```js
app.component("TodoItem", {
  props: {
    todo: {
      type: Object,
      required: true,
    },
  },

  methods: {
    removeTodo() {
      this.$parent.todos = this.$parent.todos.filter((todo) => todo.id !== vm.todo.id)
    },
  },

  template: `
    <span>
      {{ todo.text }}
      <button @click="removeTodo">
        ×
      </button>
    </span>
  `,
})
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```js
app.component("TodoItem", {
  props: {
    todo: {
      type: Object,
      required: true,
    },
  },

  emits: ["input"],

  template: `
    <input
      :value="todo.text"
      @input="$emit('input', $event.target.value)"
    >
  `,
})
```

```js
app.component("TodoItem", {
  props: {
    todo: {
      type: Object,
      required: true,
    },
  },

  emits: ["delete"],

  template: `
    <span>
      {{ todo.text }}
      <button @click="$emit('delete')">
        ×
      </button>
    </span>
  `,
})
```

</div>

</div>

<div class="composition-api">

<div class="style-example style-example-bad">
<h3>不好</h3>

```vue
<script setup>
defineProps({
  todo: {
    type: Object,
    required: true,
  },
})
</script>

<template>
  <input v-model="todo.text" />
</template>
```

```vue
<script setup>
import { getCurrentInstance } from "vue"

const props = defineProps({
  todo: {
    type: Object,
    required: true,
  },
})

const instance = getCurrentInstance()

function removeTodo() {
  const parent = instance.parent
  if (!parent) return

  parent.props.todos = parent.props.todos.filter((todo) => {
    return todo.id !== props.todo.id
  })
}
</script>

<template>
  <span>
    {{ todo.text }}
    <button @click="removeTodo">×</button>
  </span>
</template>
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```vue
<script setup>
defineProps({
  todo: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(["input"])
</script>

<template>
  <input :value="todo.text" @input="emit('input', $event.target.value)" />
</template>
```

```vue
<script setup>
defineProps({
  todo: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(["delete"])
</script>

<template>
  <span>
    {{ todo.text }}
    <button @click="emit('delete')">×</button>
  </span>
</template>
```

</div>

</div>
