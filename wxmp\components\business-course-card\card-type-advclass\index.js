const APP = getApp()
const ROUTER = require("@/services/mpRouter")
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    imgurl: {
      type: String,
      value:
        "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbcxyzx/images/default/default_information.png",
    },
    item: {
      type: Object,
      value: null,
    },
    // 当前是否是在首页展示
    isInHome: {
      type: Boolean,
      value: false,
    },
    isSearch: {
      type: Boolean,
      value: false,
    },
    collectionHeight: {
      type: String,
      value: "",
    },
  },
  options: {},

  /**
   * 组件的初始数据
   */
  data: {
    IMAGE_PREFIX: APP.globalData.CONFIG.IMAGE_PREFIX,
    textArr: [],
  },
  lifetimes: {
    attached() {
      if (this.data.isSearch && this.data.item) {
        const textArr = this.parseHtmlToTextArr(this.data.item.name)

        // 更新数据
        this.setData({
          textArr,
        })
      }
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    goDetail(e) {
      const data = e.currentTarget.dataset.item
      ROUTER.navigateTo({
        path: "/package-goods/advclass/advclass-detail/index",
        query: {
          no: data.project_no,
        },
      })
    },
    parseHtmlToTextArr(html) {
      const textArr = []
      let remaining = html

      while (remaining.length > 0) {
        const startTagMatch = remaining.match(/<text class="([^"]+)">/)
        if (startTagMatch) {
          // 提取开始标签前的普通文本
          const beforeText = remaining.slice(0, startTagMatch.index)
          if (beforeText) {
            textArr.push({ text: beforeText, isRedText: false })
          }

          // 提取 <text class="red-text"> 内部的内容
          const endTagMatch = remaining.match(/<\/text>/)
          if (endTagMatch) {
            const innerText = remaining.slice(
              startTagMatch.index + startTagMatch[0].length,
              endTagMatch.index
            )
            textArr.push({ text: innerText, isRedText: true })

            // 剩余部分从 </text> 后继续处理
            remaining = remaining.slice(
              endTagMatch.index + endTagMatch[0].length
            )
          } else {
            throw new Error("Missing closing </text> tag")
          }
        } else {
          // 没有更多 <text> 标签，直接将剩余内容作为普通文本
          textArr.push({ text: remaining, isRedText: false })
          remaining = ""
        }
      }

      return textArr
    },
  },
})
