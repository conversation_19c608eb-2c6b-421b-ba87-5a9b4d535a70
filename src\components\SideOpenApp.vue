<template>
  <div v-if="visible" class="side-open-app" :class="positionClass" :style="customStyle">
    <OpenApp :options="options">
      <div class="button" :style="{'color': brandColor}">{{ text }}</div>
    </OpenApp>
  </div>
</template>

<script setup>
import { computed } from "vue"
import OpenApp from "@/components/OpenApp.vue"
import { useBrandStore } from "@/stores/brand.js"

// Props定义
const props = defineProps({
  // 显示状态
  visible: {
    type: Boolean,
    default: true,
  },
  // 位置：bottom-right, bottom-left, bottom-center, top-right, top-left, top-center
  position: {
    type: String,
    default: "bottom-right",
  },
  // 按钮文字
  text: {
    type: String,
    default: "APP内打开",
  },
  // 自定义样式
  customStyle: {
    type: Object,
    default: () => ({}),
  },
  options: {
    type: Object,
    default: () => ({}),
  },
})

// Emits定义
const emit = defineEmits(["click"])

// 计算位置类名
const positionClass = computed(() => `position-${props.position}`)

const brandStore = useBrandStore();
const brandColor = computed(() => {
  return brandStore.color
})

const goApp = () => {}
</script>

<style lang="scss" scoped>
.side-open-app {
  position: fixed;
  right: 0;
  bottom: 2.6rem;
  z-index: 1000;
  @media (min-width: 750px) {
    right: calc(50% - 375px);
  }
}

.button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 2.4rem;
  height: 0.88rem;
  background: #ffffff;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 100px 0px 0px 100px;
  font-size: 0.32rem;
  color: #e60003;
  font-weight: bold;
}
</style>
