<template>
  <div v-if="isLaunchApp" class="wx-open-app-wrapper">
    <div style="position: relative" class="wx-open-app-btn" :id="containerId">
      <slot />
    </div>
  </div>
  <div v-else @click="clickButton">
    <slot />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue"
import { useRoute } from "vue-router"
import { isWXBrowser, isWxWorkBrowser } from "@/utils/browser"
import { useBrandStore } from "@/stores/brand"
import { useWxSDKStore } from "@/stores/wxSDK"
import openSchema from "@/utils/native_schema"
// 生成随机 ID
const generateRandomId = () => {
  return "launch-btn-container-" + Math.random().toString(36).substring(2, 11)
}

// Props 定义
const props = defineProps({
  options: {
    type: Object,
    default: () => ({}),
  },
  appendParam: {
    type: Object,
    default: null,
  }, //安卓 额外参数
})

// 响应式数据
const protocal = ref("") //指定协议头
const failUrl = ref("") //失败回调
const containerId = ref(generateRandomId()) // 随机生成的容器 ID
const isWxOpenLaunchAppCreated = ref(false) // 标记是否已创建微信开放标签

// 使用 stores 和 router
const brandStore = useBrandStore()
const wxSDKStore = useWxSDKStore()
const route = useRoute()
// 计算属性
const isLaunchApp = computed(() => {
  const brandKey = brandStore.brandKey

  return (
    isWXBrowser() &&
    (brandKey === "skb" ||
      brandKey === "jbcgk" ||
      brandKey === "gpjs" ||
      brandKey === "xuandiao" ||
      brandKey === "jbcshangan") &&
    !isWxWorkBrowser() &&
    wxSDKStore.isInitSuccess
  )
})

const currentAPPSchemaParams = computed(() => {
  return JSON.stringify(getAPPSchemaParams(props.options, props.appendParam))
})

const currentAppid = computed(() => {
  return brandStore.brand.appAppid || "wx03b6971146b301a4" // 默认使用事考帮的 appid
})
// 唤醒APP
// openAPPSchema(schema) {
//   let protocal = this.$brandConfig.getCurrentBrandSchemaParam.protocal
//   let failUrl = this.$brandConfig.getCurrentBrandSchemaParam.failUrl

//   this.$openSchema({
//     protocal: protocal,
//     schema: 'openUnit/schema?params=' + JSON.stringify(schema),
//     failUrl: `https://a.app.qq.com/o/simple.jsp?pkgname=${failUrl}`,
//   })
// },
// 方法定义
const openAPPSchema = (schema) => {
  // 注意：这里需要实现 openSchema 方法，因为原来的 this.$openSchema 不存在
  // 可能需要根据实际情况实现 schema 跳转逻辑
  const schemaUrl = `${protocal.value}://openUnit/schema?params=${JSON.stringify(schema)}`
  const failUrlFull = `https://a.app.qq.com/o/simple.jsp?pkgname=${failUrl.value}`

  console.log("尝试打开 Schema:", schemaUrl)
  console.log("失败回调 URL:", failUrlFull)
  openSchema({
    protocal: protocal.value,
    schema: "openUnit/schema?params=" + JSON.stringify(schema),
    failUrl: failUrlFull,
  })
  // // 尝试打开 schema
  // window.location.href = schemaUrl

  // // 设置超时回调到应用商店
  // setTimeout(() => {
  //   window.location.href = failUrlFull
  // }, 2000)
}

const getAPPSchemaParams = (options, appendParam) => {
  console.log("getAPPSchemaParams 输入参数:", { options, appendParam })

  const brandSchemaParam = brandStore.getCurrentBrandSchemaParam()
  const brand = brandSchemaParam?.protocal || ""
  const province = route.query.province || ""
  const appkey = brand + "." + province

  // 基础参数
  const baseParam = { brand, province }
  if (brand && province) {
    baseParam.appkey = appkey
  }

  // 从 options 中提取 unitKey 和其他参数
  const unitKey = options?.unitKey || ""
  let finalParam = { ...baseParam }

  // 处理参数合并
  if (options && typeof options === "object") {
    if (options.param && typeof options.param === "object") {
      // 如果有 param 结构，合并 options.param
      Object.assign(finalParam, options.param)
    } else {
      // 否则直接合并 options（排除 unitKey）
      const { unitKey: _, ...otherOptions } = options
      Object.assign(finalParam, otherOptions)
    }
  }

  // 获取推荐码并添加到 param 中
  // const recommendCode = getRecommendCode()
  // if (Object.keys(recommendCode).length > 0) {
  //   finalParam.recommend_code = recommendCode
  // }

  // 返回包含 option 嵌套的结构
  const schema = {
    type: "openUnit",
    option: {
      unitKey: unitKey,
      param: finalParam,
    },
  }

  // appendParam 作为额外的顶级参数
  if (appendParam) {
    Object.assign(schema, appendParam)
  }

  console.log("getAPPSchemaParams 输出结果:", schema)
  return schema
}

// 执行打开app方法
const openApp = (options, appendParam) => {
  openAPPSchema(getAPPSchemaParams(options, appendParam))
}

// 点击按钮
const clickButton = () => {
  openApp(props.options, props.appendParam)
}

// 创建微信开放标签的函数
const createWxOpenLaunchApp = () => {
  const container = document.getElementById(containerId.value)
  if (!container) return

  // 检查当前容器中是否已存在 wx-open-launch-app 元素（基于容器实例防重复）
  const existingElement = container.querySelector("wx-open-launch-app")
  if (existingElement) {
    console.log(`容器 ${containerId.value} 中已存在 wx-open-launch-app 元素，跳过创建`)
    isWxOpenLaunchAppCreated.value = true
    return
  }

  // 创建 wx-open-launch-app 元素
  const wxOpenLaunchApp = document.createElement("wx-open-launch-app")
  wxOpenLaunchApp.id = `launch-btn-${containerId.value}` // 使用容器ID确保唯一性
  wxOpenLaunchApp.setAttribute("appid", currentAppid.value)
  wxOpenLaunchApp.setAttribute("extinfo", currentAPPSchemaParams.value)

  // 创建内部的 script 标签
  const scriptTag = document.createElement("script")
  scriptTag.type = "text/wxtag-template"
  scriptTag.innerHTML = `
    <style>
      .template {
        -webkit-tap-highlight-color: transparent !important;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
        background: transparent;
        border: none;
        outline: none;
      }
    </style>
    <div class="template"></div>
  `

  wxOpenLaunchApp.appendChild(scriptTag)
  container.appendChild(wxOpenLaunchApp)

  // 添加事件监听器
  try {
    wxOpenLaunchApp.addEventListener("ready", function (e) {
      console.log(e, "wx-open-launch-app ready")
    })
    wxOpenLaunchApp.addEventListener("error", function (e) {
      console.log(e, "wx-open-launch-app error")
    })
  } catch (error) {
    console.error("添加事件监听器失败:", error)
  }

  // 标记已创建
  isWxOpenLaunchAppCreated.value = true
  console.log(`容器 ${containerId.value} 的 wx-open-launch-app 创建完成`)
}

// 生命周期
onMounted(() => {
  const brandSchemaParam = brandStore.getCurrentBrandSchemaParam()
  protocal.value = brandSchemaParam?.protocal || ""
  failUrl.value = brandSchemaParam?.failUrl || ""
  console.log(currentAPPSchemaParams.value)
})

// 监听 isLaunchApp 变化，只有为 true 时才创建微信开放标签
watch(
  isLaunchApp,
  (newValue) => {
    if (newValue) {
      console.log("jssdk初始化成功")

      nextTick(() => {
        createWxOpenLaunchApp()
      })
    }
  },
  { immediate: true } // 立即执行一次，检查初始状态
)

// 组件销毁时清理
onUnmounted(() => {
  // 重置创建标记，以便组件重新挂载时可以重新创建
  isWxOpenLaunchAppCreated.value = false
})
</script>

<style lang="scss">
.wx-open-app-btn {
  // 确保内容区域保持原有布局
  display: inherit;
  flex-direction: inherit;
  align-items: inherit;
  justify-content: inherit;
  width: 100%;
  height: 100%;
}

wx-open-launch-app {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 20;
  overflow: hidden;
  // 确保不影响父级布局
  pointer-events: auto;
}
</style>
