/**
 * 地区选择工具函数
 * 提供通用的地区选择相关工具方法，减少重复代码
 */

const UTIL = require("../utils/util")
const API = require("../config/api")
const {
  getSelectedRegionsCache,
  setSelectedRegionsCache,
} = require("../utils/cache/regionCache")
const {
  formatProvinceData,
  formatCityData,
  formatDistrictData,
} = require("../utils/regionUtils")

/**
 * 地区选择工具类
 */
class RegionSelectUtils {
  /**
   * 加载省份列表
   * @param {Object} context 页面或组件的 this 上下文
   * @param {string} dataKey 数据键名，如 'regionPopupData', 'newsRegionPopupData'
   */
  static async loadProvinceList(context, dataKey = "regionPopupData") {
    context.setData({
      [`${dataKey}.loading.province`]: true,
    })

    try {
      // 使用通用地区 API，level=1 表示获取省份
      const res = await UTIL.request(API.getRegionChildList, {
        level: 1,
        parent_id: 0,
        top_id: 0,
      })
      if (res && res.error && res.error.code === 0 && res.data) {
        // 使用格式化函数处理数据
        const formattedProvinceList = formatProvinceData(res.data)
        context.setData({
          [`${dataKey}.provinceList`]: formattedProvinceList,
          [`${dataKey}.loading.province`]: false,
        })
        console.log(
          `省份列表加载成功，数据键：${dataKey}`,
          formattedProvinceList
        )
      } else {
        console.error(`省份列表加载失败，数据键：${dataKey}`, res)
        context.setData({
          [`${dataKey}.loading.province`]: false,
        })
      }
    } catch (error) {
      console.error(`省份列表请求异常，数据键：${dataKey}`, error)
      context.setData({
        [`${dataKey}.loading.province`]: false,
      })
    }
  }

  /**
   * 加载城市列表
   * @param {Object} context 页面或组件的 this 上下文
   * @param {number} provinceId 省份ID
   * @param {string} dataKey 数据键名
   */
  static async loadCityList(context, provinceId, dataKey = "regionPopupData") {
    context.setData({
      [`${dataKey}.loading.city`]: true,
      [`${dataKey}.cityList`]: [],
      [`${dataKey}.districtList`]: [],
    })

    try {
      // 使用通用地区 API，level=2 表示获取城市
      const res = await UTIL.request(API.getRegionChildList, {
        level: 2,
        parent_id: provinceId,
        top_id: provinceId,
      })
      if (res && res.error && res.error.code === 0 && res.data) {
        // 使用格式化函数处理数据
        const formattedCityList = formatCityData(res.data)
        context.setData({
          [`${dataKey}.cityList`]: formattedCityList,
          [`${dataKey}.loading.city`]: false,
        })
        console.log(`城市列表加载成功，数据键：${dataKey}`, formattedCityList)
      } else {
        console.error(`城市列表加载失败，数据键：${dataKey}`, res)
        context.setData({
          [`${dataKey}.loading.city`]: false,
        })
      }
    } catch (error) {
      console.error(`城市列表请求异常，数据键：${dataKey}`, error)
      context.setData({
        [`${dataKey}.loading.city`]: false,
      })
    }
  }

  /**
   * 加载区县列表
   * @param {Object} context 页面或组件的 this 上下文
   * @param {number} cityId 城市ID
   * @param {number} provinceId 省份ID（作为 top_id）
   * @param {string} dataKey 数据键名
   */
  static async loadDistrictList(
    context,
    cityId,
    provinceId,
    dataKey = "regionPopupData"
  ) {
    context.setData({
      [`${dataKey}.loading.district`]: true,
      [`${dataKey}.districtList`]: [],
    })

    try {
      // 使用通用地区 API，level=3 表示获取区县
      const res = await UTIL.request(API.getRegionChildList, {
        level: 3,
        parent_id: cityId,
        top_id: provinceId,
      })
      if (res && res.error && res.error.code === 0 && res.data) {
        // 使用格式化函数处理数据
        const formattedDistrictList = formatDistrictData(res.data)
        context.setData({
          [`${dataKey}.districtList`]: formattedDistrictList,
          [`${dataKey}.loading.district`]: false,
        })
        console.log(
          `区县列表加载成功，数据键：${dataKey}`,
          formattedDistrictList
        )
      } else {
        console.error(`区县列表加载失败，数据键：${dataKey}`, res)
        context.setData({
          [`${dataKey}.loading.district`]: false,
        })
      }
    } catch (error) {
      console.error(`区县列表请求异常，数据键：${dataKey}`, error)
      context.setData({
        [`${dataKey}.loading.district`]: false,
      })
    }
  }

  /**
   * 初始化地区数据
   * @param {Object} context 页面或组件的 this 上下文
   * @param {string} cacheKey 缓存键名，如 'announcement', 'news'
   * @param {string} dataKey 数据键名，如 'regionPopupData', 'newsRegionPopupData'
   */
  static initRegionData(
    context,
    cacheKey = "announcement",
    dataKey = "regionPopupData"
  ) {
    console.log(`初始化地区数据，缓存键：${cacheKey}，数据键：${dataKey}`)

    // 从缓存加载已选择的地区
    const cachedRegions = getSelectedRegionsCache(cacheKey) || []

    context.setData({
      [`${dataKey}.selectedRegions`]: cachedRegions,
    })

    // 加载省份列表
    this.loadProvinceList(context, dataKey)
  }

  /**
   * 获取地区选择的API参数
   * @param {Array} selectedRegions 选中的地区列表
   * @returns {Array} API参数格式的地区ID数组
   */
  static getRegionApiParams(selectedRegions) {
    if (!selectedRegions || selectedRegions.length === 0) {
      return []
    }

    return selectedRegions.map((region) => region.districtId)
  }

  /**
   * 更新选中状态到对应的状态对象
   * @param {Object} context 页面或组件的 this 上下文
   * @param {Array} selectedRegions 选中的地区列表
   * @param {string} stateKey 状态对象键名，如 'noticeSelectForTemplate', 'examSelectForTemplate'
   * @param {string} filterKey 筛选键名，如 'apply_region'
   */
  static updateRegionSelectState(
    context,
    selectedRegions,
    stateKey = "noticeSelectForTemplate",
    filterKey = "apply_region"
  ) {
    const currentState = context.data[stateKey] || {}
    const regionIds = this.getRegionApiParams(selectedRegions)

    const updatedState = {
      ...currentState,
      [filterKey]: regionIds,
    }

    context.setData({
      [stateKey]: updatedState,
    })

    console.log(
      `地区选择状态已更新，状态键：${stateKey}，筛选键：${filterKey}`,
      regionIds
    )
  }

  /**
   * 保存地区选择到缓存
   * @param {Array} selectedRegions 选中的地区列表
   * @param {string} cacheKey 缓存键名
   */
  static saveRegionToCache(selectedRegions, cacheKey) {
    setSelectedRegionsCache(selectedRegions, cacheKey)
    console.log(`地区选择已保存到缓存，缓存键：${cacheKey}`, selectedRegions)
  }

  /**
   * 从缓存加载地区选择
   * @param {string} cacheKey 缓存键名
   * @returns {Array} 选中的地区列表
   */
  static loadRegionFromCache(cacheKey) {
    const cachedRegions = getSelectedRegionsCache(cacheKey) || []
    console.log(`从缓存加载地区选择，缓存键：${cacheKey}`, cachedRegions)
    return cachedRegions
  }

  /**
   * 创建地区对象
   * @param {Object} province 省份对象
   * @param {Object} city 城市对象
   * @param {Object} district 区县对象
   * @returns {Object} 地区对象
   */
  static createRegionObject(province, city, district) {
    return {
      id: Date.now().toString(), // 组件需要的唯一标识
      name: district.name, // 组件用于显示的名称
      type: "district", // 地区类型
      provinceId: province.code || province.id,
      provinceName: province.name,
      cityId: city.code || city.id,
      cityName: city.name,
      districtId: district.code || district.id,
      districtName: district.name,
      displayName: `${province.name}-${city.name}-${district.name}`,
    }
  }

  /**
   * 检查地区是否已选择
   * @param {Array} selectedRegions 已选择的地区列表
   * @param {number} districtId 区县ID
   * @returns {boolean} 是否已选择
   */
  static isRegionSelected(selectedRegions, districtId) {
    return selectedRegions.some((region) => region.districtId === districtId)
  }

  /**
   * 添加地区到选择列表
   * @param {Array} selectedRegions 已选择的地区列表
   * @param {Object} regionObject 要添加的地区对象
   * @returns {Array} 更新后的地区列表
   */
  static addRegionToSelection(selectedRegions, regionObject) {
    // 检查是否已经选择
    if (this.isRegionSelected(selectedRegions, regionObject.districtId)) {
      console.log("该区县已经选择，忽略重复选择")
      return selectedRegions
    }

    return [...selectedRegions, regionObject]
  }

  /**
   * 从选择列表中移除地区
   * @param {Array} selectedRegions 已选择的地区列表
   * @param {number} districtId 要移除的区县ID
   * @returns {Array} 更新后的地区列表
   */
  static removeRegionFromSelection(selectedRegions, districtId) {
    return selectedRegions.filter((region) => region.districtId !== districtId)
  }
}

module.exports = RegionSelectUtils
