/**
 * 移动端适配工具函数
 * 基于设计稿宽度375px，1rem = 37.5px的适配方案
 */

// 设计稿宽度
const DESIGN_WIDTH = 750
// 基准字体大小
const BASE_FONT_SIZE = 75

/**
 * 设置根字体大小
 * @param {number} designWidth 设计稿宽度，默认375px
 * @param {number} baseFontSize 基准字体大小，默认37.5px
 */
export function setRootFontSize(designWidth = DESIGN_WIDTH, baseFontSize = BASE_FONT_SIZE) {
  const html = document.documentElement
  const clientWidth = html.clientWidth || window.innerWidth

  // 计算当前设备的字体大小
  const fontSize = (clientWidth / designWidth) * baseFontSize

  // 限制最小和最大字体大小
  const minFontSize = 20 // 最小字体大小
  const maxFontSize = 60 // 最大字体大小

  const finalFontSize = Math.max(minFontSize, Math.min(maxFontSize, fontSize))

  html.style.fontSize = finalFontSize + "px"

  // 设置自定义属性，方便CSS中使用
  html.style.setProperty("--root-font-size", finalFontSize + "px")
  html.style.setProperty("--design-width", designWidth + "px")
  html.style.setProperty("--client-width", clientWidth + "px")

  console.log(`移动端适配: 设备宽度=${clientWidth}px, 根字体大小=${finalFontSize}px`)
}

/**
 * px转rem
 * @param {number} px 像素值
 * @param {number} baseFontSize 基准字体大小，默认37.5px
 * @returns {string} rem值
 */
export function pxToRem(px, baseFontSize = BASE_FONT_SIZE) {
  return (px / baseFontSize).toFixed(4) + "rem"
}

/**
 * rem转px
 * @param {number} rem rem值
 * @param {number} baseFontSize 基准字体大小，默认37.5px
 * @returns {number} 像素值
 */
export function remToPx(rem, baseFontSize = BASE_FONT_SIZE) {
  return rem * baseFontSize
}

/**
 * 获取当前根字体大小
 * @returns {number} 当前根字体大小
 */
export function getCurrentRootFontSize() {
  const html = document.documentElement
  const fontSize = window.getComputedStyle(html).fontSize
  return parseFloat(fontSize)
}

/**
 * 初始化移动端适配
 * @param {Object} options 配置选项
 * @param {number} options.designWidth 设计稿宽度
 * @param {number} options.baseFontSize 基准字体大小
 * @param {boolean} options.enableResize 是否监听窗口大小变化
 */
export function initFlexible(options = {}) {
  const { designWidth = DESIGN_WIDTH, baseFontSize = BASE_FONT_SIZE, enableResize = true } = options

  // 设置初始字体大小
  setRootFontSize(designWidth, baseFontSize)

  // 监听窗口大小变化
  if (enableResize) {
    let resizeTimer = null

    const handleResize = () => {
      clearTimeout(resizeTimer)
      resizeTimer = setTimeout(() => {
        setRootFontSize(designWidth, baseFontSize)
      }, 100)
    }

    window.addEventListener("resize", handleResize)
    window.addEventListener("orientationchange", handleResize)

    // 返回清理函数
    return () => {
      window.removeEventListener("resize", handleResize)
      window.removeEventListener("orientationchange", handleResize)
    }
  }
}

/**
 * 获取设备信息
 * @returns {Object} 设备信息
 */
export function getDeviceInfo() {
  const html = document.documentElement
  const clientWidth = html.clientWidth || window.innerWidth
  const clientHeight = html.clientHeight || window.innerHeight
  const devicePixelRatio = window.devicePixelRatio || 1
  const currentFontSize = getCurrentRootFontSize()

  return {
    clientWidth,
    clientHeight,
    devicePixelRatio,
    currentFontSize,
    isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent),
    isAndroid: /Android/.test(navigator.userAgent),
    isMobile: /Mobi|Android/i.test(navigator.userAgent),
    isTablet: clientWidth >= 768 && clientWidth < 1024,
    isDesktop: clientWidth >= 1024,
  }
}

/**
 * 设置视口meta标签
 */
export function setViewportMeta() {
  let viewport = document.querySelector('meta[name="viewport"]')

  if (!viewport) {
    viewport = document.createElement("meta")
    viewport.name = "viewport"
    document.head.appendChild(viewport)
  }

  // 设置视口配置
  viewport.content = [
    "width=device-width",
    "initial-scale=1.0",
    "maximum-scale=1.0",
    "minimum-scale=1.0",
    "user-scalable=no",
    "viewport-fit=cover",
  ].join(", ")
}

// 自动初始化（如果不使用amfe-flexible）
if (typeof window !== "undefined" && !window.lib) {
  // 设置视口
  setViewportMeta()

  // 初始化适配
  initFlexible()
}
