.semi-circle-progress {
  .progress-container {
    position: relative;
    width: 212rpx;
    height: 106rpx;
    overflow: hidden;
    /* We need a container for rotation to not affect layout */
    display: flex;
    align-items: center;
    justify-content: center;

    .progress-ring {
      position: absolute;
      top: 0;
      left: 0;
      width: 212rpx;
      height: 212rpx;
      border-radius: 50%;
      /* Rotate the entire drawing canvas so that 12 o'clock becomes 9 o'clock */
      transform: rotate(-90deg);
      /* 
        The conic-gradient creates the progress effect.
        - from 180deg: Starts at 9 o'clock.
        - #FF6A4D: Fills from 0 to the specified progress percentage.
        - rgba(0, 0, 0, 0.1): Fills the rest of the semi-circle up to 50%.
        - transparent: The bottom half is transparent.
        - calc(var(--progress) * 0.5%): We divide by 2 because the gradient applies to the full 360 degrees,
          but we only want to represent progress over 180 degrees (50% of the circle).
      */
      background: conic-gradient(
        from 180deg,
        #ff6a4d 0deg,
        #ff6a4d var(--progress-angle),
        rgba(0, 0, 0, 0.1) var(--progress-angle),
        rgba(0, 0, 0, 0.1) 360deg
      );
    }

    .progress-hole {
      position: absolute;
      top: 32rpx;
      left: 32rpx;
      width: calc(212rpx - 64rpx);
      height: calc(212rpx - 64rpx);
      background: #fff;
      border-radius: 50%;
    }

    .text-overlay {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);

      .percentage-area {
        display: flex;
        align-items: flex-end;
        .num {
          font-size: 48rpx;
          font-weight: bold;
          color: #3c3d42;
          font-family: "DINBold";
        }
        .percent {
          font-size: 24rpx;
          font-weight: bold;
          color: #3c3d42;
          font-family: "DINBold";
          margin-bottom: 10rpx;
        }
      }
    }
  }
}
