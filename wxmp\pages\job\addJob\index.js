const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    IMAGE_PREFIX: APP.globalData.CONFIG.IMAGE_PREFIX,
    jobList: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getList()
  },

  async getList() {
    const param = {
      item_type: "job",
      page: 1,
      size: 999,
    }
    const res = await UTIL.request(API.getFollowsList, param)
    let arr = []
    if (res) {
      arr = res?.data || []
      const newArr = arr.flatMap((item) => item.list || [])

      // 处理禁用状态并设置到页面数据
      const processedJobList = this.initDisabledJobs(newArr)
      this.setData({
        jobList: processedJobList,
      })
    }
  },

  /**
   * 初始化禁用的职位（从上一页面获取已选中的职位）
   */
  initDisabledJobs(arr) {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2]
      const prevJobList = prevPage.data.jobList || []

      if (prevJobList.length > 0) {
        // 创建一个映射表，存储上一页面已选中的职位ID
        const prevJobIds = new Set(prevJobList.map((job) => job.id))

        // 处理当前页面的职位列表：如果在上一页面中存在且选中，设置为禁用且选中
        const processedJobList = arr.map((job) => {
          if (prevJobIds.has(job.id)) {
            return { ...job, isDisabled: true, isChecked: true }
          }
          return { ...job, isDisabled: false, isChecked: false }
        })

        return processedJobList
      }
    }

    // 如果没有上一页面的数据，返回默认状态
    return arr.map((job) => ({ ...job, isDisabled: false, isChecked: false }))
  },

  /**
   * 子组件选择事件
   */
  onCardSelect(e) {
    const { id, isChecked } = e.detail
    const { jobList } = this.data

    const newJobList = jobList.map((item) => {
      if (item.id === id && !item.isDisabled) {
        return { ...item, isChecked }
      }
      return item
    })

    this.setData({
      jobList: newJobList,
    })
  },

  /**
   * 添加职位到对比页面
   */
  onAddJobs() {
    // 只选择可选择且选中的职位（排除禁用状态的职位）
    const selectedJobs = this.data.jobList.filter(
      (item) => item.isChecked && !item.isDisabled
    )

    // 获取当前缓存的pkListIds
    const existingPkListIds = wx.getStorageSync("pkListIds") || []

    // 将selectedJobs转换为id集合
    const selectedJobIds = selectedJobs.map((job) => job.id)

    // 过滤出缓存中没有的新的ids
    const newIds = selectedJobIds.filter(
      (id) => !existingPkListIds.includes(id)
    )

    // 将新的ids添加到缓存中
    const updatedPkListIds = [...existingPkListIds, ...newIds]
    wx.setStorageSync("pkListIds", updatedPkListIds)

    if (selectedJobs.length > 0)
      wx.showToast({
        title: `已添加${newIds.length}个职位`,
        icon: "none",
      })

    // 延迟返回上一页
    setTimeout(() => {
      wx.navigateBack()
    }, 1000)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
})
