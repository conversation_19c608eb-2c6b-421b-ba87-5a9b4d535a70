.course-card-title {
  font-size: 30rpx;
  font-weight: bold;
  line-height: 1.5;
  color: #424147;
  .label-tip {
    display: inline-block;
    background: rgba(255, 243, 240, 1);
    font-size: 22rpx;
    color: #f03916;
    position: relative;
    padding: 5rpx 18rpx 5rpx 48rpx;
    border-radius: 18rpx;
    margin-right: 10rpx;
    transform: translateY(-5rpx);
    line-height: 1.5;
    .image {
      position: absolute;
      width: 40rpx;
      height: 40rpx;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
.course-card-label-text {
  font-size: 24rpx;
  color: #919499;
  margin-top: 15rpx;
}

.text-ellipsis-1 {
  overflow: hidden;
  /*隐藏多出部分文字*/
  text-overflow: ellipsis;
  /*用省略号代替多出部分文字*/
  display: -webkit-box;
  /* 显示多行文本容器 */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  /*显示行数*/
}

.text-ellipsis-2 {
  overflow: hidden;
  /*隐藏多出部分文字*/
  text-overflow: ellipsis;
  /*用省略号代替多出部分文字*/
  display: -webkit-box;
  /* 显示多行文本容器 */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.red-text {
  color: #ff6a4d;
}
