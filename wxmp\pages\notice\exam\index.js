const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Page({
  data: {
    show_white: false,
    page: 1,
    // 分页相关状态
    hasMore: true, // 是否还有更多数据
    isLoading: false, // 是否正在加载
    articleList: [], // 文章列表
    isPageLoadComplete: false, // 页面是否加载完成
  },
  async onLoad(options) {
    this.getChoiceList()
  },
  onShow() {
    // 第一次onshow 不请求
    if (!this.data.isPageLoadComplete) {
      return
    }
    this.setData({
      page: 1,
    })
    this.getChoiceList()
    // 初始化胶囊按钮颜色
    if (this.data.show_white) {
      // 白色背景时，胶囊按钮显示黑色
      wx.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff",
      })
    } else {
      // 深色背景时，胶囊按钮显示白色
      wx.setNavigationBarColor({
        frontColor: "#ffffff",
        backgroundColor: "#000000",
      })
    }
  },
  async getChoiceList(isLoadMore = false) {
    // 如果正在加载或没有更多数据，直接返回
    if (
      this.data.isLoading ||
      (!isLoadMore && !this.data.hasMore && this.data.page > 1)
    ) {
      return
    }

    const params = {
      page: this.data.page,
    }

    this.setData({
      isLoading: true,
    })

    try {
      const res = await UTIL.request(API.getChoiceList, params)
      if (res && res.error && res.error.code === 0 && res.data) {
        const newList = res?.data?.list || []

        // 根据是否为加载更多来决定如何处理数据
        let updatedArticleList
        if (isLoadMore) {
          // 分页加载：追加到现有数据
          updatedArticleList = [...this.data.articleList, ...newList]
        } else {
          // 首次加载或筛选：直接使用新数据
          updatedArticleList = newList
        }

        this.setData({
          articleList: updatedArticleList,
          hasMore: newList.length > 0, // 如果返回的数据为空，说明没有更多数据
          isLoading: false,
          isPageLoadComplete: true,
        })
      } else {
        this.setData({
          isLoading: false,
          hasMore: false,
        })
      }
    } catch (error) {
      console.error("获取文章列表失败:", error)
      this.setData({
        isLoading: false,
      })
    }
  },
  onReachBottom() {
    console.log("触底了")

    // 检查是否还有更多数据且当前不在加载中
    if (this.data.hasMore && !this.data.isLoading) {
      // 页码+1
      const nextPage = this.data.page + 1
      this.setData({
        page: nextPage,
      })

      // 加载下一页数据
      this.getChoiceList(true)
      // const apiParams = this.buildApiParams(
      //   this.data.collectionSelectForTemplate
      // )
      // this.getArticleChildList(apiParams, true)
    }
  },
  goDetail(e) {
    const data = e.currentTarget.dataset.item
    const path =
      data.type == "article_list"
        ? "/pages/notice/collection/index"
        : "/pages/notice/detail/index"
    ROUTER.navigateTo({
      path,
      query: {
        id: data.id,
      },
    })
  },
  backPage() {
    if (getCurrentPages().length <= 1) {
      wx.reLaunch({
        url: "/pages/home/<USER>/index",
      })
      console.log("回首页")
      return false
    }
    console.log("触发返回")
    wx.navigateBack({
      delta: 1,
    })
  },
  onPageScroll(e) {
    // 如果页面滚动被禁用，则不处理滚动事件
    if (this.isPageScrollDisabled && this.isPageScrollDisabled()) {
      return
    }

    const scrollTop = e.scrollTop
    if (scrollTop > 0) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        this.setData({ show_white: true })
        // 设置胶囊按钮为黑色（适用于白色背景）
        wx.setNavigationBarColor({
          frontColor: "#000000",
          backgroundColor: "#ffffff",
        })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
        // 设置胶囊按钮为白色（适用于深色背景）
        wx.setNavigationBarColor({
          frontColor: "#ffffff",
          backgroundColor: "#000000",
        })
      }
    }
  },
  onShareAppMessage() {},
})
