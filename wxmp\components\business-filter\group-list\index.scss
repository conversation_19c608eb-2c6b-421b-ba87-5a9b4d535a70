/* 考试选择内容组件样式 */

.popu-content-c {
  padding: 0 32rpx;
  padding-top: 24rpx;
  padding-bottom: 8rpx;
  background: rgba(255, 255, 255, 1);

  .exam-list {
    display: flex;
    flex-wrap: wrap;

    &-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 212rpx;
      height: 72rpx;
      font-size: 26rpx;
      background: rgba(235, 236, 240, 0.6);
      border-radius: 12rpx;
      margin-right: 24rpx;
      margin-bottom: 24rpx;
      cursor: pointer;
      transition: all 0.2s ease;

      &:nth-child(3n) {
        margin-right: 0;
      }

      &.active {
        color: rgba(230, 0, 3, 1);
        background-color: rgba(230, 0, 3, 0.05);
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.group-list {
  .title {
    font-size: 28rpx;
    color: rgba(60, 61, 66, 1);
    margin-bottom: 32rpx;

    .label-text {
      font-size: 24rpx;
      color: rgba(145, 148, 153, 1);
    }
  }

  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .group-list-item {
    display: flex;
    flex-wrap: wrap;

    .group-item {
      margin-bottom: 24rpx;
      margin-right: 24rpx;
      background-color: rgba(235, 236, 240, 0.6);
      width: 212rpx;
      border-radius: 12rpx;
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26rpx;
      color: rgba(60, 61, 66, 1);

      // border: 1rpx solid rgba(235, 236, 240, 1);
      &:nth-child(3n) {
        margin-right: 0;
      }

      &.active {
        color: rgba(230, 0, 3, 1);
        background-color: rgba(230, 0, 3, 0.05);
        // border-color: rgba(230, 0, 3, 1);
      }
    }
  }
}
