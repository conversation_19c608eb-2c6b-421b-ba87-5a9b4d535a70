<view class="card-item" bindtap="onSelectCard">
  <!-- 禁用状态显示特殊图标 -->
  <image wx:if="{{dataItem.isDisabled}}" class="select-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch//job/comparison/unable_select.png" mode="" />
  <!-- 正常状态显示选中/未选中图标 -->
  <image wx:elif="{{dataItem.isChecked}}" class="select-icon" src="{{IMAGE_PREFIX}}/job/comparison/selected.png" mode="" />
  <image wx:else class="select-icon" src="{{IMAGE_PREFIX}}/job/comparison/select.png" mode="" />
  <view class="right-box">
    <view class="top-area">
      <view class="title">{{dataItem.job_name}}</view>
      <image wx:if="{{!hideDelete}}" class="icon" src="{{IMAGE_PREFIX}}/job/comparison/delete.png" mode="" catch:tap="onDeleteCard" />
    </view>
    <view class="main-area">
      <view class="list-item">
        <view class="title">正在报名</view>
        <view class="text text-ellipsis-1">共招{{dataItem.need_num}}人</view>
      </view>
      <view class="list-item">
        <view class="title">招录单位</view>
        <view class="text text-ellipsis-1">{{dataItem.work_unit}}</view>
      </view>
      <view class="list-item">
        <view class="title">来自公告</view>
        <view class="text text-ellipsis-1">{{dataItem.article_name}}</view>
      </view>
      <view class="list-bottom">
        <view class="text">{{dataItem.region_province_name}}·{{dataItem.exam_type_name}}</view>
        <view class="text">{{dataItem.release_time}}</view>
      </view>
    </view>
  </view>
</view>