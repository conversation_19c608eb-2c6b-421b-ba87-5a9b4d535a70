// components/tabbar/home-tabbar/index.js
import { getBaseCampusCache } from "@/utils/cache/baseCache"
const APP = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    active: {
      type: String,
      value: "home",
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    isLoad: false,
    list: [
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_home.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_home_select.png",
        pagePath: "pages/home/<USER>/index",
        pathKey: "home",
        text: "公告",
      },
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_job.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_job_select.png",
        pagePath: "pages/job/list/index",
        pathKey: "job",
        text: "职位",
      },
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_bigdata.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_bigdata_select.png",
        pagePath: "pages/special/big-data/index",
        pathKey: "big_data",
        text: "报考大数据",
      },
      {
        iconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_my.png",
        selectedIconPath:
          "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/tab_my_select.png",
        pagePath: "pages/my/home/<USER>",
        pathKey: "my",
        text: "我的",
      },
    ],

    menus: [],
    activeCampusId: null,
  },
  pageLifetimes: {
    show() {
      // this.loadMenus()
    },
    ready() {
      this.setData({ menus: this.data.list, isLoad: true })
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    loadMenus: async function () {
      // try {
      //   if (!this.data.isLoad) {
      //     await APP.checkLoadRequest()
      //   }
      // } catch (error) {}

      // const campusId = getBaseCampusCache()?.id || 5
      // if (campusId === this.data.activeCampusId) {
      //   return false
      // }

      // const menusList = APP.globalData.serverConfig?.menus_list || {}
      // const menus = menusList[campusId] || menusList.default || this.data.list
      console.log(menus)
      this.setData({ menus: this.data.list, isLoad: true })
    },
    onChange(event) {
      const { pagePath, pathKey } = event.currentTarget.dataset.item
      console.log(pagePath)
      if (pathKey === "big_data") {
        wx.navigateTo({
          url: "/" + pagePath,
          success: function (res) {},
          fail: function (res) {
            console.log(res)
          },
        })
        return
      }
      wx.switchTab({
        url: "/" + pagePath,
        success: function (res) {},
        fail: function (res) {
          console.log(res)
        },
      })
    },
  },
})
