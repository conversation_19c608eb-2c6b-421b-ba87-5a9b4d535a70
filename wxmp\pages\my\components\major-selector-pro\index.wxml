<van-popup show="{{ show }}" position="bottom" custom-style="height: 90%; border-radius: 24rpx 24rpx 0 0;" z-index="999" bind:close="onClose">
  <view class="major-selector-pro">
    <view class="header">
      <view class="title">2025年教育部专业指导目录</view>
    </view>

    <view class="selected-area" wx:if="{{selectedMajorNames}}">
      <view class="selected-display">
        <view class="left">
          已选：<text class="text">{{ selectedMajorNames }}</text>
        </view>
        <image class="delete-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/selector_delete.png" bind:tap="clearSelection" />
      </view>
    </view>

    <view class="tabs">
      <view wx:for="{{ tabs }}" wx:key="id" class="tab-item {{ activeTab === item.id ? 'active' : '' }}" data-id="{{ item.id }}" bind:tap="onTabChange">
        {{ item.name }}
      </view>
    </view>

    <view class="search-bar">
      <input class="search-input" placeholder="请输入专业名词关键词" value="{{ searchKeyword }}" bindinput="onSearchInput" />
      <image class="search-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_search.png" />
    </view>



    <view class="major-content">
      <!-- 搜索结果 -->
      <view class="search-results" wx:if="{{ isSearching }}">
        <view wx:for="{{ searchResults }}" wx:key="id" class="result-item" bind:tap="onMajorClick" data-id="{{ item.id }}" data-name="{{ item.name }}">
          {{ item.name }}
        </view>
      </view>

      <!-- 专业列表 -->
      <view class="major-list" wx:else>
        <scroll-view scroll-y class="list-panel" show-scrollbar="{{false}}" enhanced>
          <view wx:for="{{ majorCategories }}" wx:key="id" class="panel-item {{ activeCategoryId === item.id ? 'active' : '' }}" data-id="{{ item.id }}" bind:tap="onCategoryClick">
            {{ item.name }}
          </view>
        </scroll-view>

        <scroll-view scroll-y class="list-panel" show-scrollbar="{{false}}" enhanced>
          <view wx:for="{{ majorSecondLevel[activeCategoryId] }}" wx:key="id" class="panel-item {{ activeSecondLevelId === item.id ? 'active' : '' }}" data-id="{{ item.id }}" bind:tap="onSecondLevelClick">
            {{ item.name }}
          </view>
        </scroll-view>

        <scroll-view scroll-y class="list-panel" show-scrollbar="{{false}}" enhanced>
          <view wx:for="{{ majorThirdLevel[activeSecondLevelId] }}" wx:key="id" class="panel-item {{ selectedMajor.id === item.id ? 'active' : '' }}" data-id="{{ item.id }}" data-name="{{ item.name }}" bind:tap="onMajorClick">
            {{ item.name }}
          </view>
        </scroll-view>
      </view>
    </view>

    <view class="bottom-actions">
      <view class="sync-area" bind:tap="toggleSyncToResume">
        <image class="checkbox-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/{{ syncToResume ? 'selector_red' : 'selector_gray' }}.png" />
        <text>同步至我的简历</text>
      </view>
      <view class="action-btn save-btn {{ selectedMajor ? 'active' : '' }}" bind:tap="onSave">保存</view>
    </view>
  </view>
</van-popup>