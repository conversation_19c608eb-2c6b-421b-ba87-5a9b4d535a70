.position-list-item {
  background: #fff;
  padding: 32rpx;
  border-radius: 16rpx;
  position: relative;
  margin-bottom: 24rpx;
  &:last-child {
    margin-bottom: 0;
  }
  .label-img {
    width: 136rpx;
    height: 54rpx;
    position: absolute;
    right: 32rpx;
    top: 0;
  }
  .title-area {
    margin-bottom: 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 32rpx;
      color: rgba(34, 36, 46, 1);
      font-weight: bold;
      flex: 1;
    }
    .options-area {
      padding: 4rpx 10rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .options-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .label-item {
    display: flex;
    font-size: 24rpx;
    color: rgba(145, 148, 153, 1);
    margin-bottom: 16rpx;
    .status {
      font-size: 24rpx;
      // font-weight: bold;
      color: rgba(19, 191, 128, 0.8);
      &.blue {
        color: rgba(68, 138, 255, 0.8);
      }
      &.over {
        color: rgba(255, 106, 77, 0.8);
      }
      &.end {
        color: rgba(145, 148, 153, 1);
      }
    }
    .num {
      font-size: 24rpx;
      color: rgba(60, 61, 66, 1);
      margin-left: 32rpx;
      flex: 1;
    }
  }
  .bottom-item {
    margin-top: 16rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #c2c5cc;
    font-size: 22rpx;
  }
  .sort-area {
    height: 100%;
    padding: 64rpx 32rpx 64rpx 28rpx;
    box-sizing: border-box;
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .sort-btn {
      width: 56rpx;
      height: 84rpx;
      border: 1px solid #ebecf0;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      .sort-img {
        width: 32rpx;
        height: 32rpx;
      }

      &.disabled {
        opacity: 0.3;
        pointer-events: none;
      }
    }
  }
}
.r64 {
  right: 64rpx !important;
}
.pr120 {
  padding-right: 120rpx !important;
}

// 文本省略号样式
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
