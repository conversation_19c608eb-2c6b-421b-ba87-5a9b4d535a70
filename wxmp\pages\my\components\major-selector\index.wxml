<van-popup show="{{ show }}" custom-style="background: #Fff !important;height:80vh" round z-index="999" position="bottom" bind:close="onClose">
  <view class="major-box">
    <view class="top">
      <view class="close" bindtap="onClose">关闭</view>
      <view class="title">请选择专业</view>
      <view class="confirm" bindtap="onConfirm">确定</view>
    </view>
    <view class="major-content">
      <view class="major-content-top">
        <view class="title">2025年教育部专业指导目录</view>
        <view class="label-list">
          <view class="label">{{ degreeName || '请选择学历' }}</view>
        </view>
        <view class="select-is">
          <text class="left">已选：</text>{{ selectedText || '请选择专业' }}
        </view>
        <view class="search-box">
          <input type="text" placeholder-style="color:rgba(194, 197, 204, 1)" placeholder="请输入专业名词关键词" value="{{ searchKeyword }}" bindinput="onSearchInput" />
          <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_search.png"></image>
        </view>
      </view>
      
      <!-- 搜索结果显示区域 -->
      <view class="search-result" wx:if="{{ searchKeyword && searchResults.length > 0 }}">
        <view class="search-item" wx:for="{{ searchResults }}" wx:key="index" bindtap="onSelectSearchResult" data-item="{{ item }}">
          <view class="search-item-text">{{ item.fullPath }}</view>
        </view>
      </view>
      
      <!-- 无搜索结果 -->
      <view class="no-result" wx:elif="{{ searchKeyword && searchResults.length === 0 }}">
        <view class="no-result-text">未找到相关专业</view>
      </view>
      
      <!-- 三级选择区域 -->
      <view class="major-contetnt-center" wx:else>
        <scroll-view scroll-y style="background: rgba(247, 248, 250, 1);" class="select-center-item" show-scrollbar="{{false}}" enhanced>
          <view class="one-text {{ selectedFirstIndex === index ? 'active' : '' }}" 
                wx:for="{{ firstLevelList }}" 
                wx:key="index" 
                bindtap="onFirstLevelSelect" 
                data-index="{{ index }}">{{ item.name }}</view>
        </scroll-view>
        <scroll-view scroll-y class="select-center-item" show-scrollbar="{{false}}" enhanced>
          <view class="two-text {{ selectedSecondIndex === index ? 'active' : '' }}" 
                wx:for="{{ secondLevelList }}" 
                wx:key="index" 
                bindtap="onSecondLevelSelect" 
                data-index="{{ index }}">{{ item.name }}</view>
        </scroll-view>
        <scroll-view scroll-y class="select-center-item" show-scrollbar="{{false}}" enhanced>
          <view class="two-text {{ selectedThirdIndex === index ? 'active' : '' }}" 
                wx:for="{{ thirdLevelList }}" 
                wx:key="index" 
                bindtap="onThirdLevelSelect" 
                data-index="{{ index }}">{{ item.name }}</view>
        </scroll-view>
      </view>
    </view>
  </view>
</van-popup> 