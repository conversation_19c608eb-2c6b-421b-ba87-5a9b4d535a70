import { defineStore } from "pinia"
import { ref } from "vue"
import { setShare } from "@/utils/share"

export const useWxSDKStore = defineStore("wxSDK", () => {
  // 状态
  const isInitSuccess = ref(false) // 是否初始化成功

  // 设置初始化状态
  const setInitSuccess = (success) => {
    isInitSuccess.value = success
  }

  // 配置微信SDK
  const configWxSDK = (configData) => {
    return new Promise((resolve, reject) => {
      // 配置微信SDK
      window.wx.config({
        debug: false,
        appId: configData.appId,
        timestamp: configData.timestamp,
        nonceStr: configData.nonceStr,
        signature: configData.signature,
        jsApiList: [
          "updateAppMessageShareData",
          "updateTimelineShareData",
          "onMenuShareAppMessage",
        ],
        openTagList: ["wx-open-launch-app"],
      })

      // 监听微信SDK状态
      window.wx.ready(() => {
        setInitSuccess(true)
        setShare()
        resolve(true)
      })

      window.wx.error((error) => {
        setInitSuccess(false)
        reject(error)
      })
    })
  }

  return {
    isInitSuccess,
    setInitSuccess,
    configWxSDK,
  }
})
