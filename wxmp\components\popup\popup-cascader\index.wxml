<van-popup custom-class="cascader-popup" show="{{ show }}" z-index="9999" closeable="{{true}}" round position="bottom" close-on-click-overlay custom-style="" bind:close="close">
  <view class="popup-header">所在地区</view>
  <view class="cascader">
    <view class="tabs">

      <view wx:for="{{selected}}" wx:key="index" class="tab-item {{index == currentIndex ? 'active' : ''}}" bindtap="changeTab" data-index="{{index}}">
        {{item.text}}
        <text wx:if="{{index !== selected.length - 1}}"></text>
      </view>
    </view>
    <scroll-view scroll-y class="list">
      <view wx:for="{{currentOptions}}" wx:key="index" class="option" bindtap="selectOption" data-option="{{item}}">
        {{item.text}}
      </view>
    </scroll-view>
  </view>
</van-popup>