<template name="navitem">
  <view class="nav-item  scroll-item   {{item.key==tabsIndex?'active':''}} " id="{{item.key}}" wx:for="{{tabsList}}" wx:key="key">
    <view class="nav-link" style="display: flex;justify-content: center;" data-data="{{item}}" catchtap="clickTabsNav">
      <view style="position: relative;">
        {{item.name}}
        <image src="{{IMAGE_PREFIX}}images/course/course_advclass_try_listen.png" wx:if="{{item.key=='course_list'&&isTry}}" class="try-listen"></image>
        <image src="{{IMAGE_PREFIX}}images/course/course_trial.png" wx:if="{{item.key=='qv_list'&&isTry}}" class="try-listen"></image>
      </view>
    </view>
  </view>
</template>

<!--这是tab.wxml-->
<view class="cmpt-tabs " wx:if='{{tabsList.length>=2}}'>
  <view class="tab-nav-box">
    <scroll-view wx:if="{{isScroll}}" class=" tab-nav tab-nav1 scroll-x {{isCenter?'flex':''}} " scroll-into-view="tiku{{renderNumber?'':tabsIndex}}" enhanced="{{true}}" show-scrollbar="{{false}}" scroll-x="true">
      <template is="navitem" data="{{tabsList,tabsIndex,mrShow}}"></template>
    </scroll-view>
    <view wx:else class="tab-nav tab-nav2 ">
      <view class="nav-list">
        <template is="navitem" data="{{tabsList,tabsIndex,IMAGE_PREFIX,isTry}}"></template>
      </view>
      <view class="line {{tabsItemOffset?'active':''}}" style="left:{{tabsItemOffset.left+tabsItemOffset.width/2}}px;"></view>
    </view>
  </view>
</view>