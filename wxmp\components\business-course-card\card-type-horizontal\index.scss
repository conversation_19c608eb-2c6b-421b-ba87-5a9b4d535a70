.course-horizontal-card {
  box-sizing: border-box;
  background: #fff;
  margin-bottom: 24rpx;
  width: 100%;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0rpx 2rpx 6rpx 2rpx rgba(0, 0, 0, 0.04);
  .left-bg {
    width: 240rpx;
    height: 150rpx;
    object-fit: cover;
    border-radius: 8rpx;
    margin-top: 10rpx;
  }
  .fonts {
    flex: 1;
    min-width: 0;
    padding-left: 24rpx;
    .title-h {
      height: 96rpx;
    }
    .label-text {
      font-size: 24rpx;
      color: #919499;
      margin-top: 8rpx;
    }
    .fonts-bootom {
      margin-top: 16rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .teacher-list {
        display: flex;
        align-items: center;
        .img {
          width: 48rpx;
          height: 48rpx;
          border-radius: 50%;
          object-fit: cover;
          margin-right: 16rpx;
        }
      }

      .right-box {
      }
    }
  }
}

.study-num {
  font-size: 22rpx;
  color: #c2c5cc;
  margin-right: 16rpx;
}

.text-ellipsis-2 {
  overflow: hidden;
  /*隐藏多出部分文字*/
  text-overflow: ellipsis;
  /*用省略号代替多出部分文字*/
  display: -webkit-box;
  /* 显示多行文本容器 */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
