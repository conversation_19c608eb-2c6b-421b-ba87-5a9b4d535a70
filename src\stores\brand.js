import { defineStore } from "pinia"
import BRAND from "../config/brand/brand"
import BRAND_HOST from "../config/brand/brandHost"
import BRAND_IMAGE from "../config/brand/brandImage"
import BRAND_COLOR from "../config/brand/brandColor"
import BRAND_SCHEMAS from "../config/brand/brandSchemas"

const DEFAULT_HOST_BRAND_OPENIDKEY = "share_skb"

/**
 * 品牌状态管理Store
 * 职责：品牌信息管理、品牌切换、品牌相关工具方法
 *
 * @typedef {Object} HostBrand
 * @property {string} brandKey - 品牌标识
 * @property {string} url - 域名
 * @property {string} openidKey - OpenID键名
 * @property {string} openidBrandkey - OpenID品牌键名
 *
 * @typedef {Object} Brand
 * @property {string} name - 品牌名称
 * @property {string} brandKey - 品牌标识
 *
 * @typedef {Object} BrandState
 * @property {HostBrand} currentHostBrand - 当前主机品牌信息
 * @property {Brand} currentBrand - 当前品牌信息
 */
export const useBrandStore = defineStore("brand", {
  state: () => ({
    // 当前品牌基本信息
    brand: null,
    // 当前主机品牌信息
    host: null,
    // 当前品牌图片配置
    image: null,
    // 当前品牌颜色配置
    color: null,
    // 当前品牌Schema配置
    schema: null,
  }),

  getters: {
    // 品牌名称
    brandName: (state) => state.brand?.name || "",

    // 品牌标识
    brandKey: (state) => state.brand?.brandKey || "",

    // 当前域名
    currentUrl: () => window?.location?.host || "",

    // OpenID键名
    openidKey: (state) => state.host?.openidKey || "",

    // OpenID品牌键名
    openidBrandkey: (state) => state.host?.openidBrandkey || "",

    // 是否为测试环境
    isTestEnvironment: (state) => {
      return state.host?.openidKey?.includes("test") || false
    },
  },

  actions: {
    /**
     * 初始化品牌信息
     * 根据当前域名自动识别品牌
     */
    initBrand() {
      try {
        // 获取当前主机品牌
        const hostBrand = this.getCurrentHostBrand(BRAND_HOST, DEFAULT_HOST_BRAND_OPENIDKEY)

        // 获取当前品牌
        const brand = this.getBrandByKey(hostBrand.brandKey)

        // 设置 state 中的各个字段
        this.brand = brand
        this.host = hostBrand
        this.image = BRAND_IMAGE[brand.brandKey] || null
        this.color = BRAND_COLOR[brand.brandKey] || null
        this.schema = BRAND_SCHEMAS[brand.brandKey] || null
      } catch (error) {
        console.error("品牌初始化失败:", error)
        // 使用默认品牌
        this.setDefaultBrand()
      }
    },

    /**
     * 设置默认品牌（事考帮）
     */
    setDefaultBrand() {
      const defaultBrand = BRAND[0]
      const defaultHost = BRAND_HOST[0]

      this.brand = defaultBrand
      this.host = defaultHost
      this.image = BRAND_IMAGE[defaultBrand.brandKey] || null
      this.color = BRAND_COLOR[defaultBrand.brandKey] || null
      this.schema = BRAND_SCHEMAS[defaultBrand.brandKey] || null
      this.isInitialized = true
    },

    /**
     * 获取访问品牌信息
     * @param {Array} list - 品牌主机列表
     * @param {string} defaultHostBrandOpenidkey - 默认主机品牌OpenID键名
     * @returns {Object} 主机品牌信息
     */
    getCurrentHostBrand(list, defaultHostBrandOpenidkey) {
      const hostURL = window?.location?.host || ""

      let result = list.filter((brand) => {
        const brandURL = brand.url

        if (Array.isArray(brandURL)) {
          return brandURL.includes(hostURL)
        }
        if (typeof brandURL === "string") {
          return brandURL === hostURL
        }
        return false
      })

      // 如果没有匹配的，使用默认的
      if (result.length === 0) {
        result = list.filter((brand) => {
          return brand.openidKey === defaultHostBrandOpenidkey
        })
      }

      return result[0] || list[0]
    },

    /**
     * 根据品牌标识获取品牌信息
     * @param {string} brandKey - 品牌标识
     * @returns {Object|null} 品牌信息
     */
    getBrandByKey(brandKey) {
      return BRAND.find((brand) => brand.brandKey === brandKey) || null
    },

    /**
     * 根据URL获取主机品牌信息
     * @param {string} url - 域名
     * @returns {Object|null} 主机品牌信息
     */
    getHostBrandByUrl(url) {
      return (
        BRAND_HOST.find((brand) => {
          if (Array.isArray(brand.url)) {
            return brand.url.includes(url)
          }
          return brand.url === url
        }) || null
      )
    },

    /**
     * 获取微信OpenID URL
     * @param {Object} query - 查询参数
     * @returns {string} OpenID URL
     */
    getWxOpenidURL(query = {}) {
      if (!this.host) {
        console.warn("当前主机品牌信息未初始化")
        return ""
      }

      const queryWithBrand = {
        ...query,
        brandkey: this.host.openidBrandkey,
      }

      const p = JSON.stringify(queryWithBrand)

      // 测试服调用另一个地址
      // if (this.host.openidKey.indexOf('test') >= 0) {
      //   return `https://wxapi.shikaobang.cn/third-auth/web/wx/getOpenid?k=${this.host.openidKey}&p=${p}`
      // }

      return `https://wxapi.shikaobang.cn/third-auth/web/wx/getOpenid?k=${this.host.openidKey}&p=${p}`
    },

    /**
     * 获取金标尺教师Schema协议
     * @returns {string} Schema协议
     */
    getGpjsSchemaProtocol() {
      const isIOS = !(
        navigator.userAgent.indexOf("Android") > -1 || navigator.userAgent.indexOf("Adr") > -1
      )
      return isIOS ? "gpjiaoshi" : "gpjs"
    },

    /**
     * 获取当前品牌Schema参数
     * @returns {Object|null} Schema参数
     */
    getCurrentBrandSchemaParam() {
      if (!this.schema) {
        console.warn("当前品牌Schema配置未找到")
        return null
      }

      return JSON.parse(JSON.stringify(this.schema))
    },
  },
})
