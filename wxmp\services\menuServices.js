// 菜单处理工具函数

/**
 * 处理单个菜单项
 * @param {Object} menuItem 菜单项数据
 * @returns {Object} 处理后的菜单项
 */
function processMenuItem(menuItem) {
  const { title, filter_key, type, is_fixed, data } = menuItem

  // 直接使用filter_key作为key，不再进行特殊处理映射
  let key = filter_key
  let menuType = type

  const processedMenuItem = {
    title,
    key,
    filter_key, // 保存原始的filter_key用于数据获取
    type: menuType,
    is_fixed: is_fixed,
    selected: false,
    data: data || null, // 保存原始数据用于后续处理
  }

  // 根据type类型初始化相关数据
  if (type === "dialog_grid" && data && data.length > 0) {
    // 处理dialog_grid类型的数据
    if (data.length === 1) {
      // 单个数据组，类似考试类型的展现方式
      processedMenuItem.examData = data[0].list || []
    } else {
      // 多个数据组，类似筛选的展现方式
      processedMenuItem.filterData = data
    }
  }

  return processedMenuItem
}

/**
 * 处理菜单列表，返回二维数组格式的 menuList
 * @param {Array} serverMenuList 服务器返回的菜单列表
 * @returns {Array} 处理后的 menuList（二维数组）
 */
function processMenuList(serverMenuList) {
  if (!Array.isArray(serverMenuList) || serverMenuList.length === 0) return []

  // 检查是否有is_fixed为true的菜单项
  const hasFixedMenus = serverMenuList.some(
    (menuItem) => menuItem.is_fixed === true
  )

  if (hasFixedMenus) {
    // 分离固定菜单和非固定菜单
    const fixedMenus = serverMenuList.filter(
      (menuItem) => menuItem.is_fixed === true
    )
    const nonFixedMenus = serverMenuList.filter(
      (menuItem) => menuItem.is_fixed === false
    )

    // 处理非固定菜单（左侧滚动部分）
    const dynamicLeftMenuList = nonFixedMenus.map(processMenuItem)
    // 处理固定菜单（右侧固定部分）
    const dynamicRightMenuList = fixedMenus.map(processMenuItem)

    // 返回新的二维数组格式：[[leftMenus], [rightMenus]]
    return [dynamicLeftMenuList, dynamicRightMenuList]
  } else {
    // 所有菜单都是is_fixed为false，使用单行布局：[[allMenus]]
    const dynamicMenuList = serverMenuList.map(processMenuItem)
    return [dynamicMenuList]
  }
}

module.exports = {
  processMenuList,
}
