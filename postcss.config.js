export default {
  plugins: {
    autoprefixer: {},
    // "postcss-pxtorem": {
    //   // 根元素字体大小，1rem = 37.5px
    //   rootValue: 37.5,
    //   // 需要转换的CSS属性，* 表示所有属性
    //   propList: ["*"],
    //   // 不转换的选择器，保留px单位
    //   selectorBlackList: [
    //     ".van-", // Vant组件不转换，避免样式问题
    //     ".ignore-", // 自定义忽略类名前缀
    //   ],
    //   // 替换规则
    //   replace: true,
    //   // 媒体查询中是否转换px
    //   mediaQuery: false,
    //   // 最小转换数值，小于此值不转换
    //   minPixelValue: 2,
    //   // 排除的文件或文件夹
    //   exclude: /node_modules/i,
    // },
  },
}
