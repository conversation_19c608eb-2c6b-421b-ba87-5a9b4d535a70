const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Component({
  options: {
    addGlobalClass: true, //使用全局样式
    isLoad: false, // 页面是否加载完成
    multipleSlots: true,
  },
  /**
   * 组件的属性列表
   */
  properties: {
    haveRight: {
      type: Boolean,
      value: false,
    },
    list: {
      type: Array,
      value: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    toJobDetail(e) {
      const { id } = e.currentTarget.dataset.item
      ROUTER.navigateTo({
        path: "/pages/job/detail/index",
        query: {
          id,
        },
      })
    },
  },
})
