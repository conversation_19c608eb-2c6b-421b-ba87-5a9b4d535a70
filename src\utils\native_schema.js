// 常量定义
const MOBILE_AGENTS = ["Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"]
const DEFAULT_LOAD_WAITING = 3000
const FALLBACK_DELAY = 200

// 浏览器检测工具
const browser = {
  isAndroid: () => /Android/i.test(navigator.userAgent),
  isMobileQQ: () => {
    const ua = navigator.userAgent
    return (
      /(iPad|iPhone|iPod).*? (IPad)?QQ\/([\d]+)/.test(ua) ||
      /\bV1_AND_SQI?_([\d]+)(.*? QQ\/([\d]+))?/.test(ua)
    )
  },
  isIOS: () => /iPhone|iPad|iPod|Mac/i.test(navigator.userAgent),
  isWx: () => /micromessenger/i.test(navigator.userAgent),
  isPC: () => !MOBILE_AGENTS.some((agent) => navigator.userAgent.includes(agent)),
}

// App配置
const AppConfig = {
  PROTOCAL: "",
  HOME: "",
  FAILBACK: {
    ANDROID: "",
    IOS: "",
  },
  APK_INFO: {
    PKG: "",
    CATEGORY: "android.intent.category.DEFAULT",
    ACTION: "android.intent.action.VIEW",
  },
  LOAD_WAITING: DEFAULT_LOAD_WAITING,
}

// 浏览器提示弹窗样式
const MODAL_STYLES = `
  p{padding:0;margin:0}
  .modal_browser_tips{position:fixed;top:0;left:0;z-index:1050;display:none;width:100%;height:100%;outline:0}
  .modal-tip-bg{position:absolute;width:100%;height:100%;transition:all .3s linear;background-color:rgba(0,0,0,0)}
  .right-browser{position:absolute;top:0;right:14px;width:4rem;height:2.4rem;background:url(https://skbang-1253756937.cos.ap-guangzhou.myqcloud.com/active/right_browser.png) no-repeat center;background-size:100%;opacity:0;animation-duration:1s;transform:scale(1)}
  .show{display:block!important}
  .modal-tip-bg.in{background-color:rgba(0,0,0,.6)}
  .right-browser.in{animation-duration:.6s;animation-name:bounceIn;opacity:1}
  @keyframes bounceIn{0%,50%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:scale3d(0.3,0.3,0.3)}50%{opacity:1;transform:scale3d(1.03,1.03,1.03)}to{opacity:1;transform:scale(1)}}
`

const MODAL_TEMPLATE = `<div class="modal_browser_tips"><div class="modal-tip-bg"><div class="right-browser"></div></div></div>`

// 浏览器提示弹窗 - 单例模式
class BrowserTip {
  constructor() {
    if (BrowserTip.instance) {
      return BrowserTip.instance
    }

    this.initializeModal()
    BrowserTip.instance = this
  }

  initializeModal() {
    if (BrowserTip.initialized) return

    // 添加样式
    const styleEl = document.createElement("style")
    styleEl.innerHTML = MODAL_STYLES
    document.head.appendChild(styleEl)

    // 添加DOM结构
    if (document.readyState === "loading") {
      document.write(MODAL_TEMPLATE)
    } else {
      document.body.insertAdjacentHTML("beforeend", MODAL_TEMPLATE)
    }

    // 绑定事件
    this.bindEvents()
    BrowserTip.initialized = true
  }

  bindEvents() {
    const modal = document.querySelector(".modal_browser_tips")
    modal?.addEventListener("click", () => this.hide(), false)
  }

  show() {
    const modal = document.querySelector(".modal_browser_tips")
    const background = document.querySelector(".modal-tip-bg")
    const tips = document.querySelector(".right-browser")

    modal?.classList.add("show")
    tips?.classList.add("in")
    setTimeout(() => background?.classList.add("in"), 1)
  }

  hide() {
    const modal = document.querySelector(".modal_browser_tips")
    const background = document.querySelector(".modal-tip-bg")
    const tips = document.querySelector(".right-browser")

    background?.classList.remove("in")
    tips?.classList.remove("in")
    setTimeout(() => modal?.classList.remove("show"), 600)
  }
}

// 静态属性
BrowserTip.instance = null
BrowserTip.initialized = false

// 创建单例实例
const browserTips = new BrowserTip()

/**
 * 合并配置参数
 * @param {Object} config 配置对象
 */
function mixinConfig(config) {
  if (!config) return

  AppConfig.PROTOCAL = config.protocal || AppConfig.PROTOCAL
  AppConfig.schema = config.schema || AppConfig.HOME
  AppConfig.LOAD_WAITING = config.loadWaiting || AppConfig.LOAD_WAITING

  if (browser.isIOS()) {
    AppConfig.FAILBACK.IOS = config.failUrl || AppConfig.FAILBACK.IOS
  } else {
    AppConfig.FAILBACK.ANDROID = config.failUrl || AppConfig.FAILBACK.ANDROID
    AppConfig.APK_INFO = config.apkInfo || AppConfig.APK_INFO
  }
}

/**
 * 生成Schema URL
 * @param {string} schema Schema路径
 * @returns {string} 完整的Schema URL
 */
function generateSchema(schema) {
  const schemaStr = schema || AppConfig.HOME
  return `${AppConfig.PROTOCAL}://${schemaStr}`
}

/**
 * 创建隐藏的链接元素并触发点击
 * @param {string} url 要打开的URL
 */
function createAndClickLink(url) {
  const link = document.createElement("a")
  link.style.cssText = "display:none;width:0;height:0;"
  link.href = url
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 检查是否在App内部环境
 * @returns {boolean} 是否在App内部
 */
function isInApp() {
  return !!(window?.tikuWeb?.setShareHtml || window?.webkit?.messageHandlers?.canLoadShare)
}

/**
 * 设置页面可见性变化监听器
 * @param {Function} callback 回调函数
 */
function setupVisibilityListeners(callback) {
  const visibilityChange = () => {
    if (document.hidden || document.webkitHidden) {
      callback()
    }
  }

  document.addEventListener("visibilitychange", visibilityChange, false)
  document.addEventListener("webkitvisibilitychange", visibilityChange, false)
  window.addEventListener("pagehide", callback, false)
}

/**
 * 唤醒native App，如果无法唤醒则跳转到下载页
 * @param {Object} config 配置对象
 */
function openSchema(config) {
  mixinConfig(config)

  const schemaUrl = generateSchema(AppConfig.schema)

  // 微信环境下显示提示
  if (browser.isWx()) {
    browserTips.show()
    return
  }

  // 创建链接并触发点击
  createAndClickLink(schemaUrl)

  // 设置超时处理
  const startTime = Date.now()
  let loadTimer = setTimeout(() => {
    // 如果页面已隐藏，说明可能已经跳转到App
    if (document.hidden || document.webkitHidden) {
      return
    }

    // 检查时间差，判断是否从App返回
    const timeDiff = Date.now() - startTime
    if (timeDiff > AppConfig.LOAD_WAITING + FALLBACK_DELAY) {
      // 从App返回，不做处理
      return
    }

    // 如果在App内部，不跳转到下载页
    if (isInApp()) {
      return
    }

    // 跳转到下载页
    const fallbackUrl = browser.isIOS() ? AppConfig.FAILBACK.IOS : AppConfig.FAILBACK.ANDROID
    if (fallbackUrl) {
      window.location.href = fallbackUrl
    }
  }, AppConfig.LOAD_WAITING)

  // 监听页面可见性变化，清除定时器
  setupVisibilityListeners(() => {
    clearTimeout(loadTimer)
  })
}

export default openSchema
