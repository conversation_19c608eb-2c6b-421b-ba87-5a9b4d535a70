const API = require("@/config/api")
const UTIL = require("@/utils/util")

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    defaultValue: {
      type: String,
      value: "",
    },
    educationId: {
      type: String,
      value: "6", // 默认为普通本科
    },
    degreeName: {
      type: String,
      value: "", // 学历名称
    },
    majorList: {
      type: Array,
      value: [],
    },
    // 新增：用于回显的专业ID数组 [一级ID, 二级ID, 三级ID]
    selectedMajorIds: {
      type: Array,
      value: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    searchKeyword: "",
    selectedText: "",
    selectedFirstIndex: -1,
    selectedSecondIndex: -1,
    selectedThirdIndex: -1,
    selectedMajor: null,
    firstLevelList: [],
    secondLevelList: [],
    thirdLevelList: [],
    searchResults: [], // 搜索结果
    selectedIndexes: [], // 存储每一级的选中索引，用于回显
    selectedMajorIds: [], // 存储每一级的ID，用于回显
  },

  /**
   * 监听属性变化
   */
  observers: {
    async show(newVal) {
      if (newVal) {
        this.setData({
          firstLevelList: this.properties.majorList,
          secondLevelList: [],
          thirdLevelList: [],
          selectedFirstIndex: -1,
          selectedSecondIndex: -1,
          selectedThirdIndex: -1,
          selectedMajor: null,
          selectedText: "",
          selectedMajorIds: this.properties.selectedMajorIds || [],
        })

        // 如果有回显ID数组，进行回显
        if (this.data.selectedMajorIds.length === 3) {
          await this.performEchoByIds()
        }
      }
    },

    // 监听学历ID变化，重置专业选择器状态
    educationId(newVal, oldVal) {
      if (newVal !== oldVal && oldVal) {
        // 学历改变时，重置所有状态
        this.setData({
          selectedFirstIndex: -1,
          selectedSecondIndex: -1,
          selectedThirdIndex: -1,
          selectedMajor: null,
          selectedText: "",
          selectedIndexes: [],
          selectedMajorIds: [],
          secondLevelList: [],
          thirdLevelList: [],
        })
      }
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 根据ID数组进行回显
     */
    async performEchoByIds() {
      const [firstId, secondId, thirdId] = this.data.selectedMajorIds

      try {
        // 1. 找到第一级索引
        const firstIndex = this.data.firstLevelList.findIndex(
          (item) => item.id == firstId
        )
        if (firstIndex === -1) return

        // 2. 选中第一级并获取第二级数据
        await this.onFirstLevelSelect({
          currentTarget: { dataset: { index: firstIndex } },
        })

        // 3. 找到第二级索引
        const secondIndex = this.data.secondLevelList.findIndex(
          (item) => item.id == secondId
        )
        if (secondIndex === -1) return

        // 4. 选中第二级并获取第三级数据
        await this.onSecondLevelSelect({
          currentTarget: { dataset: { index: secondIndex } },
        })

        // 5. 找到第三级索引
        const thirdIndex = this.data.thirdLevelList.findIndex(
          (item) => item.id == thirdId
        )
        if (thirdIndex === -1) return

        // 6. 选中第三级
        this.onThirdLevelSelect({
          currentTarget: { dataset: { index: thirdIndex } },
        })
      } catch (error) {
        console.error("回显失败:", error)
      }
    },

    /**
     * 关闭弹窗
     */
    onClose() {
      this.triggerEvent("close")
    },

    /**
     * 确认选择
     */
    onConfirm() {
      if (this.data.selectedMajor) {
        // 保存选中的索引和ID用于回显
        const selectedIds = []
        if (this.data.selectedFirstIndex >= 0) {
          selectedIds.push(
            this.data.firstLevelList[this.data.selectedFirstIndex].id
          )
        }
        if (this.data.selectedSecondIndex >= 0) {
          selectedIds.push(
            this.data.secondLevelList[this.data.selectedSecondIndex].id
          )
        }
        if (this.data.selectedThirdIndex >= 0) {
          selectedIds.push(
            this.data.thirdLevelList[this.data.selectedThirdIndex].id
          )
        }

        this.setData({
          selectedIndexes: [
            this.data.selectedFirstIndex,
            this.data.selectedSecondIndex,
            this.data.selectedThirdIndex,
          ],
          selectedMajorIds: selectedIds,
        })

        this.triggerEvent("confirm", {
          major: this.data.selectedMajor,
          text: this.data.selectedText,
          majorIds: selectedIds, // 传递ID数组给父组件
        })
        this.onClose()
      } else {
        wx.showToast({
          title: "请选择一个专业",
          icon: "none",
        })
      }
    },

    /**
     * 搜索输入事件
     */
    onSearchInput(e) {
      const keyword = e.detail.value.trim()
      this.setData({
        searchKeyword: keyword,
      })

      if (keyword) {
        this.performSearch(keyword)
      } else {
        this.setData({
          searchResults: [],
        })
      }
    },

    /**
     * 执行搜索
     */
    async performSearch(keyword) {
      try {
        const param = {
          keywords: keyword,
          education_id: this.data.educationId,
        }
        const res = await UTIL.request(API.getMajorByKeyWords, param)
        if (res && res.data && res.data.length > 0) {
          this.setData({
            searchResults: res.data,
          })
        } else {
          this.setData({
            searchResults: [],
          })
        }
      } catch (error) {
        console.error("搜索失败:", error)
        this.setData({
          searchResults: [],
        })
        wx.showToast({
          title: "搜索失败",
          icon: "none",
        })
      }
    },

    /**
     * 选择搜索结果
     */
    onSelectSearchResult(e) {
      const selectedItem = e.currentTarget.dataset.item
      // 注意：这里需要根据 `getMajorByKeyWords` 返回的数据结构来确定如何构造 major 和 text
      // 假设返回的 item 包含 id 和 name，并且 name 就是完整的专业路径
      // 如果搜索结果包含路径ID数组，则使用；否则设置为空数组
      const majorIds = selectedItem.path_ids || []

      this.triggerEvent("confirm", {
        major: { id: selectedItem.id, name: selectedItem.name },
        text: selectedItem.name,
        majorIds: majorIds,
      })
      this.onClose()
    },

    /**
     * 选择一级分类
     */
    async onFirstLevelSelect(e) {
      const index = e.currentTarget.dataset.index
      const selectedItem = this.data.firstLevelList[index]

      try {
        this.setData({
          selectedFirstIndex: index,
          selectedSecondIndex: -1,
          selectedThirdIndex: -1,
          selectedMajor: null,
          selectedText: "",
          secondLevelList: [],
          thirdLevelList: [],
        })

        const param = {
          pid: selectedItem.id,
          education_id: this.data.educationId,
        }
        const res = await UTIL.request(API.getMajorList, param)
        if (res && res.data && res.data.length > 0) {
          this.setData({
            secondLevelList: res.data,
          })
        }
      } catch (error) {
        console.error("获取第二级专业数据失败:", error)
        wx.showToast({
          title: "获取专业数据失败",
          icon: "none",
        })
      }
    },

    /**
     * 选择二级分类
     */
    async onSecondLevelSelect(e) {
      const index = e.currentTarget.dataset.index
      const selectedItem = this.data.secondLevelList[index]

      try {
        this.setData({
          selectedSecondIndex: index,
          selectedThirdIndex: -1,
          selectedMajor: null,
          selectedText: "",
          thirdLevelList: [],
        })

        const param = {
          pid: selectedItem.id,
          education_id: this.data.educationId,
        }
        const res = await UTIL.request(API.getMajorList, param)
        if (res && res.data && res.data.length > 0) {
          this.setData({
            thirdLevelList: res.data,
          })
        }
      } catch (error) {
        console.error("获取第三级专业数据失败:", error)
        wx.showToast({
          title: "获取专业数据失败",
          icon: "none",
        })
      }
    },

    /**
     * 选择三级分类（具体专业）
     */
    onThirdLevelSelect(e) {
      const index = e.currentTarget.dataset.index
      const selectedItem = this.data.thirdLevelList[index]
      const firstLevel = this.data.firstLevelList[this.data.selectedFirstIndex]
      const secondLevel =
        this.data.secondLevelList[this.data.selectedSecondIndex]

      const selectedText = `${firstLevel.name}-${secondLevel.name}-${selectedItem.name}`

      this.setData({
        selectedThirdIndex: index,
        selectedMajor: selectedItem,
        selectedText: selectedText,
      })
    },
  },
})
