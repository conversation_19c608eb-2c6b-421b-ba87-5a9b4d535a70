const {
  handleSingleSelect,
  handleMultiSelect,
} = require("@/services/selectionService")

/**
 * 考试选择内容组件
 * 纯展示组件，只负责展示列表和触发点击事件
 * 选择逻辑完全由页面控制
 */
Component({
  /**
   * 组件属性
   */
  properties: {
    // 考试列表数据
    dataList: {
      type: Array,
      value: [],
    },
    // 选中状态数据
    selected: {
      type: Object,
      value: {},
    },
    // 唯一键名，用于wx:key
    uniqueKey: {
      type: String,
      value: "value",
    },
    filterKey: {
      type: String,
      value: "",
    },
    show: {
      type: Boolean,
      value: false,
    },
  },

  /**
   * 组件数据
   */
  data: {
    tempSelected: {},
  },
  observers: {
    selected: function (newVal) {
      this.setData({
        tempSelected: JSON.parse(JSON.stringify(newVal)),
      })
    },
    show: function (newVal) {
      this.setData({
        tempSelected: JSON.parse(JSON.stringify(this.data.selected)),
      })
    },
  },
  /**
   * 组件方法
   */
  methods: {
    /**
     * 处理考试选择
     * @param {Object} e - 事件对象
     */
    handleOptionSelect(e) {
      const { value, key } = e.currentTarget.dataset
      const isMultipleChoice =
        this.data.dataList.find((item) => item.filter_key === key)?.is_radio ===
        0

      const data = this.data.tempSelected[key]

      const tempSelected = isMultipleChoice
        ? handleMultiSelect(data, value)
        : handleSingleSelect(data, value)
      this.setData({
        [`tempSelected.${key}`]: tempSelected,
      })
    },
    handleReset() {
      let tempSelected = { ...this.data.tempSelected }

      for (const key in tempSelected) {
        tempSelected[key] = []
      }
      this.setData({
        tempSelected,
      })
      // 只触发事件，不处理选择逻辑
      this.triggerEvent("reset", { filterKey: this.data.filterKey })
    },
    handleConfirm() {
      console.log("cccccccccccc")
      // 只触发事件，不处理选择逻辑
      this.triggerEvent("confirm", {
        filterKey: this.data.filterKey,
        tempSelected: this.data.tempSelected,
      })
    },
  },
})
