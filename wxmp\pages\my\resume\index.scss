page {
  background: #f7f8fa;
}

/* 通用选择器样式覆盖 - 完全模仿big-data页面 */
.column-area {
  .van-picker-column__item {
    font-size: 30rpx;
    font-weight: 400;
    color: #313436;
  }

  view .active-item {
    font-weight: 500;
    color: #313436;
    font-size: 32rpx;
  }
}

.top-area {
  .van-picker__title {
    font-size: 32rpx;
    font-weight: 500;
    color: #313436;
  }

  .van-picker__confirm {
    font-size: 28rpx;
    font-weight: 400;
    color: #e60003;
  }

  .van-picker__cancel {
    font-size: 28rpx;
    font-weight: 400;
    color: #c2c5cc;
  }
}
.van-picker__toolbar {
  border-bottom: 2rpx solid #ebecf0;
}

.resume {
  min-height: 100vh;
  padding: 48rpx 32rpx;
  box-sizing: border-box;
  .top-card {
    width: 100%;
    padding: 32rpx;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 16rpx;
    position: relative;
    .title-area {
      display: flex;
      align-items: center;
      .text {
        font-size: 30rpx;
        color: #22242e;
        font-weight: bold;
        margin-right: 8rpx;
      }
      .img {
        width: 128rpx;
        height: 36rpx;
        margin-top: 1rpx;
      }
    }
    .mt16 {
      margin-top: 16rpx !important;
    }
    .checked-item {
      display: flex;
      align-items: center;
      margin-top: 24rpx;
      .icon {
        width: 24rpx;
        height: 24rpx;
        margin-right: 8rpx;
      }
      .text {
        font-size: 22rpx;
        color: #666666;
      }
    }
    .circle-box {
      width: 220rpx;
      height: 220rpx;
      background: #ffffff;
      border-radius: 50%;
      position: absolute;
      right: 32rpx;
      bottom: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      .circle-text {
        position: absolute;
        top: 46%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 28rpx;
        font-weight: bold;
        color: #3c3d42;
        font-family: "DINBold";
        vertical-align: bottom;
        .num {
          font-size: 48rpx;
        }
      }
    }
  }
  .bottom-text {
    margin-top: 32rpx;
    font-size: 22rpx;
    color: #c2c5cc;
    .blue-text {
      color: #448aff;
    }
  }
  .bottom-btn {
    width: 100%;
    padding: 14rpx 32rpx;
    box-sizing: border-box;
    .btn {
      width: 100%;
      height: 84rpx;
      background: #ec3e33;
      border-radius: 16rpx;
      color: #ffffff;
      font-size: 28rpx;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0.5;

      &.active {
        opacity: 1;
      }
    }
  }
}
