page {
  background: #f7f8fa;
}
.job-detail {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  .top-area {
    background: #ffffff;
    padding: 24rpx 32rpx 40rpx 32rpx;
    .title-area {
      display: flex;
      justify-content: space-between;
      margin-bottom: 32rpx;
      .left-text {
        font-size: 36rpx;
        color: #22242e;
        font-weight: bold;
      }
      .right-text {
        font-size: 28rpx;
        color: #13bf80;
        font-weight: 500;
      }
    }
    .main-area {
      .entry-item {
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
        .title {
          font-size: 26rpx;
          color: #919499;
          margin-right: 40rpx;
        }
        .content {
          font-size: 26rpx;
          color: #3c3d42;
        }
        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }
  }
  .baokao-area {
    .data-area {
      width: 100%;
      height: 170rpx;
      background: rgba(247, 248, 250, 0.5);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      margin-bottom: 32rpx;
      .data-item {
        flex: 1;
        position: relative;
        .content {
          display: flex;
          flex-direction: column;
          align-items: center;
          .num {
            color: #3c3d42;
            font-family: "DINBold";
            font-weight: 500;
            font-size: 40rpx;
            margin-bottom: 24rpx;
          }
          .text {
            font-size: 24rpx;
            color: #3c3d42;
          }
        }
        .line {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 1rpx;
          height: 48rpx;
          background: #ebecf0;
        }
        &:last-of-type {
          .line {
            display: none;
          }
        }
      }
    }
    .data-sourse {
      .list-item {
        display: flex;
        align-items: center;
        .title {
          font-size: 22rpx;
          color: #c2c5cc;
        }
        .text {
          color: #919499;
          font-size: 22rpx;
        }
      }
    }
  }

  .history-area {
    .card-img {
      width: 686rpx;
      height: 428rpx;
      border-radius: 16rpx;
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .title {
        color: #3c3d42;
        font-size: 32rpx;
        font-weight: 500;
        margin-bottom: 32rpx;
      }
      .btn {
        width: 280rpx;
        height: 80rpx;
        background: #ec3e33;
        box-shadow: 0 8rpx 20rpx 2rpx rgba(230, 0, 3, 0.2);
        border-radius: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 26rpx;
        font-weight: 500;
      }
      .bg-img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: -1;
      }
    }
    .desc-text {
      .gray-text {
        color: #c2c5cc;
      }
      margin-top: 32rpx;
      width: 100%;
      color: #919499;
      font-size: 22rpx;
    }
  }

  .form-area {
    .form-item {
      display: flex;
      align-items: center;
      padding: 24rpx 32rpx;
      box-sizing: border-box;
      border-radius: 8rpx;
      &:nth-of-type(2n + 1) {
        background: rgba(247, 248, 250, 0.6);
      }
      .left-text {
        font-size: 26rpx;
        color: #919499;
        width: 108rpx;
        margin-right: 40rpx;
      }
      .right-text {
        flex: 1;
        color: #3c3d42;
        font-size: 26rpx;
      }
    }
  }
  .bottom-text {
    padding: 40rpx 32rpx;
    box-sizing: border-box;
    color: #c2c5cc;
    font-size: 22rpx;
  }

  .bottom-area {
    width: 100%;
    background: #ffffff;
    padding: 14rpx 32rpx 32rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .left {
      margin-right: 64rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 80rpx;
      .img-area {
        position: relative;
        .img {
          width: 40rpx;
          height: 40rpx;
        }
        .message {
          position: absolute;
          top: -8rpx;
          right: -34rpx;
          font-weight: bold;
          font-size: 20rpx;
          line-height: 28rpx;
          color: #ffffff;
          background: #e60003;
          border-radius: 18rpx;
          padding: 2rpx 14rpx;
          box-sizing: border-box;
          border: 2rpx solid #ffffff;
        }
      }
      .text {
        color: #919499;
        font-size: 20rpx;
      }
    }
    .right {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .btn_1 {
        width: 260rpx;
        height: 84rpx;
        background: rgba(236, 62, 51, 0.1);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #ec3e33;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .btn_2 {
        width: 260rpx;
        height: 84rpx;
        background: rgba(236, 62, 51, 1);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        .img {
          width: 32rpx;
          height: 32rpx;
          margin-right: 8rpx;
        }
      }
      .op08 {
        opacity: 0.8 !important;
      }
    }
  }
  .position-sub-tab-list {
    display: flex;
    align-items: center;
    gap: 16rpx;
    width: fit-content;
    margin-bottom: 32rpx;

    .position-sub-tab-item {
      padding: 12rpx 30rpx;
      font-size: 24rpx;
      color: #3c3d42;
      background: #ffffff;
      border: 1rpx solid #ebecf0;
      border-radius: 8rpx;
      font-weight: 400;

      &.active {
        color: rgba(230, 0, 3, 0.8);
        background: rgba(230, 0, 3, 0.05);
        border: 1rpx solid rgba(230, 0, 3, 0.3);
      }
    }
  }
  .position-table-content {
    // 表格头部
    .position-table-header {
      display: flex;
      align-items: center;
      background: linear-gradient(
        180deg,
        #fef2f2 0%,
        rgba(254, 242, 242, 0.5) 100%
      );
      border-radius: 8rpx;
      padding: 20rpx 0 18rpx 0;
      .header-item {
        font-size: 24rpx;
        color: #93292c;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: 0 8rpx;

        text {
          line-height: 1.2;
          margin: 2rpx 0;
        }

        // 序号列
        &.header-0 {
          width: 80rpx;
          margin: 0;
        }

        // 年份列 - 加宽
        &.header-1 {
          width: 120rpx;
        }

        // 招聘人数列
        &.header-2 {
          width: 120rpx;
        }

        // 报名人数列
        &.header-3 {
          width: 120rpx;
        }

        // 报录比列
        &.header-4 {
          width: 100rpx;
        }

        // 最低进面分列
        &.header-5 {
          width: 120rpx;
        }
      }
    }

    // 表格主体
    .position-table-body {
      .position-table-row {
        display: flex;
        align-items: center;
        padding: 34rpx 0;
        &:nth-of-type(2n) {
          background: #f7f8fa;
        }
        .table-item {
          font-size: 24rpx;
          color: #22242e;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 8rpx;

          // 序号列
          &.table-0 {
            width: 80rpx;
            margin: 0;

            .rank-badge {
              display: inline-block;
              font-size: 24rpx;
              color: #919499;

              &.rank-1 {
                color: #e60003;
                font-weight: 500;
              }

              &.rank-2 {
                color: #ff6a4d;
                font-weight: 500;
              }

              &.rank-3 {
                color: #ffb474;
                font-weight: 500;
              }
            }
          }

          // 年份列 - 加宽
          &.table-1 {
            width: 120rpx;

            .year-text {
              font-size: 24rpx;
              color: #3c3d42;
              font-weight: 500;
            }
          }

          // 招聘人数列
          &.table-2 {
            width: 120rpx;

            .data-text {
              color: #3c3d42;
              font-size: 24rpx;
            }
          }

          // 报名人数列
          &.table-3 {
            width: 120rpx;

            .data-text {
              color: #3c3d42;
              font-size: 24rpx;
            }
          }

          // 报录比列
          &.table-4 {
            width: 100rpx;

            .data-text {
              color: #3c3d42;
              font-size: 24rpx;
            }
          }

          // 最低进面分列
          &.table-5 {
            width: 120rpx;

            .data-text {
              color: #3c3d42;
              font-size: 24rpx;
            }
          }
        }
      }
    }
  }
}
.orange {
  color: #ff6a4d !important;
}
.red {
  color: #e60003 !important;
}
.mb0 {
  margin-bottom: 0 !important;
}
