.scroll-x {
  white-space: nowrap;

  .scroll-item {
    display: inline-block;
  }
}

.cmpt-tabs {
  .tab-nav {
    box-sizing: border-box;

    &.flex_center {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .nav-item {
      position: relative;
      flex: 1;
      line-height: 56rpx;
      background: #f7f8fa;
      border: 1rpx solid #f7f8fa;
      font-size: 26rpx;
      color: #666666;
      padding: 0 32rpx;
      border-radius: 28rpx;
      & + .nav-item {
        margin-left: 12rpx;
      }

      &.active {
        background: rgba(#d62828, 0.05);
        border-radius: 28rpx;
        border: 1rpx solid rgba(#d62828, 0.5);
        color: #d62828;
      }
    }
  }
}
