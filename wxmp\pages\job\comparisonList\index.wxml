<wxs src="/utils/wxs/jobUtils.wxs" module="jobUtils" />

<view class="comparison-list">

  <view class="top-card" bindtap="onNavigateToAddJob">
    <image class="icon" src="{{IMAGE_PREFIX}}/job/comparison/add.png" mode="" />
    <view class="text">从关注职位中添加</view>
  </view>

  <view class="card-area">
    <comparison-card wx:for="{{jobList}}" wx:key="id" dataItem="{{item}}" bind:select="onCardSelect" bind:delete="onCardDelete">
    </comparison-card>
  </view>

  <!-- 底部操作区域 -->
  <tabbar-box>
    <view class="bottom-area">
      <view class="left" bindtap="onToggleSelectAll">
        <image class="icon" src="{{IMAGE_PREFIX}}/job/comparison/{{isAllSelected ? 'selected' : 'select'}}.png" mode="" />
        <view class="text">{{isAllSelected ? '取消全选' : '全选'}}</view>
      </view>
      <view class="btn {{jobUtils.hasSelected(jobList) ? 'active' : ''}}" bindtap="onCompare">
        {{jobUtils.getFullCompareButtonText(jobList)}}
      </view>
    </view>
  </tabbar-box>
</view>
<modal-default show="{{modalShow}}" title="是否要移除该职位" bind:confirm="confirmDelete" bind:cancel="cancelDelete"></modal-default>