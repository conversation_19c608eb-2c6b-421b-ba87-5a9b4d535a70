import Cookies from "js-cookie"

/**
 * 基础 Cookie 操作工具函数
 * 基于 js-cookie 库的封装，提供统一的 cookie 操作接口
 *
 * 注意：这是核心工具函数，不要直接导入使用
 * 请通过 @/utils/cookies 统一入口导入
 */

/**
 * 设置 Cookie
 * @param {string} name - Cookie 名称
 * @param {string|number|object} value - Cookie 值
 * @param {Object} options - Cookie 选项
 * @param {number} options.expires - 过期时间（天数）
 * @param {string} options.path - 路径，默认为 '/'
 * @param {string} options.domain - 域名
 * @param {boolean} options.secure - 是否仅在 HTTPS 下传输
 * @param {string} options.sameSite - SameSite 属性
 * @returns {string|undefined} 设置成功返回 cookie 字符串，失败返回 undefined
 */
export function setCookie(name, value, options = {}) {
  try {
    // 默认选项
    const defaultOptions = {
      path: "/",
      expires: 7, // 默认7天过期
    }

    // 合并选项
    const finalOptions = { ...defaultOptions, ...options }

    // 如果值是对象，转换为 JSON 字符串
    const finalValue = typeof value === "object" ? JSON.stringify(value) : String(value)

    return Cookies.set(name, finalValue, finalOptions)
  } catch (error) {
    console.error(`设置 Cookie 失败: ${name}`, error)
    return undefined
  }
}

/**
 * 获取 Cookie
 * @param {string} name - Cookie 名称
 * @param {boolean} parseJson - 是否尝试解析为 JSON，默认 false
 * @returns {string|object|undefined} Cookie 值，不存在返回 undefined
 */
export function getCookie(name, parseJson = false) {
  try {
    const value = Cookies.get(name)

    if (value === undefined) {
      return undefined
    }

    // 如果需要解析 JSON
    if (parseJson) {
      try {
        return JSON.parse(value)
      } catch (parseError) {
        console.warn(`解析 Cookie JSON 失败: ${name}`, parseError)
        return value
      }
    }

    return value
  } catch (error) {
    console.error(`获取 Cookie 失败: ${name}`, error)
    return undefined
  }
}

/**
 * 删除 Cookie
 * @param {string} name - Cookie 名称
 * @param {Object} options - 删除选项
 * @param {string} options.path - 路径，默认为 '/'
 * @param {string} options.domain - 域名
 * @returns {boolean} 删除成功返回 true
 */
export function delCookie(name, options = {}) {
  try {
    const defaultOptions = {
      path: "/",
    }

    const finalOptions = { ...defaultOptions, ...options }
    Cookies.remove(name, finalOptions)
    return true
  } catch (error) {
    console.error(`删除 Cookie 失败: ${name}`, error)
    return false
  }
}

/**
 * 检查 Cookie 是否存在
 * @param {string} name - Cookie 名称
 * @returns {boolean} 存在返回 true，不存在返回 false
 */
export function hasCookie(name) {
  return getCookie(name) !== undefined
}

/**
 * 获取所有 Cookie
 * @returns {Object} 包含所有 Cookie 的对象
 */
export function getAllCookies() {
  try {
    return Cookies.get()
  } catch (error) {
    console.error("获取所有 Cookie 失败", error)
    return {}
  }
}

/**
 * 清除所有 Cookie
 * @param {Array<string>} excludeNames - 排除的 Cookie 名称列表
 * @returns {boolean} 清除成功返回 true
 */
export function clearAllCookies(excludeNames = []) {
  try {
    const allCookies = getAllCookies()

    Object.keys(allCookies).forEach((name) => {
      if (!excludeNames.includes(name)) {
        delCookie(name)
      }
    })

    return true
  } catch (error) {
    console.error("清除所有 Cookie 失败", error)
    return false
  }
}

/**
 * 设置会话 Cookie（浏览器关闭时删除）
 * @param {string} name - Cookie 名称
 * @param {string|number|object} value - Cookie 值
 * @param {Object} options - Cookie 选项
 * @returns {string|undefined} 设置成功返回 cookie 字符串
 */
export function setSessionCookie(name, value, options = {}) {
  // 不设置 expires，使其成为会话 cookie
  const { expires, ...otherOptions } = options
  return setCookie(name, value, otherOptions)
}

/**
 * 设置长期 Cookie（30天过期）
 * @param {string} name - Cookie 名称
 * @param {string|number|object} value - Cookie 值
 * @param {Object} options - Cookie 选项
 * @returns {string|undefined} 设置成功返回 cookie 字符串
 */
export function setPersistentCookie(name, value, options = {}) {
  return setCookie(name, value, { ...options, expires: 30 })
}
