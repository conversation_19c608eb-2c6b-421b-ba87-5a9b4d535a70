include:
  - project: 'tiku/maintain/cicd-template'
    file: '/api-template.yml'
    ref: HEAD

variables:
  SERVICE_TYPE: http-vue                                                # 服务类型: [http-swoft|rpc-swoft|http-fpm|http-vue|cli-swoft]
  NAMESPACE: training-3                                                 # k8s命名空间
  SERVICE_PORT: 80                                                      # 服务内部端口号
  URI_PREFIX: "/position"                                                    # URI路径前缀
  #----------------测试环境-------------------------------------------------------------------------------------------------------
  #注意：多域名下如果有域名启用https，则主域名必须开启https
  TEST_DOMAIN: "share.test.shikaobang.cn"                               # 公网域名,内部服务留空
  TEST_DOMAIN_ALIAS: ""                                                 # 公网域名别名配置, 内部服务留空: <domain1>,<domain2>...
  TEST_DOMAIN_ENABLE_HTTPS: "true"                                      # 公网是否启用https,内部服务忽略: ["true"|"false"]
  TEST_HTTPS_SECRET_NAME: "test.shikaobang.cn"                          # 公网https证书secret名称,非https应用忽略
  TEST_DOMAIN1: "share.test.guopeichina.com"                            # 公网副域名1配置,内部服务留空
  TEST_DOMAIN1_ENABLE_HTTPS: "true"                                     # 公网副域名1是否开起https,内部服务忽略: ["true"|"false"]
  TEST_DOMAIN1_HTTPS_SECRET_NAME: "test.guopeichina.com"                # 公网副域名1证书secret名称,非https应用忽略
  TEST_DOMAIN2: "share.test.jbcgk.com"                                  # 公网副域名2配置,内部服务留空
  TEST_DOMAIN2_ENABLE_HTTPS: "true"                                     # 公网副域名2是否开起https,内部服务忽略: ["true"|"false"]
  TEST_DOMAIN2_HTTPS_SECRET_NAME: "test.jbcgk.com"                      # 公网副域名2证书secret名称,非https应用忽略
  TEST_DOMAIN3: "share.test.xuandiaobang.com"                           # 公网副域名3配置,内部服务留空
  TEST_DOMAIN3_ENABLE_HTTPS: "true"                                     # 公网副域名3是否开起https,内部服务忽略: ["true"|"false"]
  TEST_DOMAIN3_HTTPS_SECRET_NAME: "test.xuandiaobang.com"               # 公网副域名3证书secret名称,非https应用忽略
  TEST_DOMAIN4: "share.test.jbcjiaoshi.com"                             # 公网副域名4配置,内部服务留空
  TEST_DOMAIN4_ENABLE_HTTPS: "true"                                     # 公网副域名4是否开起https,内部服务忽略: ["true"|"false"]
  TEST_DOMAIN4_HTTPS_SECRET_NAME: "test.jbcjiaoshi.com"                 # 公网副域名4证书secret名称,非https应用忽略
  TEST_DOMAIN5: "share.test.jbczsb.com"                                 # 公网副域名5配置,内部服务留空
  TEST_DOMAIN5_ENABLE_HTTPS: "true"                                     # 公网副域名5是否开起https,内部服务忽略: ["true"|"false"]
  TEST_DOMAIN5_HTTPS_SECRET_NAME: "test.jbczsb.com"                     # 公网副域名5证书secret名称,非https应用忽略
  TEST_DOMAIN6: "share.test.yianjiaoyu.com"                             # 公网副域名6配置,内部服务留空
  TEST_DOMAIN6_ENABLE_HTTPS: "true"                                     # 公网副域名6是否开起https,内部服务忽略: ["true"|"false"]
  TEST_DOMAIN6_HTTPS_SECRET_NAME: "test.yianjiaoyu.com"                 # 公网副域名6证书secret名称,非https应用忽略
  TEST_DOMAIN7: "share.test.jbcshangan.com"                             # 公网副域名7配置,内部服务留空
  TEST_DOMAIN7_ENABLE_HTTPS: "true"                                     # 公网副域名7是否开起https,内部服务忽略: ["true"|"false"]
  TEST_DOMAIN7_HTTPS_SECRET_NAME: "test.jbcshangan"                     # 公网副域名7证书secret名称,非https应用忽略
  TEST_REQUEST_CPU: 10m                                                 # CPU需求
  TEST_REQUEST_MEM: 30Mi                                                # 内存需求
  TEST_LIMIT_CPU: 1000m                                                 # CPU使用上限
  TEST_LIMIT_MEM: 2Gi                                                   # 内存使用上限
  TEST_REPLICAS: 1                                                      # 副本数
  TEST_INGRESS_CLASS: nginx-external                                    # 使用哪一个nginx-ingress-controller作为公网流量入口,内部服务忽略

  #-----------------生产环境------------------------------------------------------------------------------------------------------
  #注意：多域名下如果有域名启用https，则主域名必须开启https
  PROD_DOMAIN: "share.shikaobang.cn"                                    # 公网域名，内部服务留空
  PROD_DOMAIN_ALIAS: ""                                                 # 公网域名别名配置, 内部服务留空: <domain1>,<domain2>...
  PROD_DOMAIN_ENABLE_HTTPS: "true"                                      # 公网是否启用https,内部服务忽略: ["true"|"false"]
  PROD_HTTPS_SECRET_NAME: "shikaobang.cn"                               # 公网https证书secret名称,非https应用忽略
  PROD_DOMAIN1: "share.guopeichina.com"                                 # 公网副域名1配置,内部服务留空
  PROD_DOMAIN1_ENABLE_HTTPS: "true"                                     # 公网副域名1是否启用https,内部服务忽略: ["true"|"false"]
  PROD_DOMAIN1_HTTPS_SECRET_NAME: "guopeichina.com"                     # 公网副域名1证书secret名称,非https应用忽略
  PROD_DOMAIN2: "share.jbcgk.com"                                       # 公网副域名2配置,内部服务留空
  PROD_DOMAIN2_ENABLE_HTTPS: "true"                                     # 公网副域名2是否启用https,内部服务忽略: ["true"|"false"]
  PROD_DOMAIN2_HTTPS_SECRET_NAME: "jbcgk.com"                           # 公网副域名2证书secret名称,非https应用忽略
  PROD_DOMAIN3: "share.xuandiaobang.com"                                # 公网副域名3配置,内部服务留空
  PROD_DOMAIN3_ENABLE_HTTPS: "true"                                     # 公网副域名3是否启用https,内部服务忽略: ["true"|"false"]
  PROD_DOMAIN3_HTTPS_SECRET_NAME: "xuandiaobang.com"                    # 公网副域名3证书secret名称,非https应用忽略
  PROD_DOMAIN4: "share.jbcjiaoshi.com"                                  # 公网副域名4配置,内部服务留空
  PROD_DOMAIN4_ENABLE_HTTPS: "true"                                     # 公网副域名4是否启用https,内部服务忽略: ["true"|"false"]
  PROD_DOMAIN4_HTTPS_SECRET_NAME: "jbcjiaoshi.com"                      # 公网副域名4证书secret名称,非https应用忽略
  PROD_DOMAIN5: "share.jbczsb.com"                                      # 公网副域名5配置,内部服务留空
  PROD_DOMAIN5_ENABLE_HTTPS: "true"                                     # 公网副域名5是否启用https,内部服务忽略: ["true"|"false"]
  PROD_DOMAIN5_HTTPS_SECRET_NAME: "jbczsb.com"                          # 公网副域名5证书secret名称,非https应用忽略
  PROD_DOMAIN6: "share.yianjiaoyu.com"                                  # 公网副域名6配置,内部服务留空
  PROD_DOMAIN6_ENABLE_HTTPS: "true"                                     # 公网副域名6是否启用https,内部服务忽略: ["true"|"false"]
  PROD_DOMAIN6_HTTPS_SECRET_NAME: "yianjiaoyu.com"                      # 公网副域名6证书secret名称,非https应用忽略
  PROD_DOMAIN7: "share.jbcshangan.com"                                  # 公网副域名7配置,内部服务留空
  PROD_DOMAIN7_ENABLE_HTTPS: "true"                                     # 公网副域名7是否启用https,内部服务忽略: ["true"|"false"]
  PROD_DOMAIN7_HTTPS_SECRET_NAME: "jbcshangan.com"                      # 公网副域名7证书secret名称,非https应用忽略
  PROD_REQUEST_CPU: 250m                                                # CPU需求
  PROD_REQUEST_MEM: 500Mi                                               # 内存需求
  PROD_LIMIT_CPU: 1000m                                                 # CPU使用上限
  PROD_LIMIT_MEM: 2Gi                                                   # 内存使用上限
  PROD_REPLICAS: 1                                                      # 副本数
  PROD_INGRESS_CLASS: nginx-external-training-3                         # 使用哪一个nginx-ingress-controller作为公网流量入口,内部服务忽略

build:
  stage: build
  script:
    - |
      if [ "${SERVICE_TYPE}" == "http-vue" ]; then
          sudo docker build -t ccr.ccs.tencentyun.com/tiku/${CI_PROJECT_NAME}:${CI_COMMIT_SHORT_SHA} --build-arg uri_prefix=${URI_PREFIX} -f ./k8s/Dockerfile-${SERVICE_TYPE} .
      fi
      sudo docker push ccr.ccs.tencentyun.com/tiku/${CI_PROJECT_NAME}:${CI_COMMIT_SHORT_SHA}
  tags:
    - default
  only:
    refs:
      - master
      - /^issue-.*/






