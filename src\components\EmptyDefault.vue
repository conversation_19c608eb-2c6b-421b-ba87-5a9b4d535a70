<template>
  <div class="empty-container" :style="{'height': isNewStyle?'auto':'100vh','background': isNewStyle?'#f7f8fa':''}">
    <img :src="imageUrl" :alt="text" />
    <div class="text">{{ text }}</div>
    <OpenApp v-if="showButton && !isInAPP" :options="openAppOptions" class="back-btn">
      {{ buttonText }}
    </OpenApp>
    <div class="back-btn" v-if="showButton && isInAPP" @click="backPage">返回</div>
  </div>
</template>

<script setup>
import OpenApp from "@/components/OpenApp.vue"
import { computed } from "vue"
import { isDeviceAppClientCookie } from "@/utils/cookies/modules/appDevice.js"
import { closeAppWebview } from "@/utils/appDevice.js"


const isInAPP = computed(() => isDeviceAppClientCookie())
// Props 定义
const props = defineProps({
  // 图片地址
  imageUrl: {
    type: String,
    default:
      "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_h5_deafault.png",
  },
  // 显示文本
  text: {
    type: String,
    default: "内容走丢啦，去看看别的公告吧",
  },
  // 是否显示按钮
  showButton: {
    type: Boolean,
    default: true,
  },
  // 按钮文本
  buttonText: {
    type: String,
    default: "返回APP",
  },
  // OpenApp 组件的 options 参数
  openAppOptions: {
    type: Object,
    default: () => ({}),
  },
  isNewStyle: {
    type: Boolean,
    default: false,
  }
})

const backPage = () => {
  closeAppWebview()
}
</script>

<style lang="scss" scoped>
.empty-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  img {
    width: 4rem;
  }

  .text {
    font-size: 0.35rem;
    color: rgba(145, 148, 153, 1);
  }

  .back-btn {
    width: 5.33rem;
    height: 1.12rem;
    background: rgba(230, 0, 3, 1);
    color: #fff;
    font-size: 0.37rem;
    border-radius: 0.21rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 1.07rem;
  }
}
</style>
