# 优先级 B 规则：强烈推荐 {#priority-b-rules-strongly-recommended}

::: warning 注意
此 Vue.js 风格指南已过时，需要重新审查。如果您有任何问题或建议，请[提交 issue](https://github.com/vuejs/docs/issues/new)。
:::

在大多数项目中，这些规则已被发现可以提高可读性和/或开发者体验。如果您违反了它们，您的代码仍然可以运行，但违反应该很少见并且有充分的理由。

## 组件文件 {#component-files}

**只要有构建系统可以连接文件，每个组件都应该在自己的文件中。**

这有助于您在需要编辑组件或查看如何使用它时更快地找到组件。

<div class="style-example style-example-bad">
<h3>不好</h3>

```js
app.component("TodoList", {
  // ...
})

app.component("TodoItem", {
  // ...
})
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```
components/
|- TodoList.js
|- TodoItem.js
```

```
components/
|- TodoList.vue
|- TodoItem.vue
```

</div>

## 单文件组件文件名大小写 {#single-file-component-filename-casing}

**[单文件组件](/guide/scaling-up/sfc)的文件名应该要么始终是 PascalCase，要么始终是 kebab-case。**

PascalCase 在代码编辑器中的自动补全效果最好，因为它与我们在 JS(X) 和模板中引用组件的方式一致（尽可能）。然而，混合大小写的文件名有时会在不区分大小写的文件系统上产生问题，这就是为什么 kebab-case 也是完全可以接受的。

<div class="style-example style-example-bad">
<h3>不好</h3>

```
components/
|- mycomponent.vue
```

```
components/
|- myComponent.vue
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```
components/
|- MyComponent.vue
```

```
components/
|- my-component.vue
```

</div>

## 基础组件名 {#base-component-names}

**基础组件（也就是展示性的、无逻辑的或无状态的组件）应该全部以一个特定的前缀开头，比如 `Base`、`App` 或 `V`。**

::: details 详细说明
这些组件为您的应用程序中一致的样式和行为奠定了基础。它们可能**只**包含：

- HTML 元素，
- 其他基础组件，以及
- 第三方 UI 组件。

但它们**永远不会**包含全局状态（例如来自 [Pinia](https://pinia.vuejs.org/) store）。

它们的名字通常包含所包装元素的名字（例如 `BaseButton`、`BaseTable`），除非没有元素存在于它们特定的目的（例如 `BaseIcon`）。如果您为更特定的上下文构建类似的组件，它们几乎总是会使用这些组件（例如 `BaseButton` 可能在 `ButtonSubmit` 中使用）。

这个约定的一些优势：

- 当在编辑器中按字母顺序组织时，您应用的基础组件会全部列在一起，使它们更容易识别。

- 由于组件名应该始终是多词的，这个约定防止您不得不为简单的组件包装器选择任意的前缀（例如 `MyButton`、`VueButton`）。

- 由于这些组件使用得如此频繁，您可能希望简单地使它们成为全局的，而不是到处导入它们。前缀使这在 Webpack 中成为可能：

  ```js
  const requireComponent = require.context("./src", true, /Base[A-Z]\w+\.(vue|js)$/)
  requireComponent.keys().forEach(function (fileName) {
    let baseComponentConfig = requireComponent(fileName)
    baseComponentConfig = baseComponentConfig.default || baseComponentConfig
    const baseComponentName =
      baseComponentConfig.name || fileName.replace(/^.+\//, "").replace(/\.\w+$/, "")
    app.component(baseComponentName, baseComponentConfig)
  })
  ```

  :::

<div class="style-example style-example-bad">
<h3>不好</h3>

```
components/
|- MyButton.vue
|- VueTable.vue
|- Icon.vue
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```
components/
|- BaseButton.vue
|- BaseTable.vue
|- BaseIcon.vue
```

```
components/
|- AppButton.vue
|- AppTable.vue
|- AppIcon.vue
```

```
components/
|- VButton.vue
|- VTable.vue
|- VIcon.vue
```

</div>

## 紧密耦合的组件名 {#tightly-coupled-component-names}

**和父组件紧密耦合的子组件应该以父组件名作为前缀命名。**

如果一个组件只在某个父组件的场景下有意义，这层关系应该体现在其名字上。因为编辑器通常会按字母顺序组织文件，所以这样做可以把相关联的文件排在一起。

::: details 详细说明
您可能会倾向于通过将子组件嵌套在以其父组件命名的目录中来解决这个问题。例如：

```
components/
|- TodoList/
   |- Item/
      |- index.vue
      |- Button.vue
   |- index.vue
```

或者：

```
components/
|- TodoList/
   |- Item/
      |- Button.vue
   |- Item.vue
|- TodoList.vue
```

不推荐这样做，因为这会导致：

- 许多文件的名字相似，使得在代码编辑器中快速切换文件变得困难。
- 过多的嵌套子目录，增加了在编辑器侧边栏中浏览组件所花的时间。
  :::

<div class="style-example style-example-bad">
<h3>不好</h3>

```
components/
|- TodoList.vue
|- TodoItem.vue
|- TodoButton.vue
```

```
components/
|- SearchSidebar.vue
|- NavigationForSearchSidebar.vue
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```
components/
|- TodoList.vue
|- TodoListItem.vue
|- TodoListItemButton.vue
```

```
components/
|- SearchSidebar.vue
|- SearchSidebarNavigation.vue
```

</div>

## 组件名中的单词顺序 {#order-of-words-in-component-names}

**组件名应该以高级别的（通常是一般化描述的）单词开头，以描述性的修饰词结尾。**

::: details 详细说明
您可能会想：

> "为什么我们要强制组件名使用不太自然的语言？"

在自然英语中，形容词和其他描述符通常出现在名词之前，而例外情况需要连接词。例如：

- Coffee _with_ milk
- Soup _of the_ day
- Visitor _to the_ museum

如果您愿意，您绝对可以在组件名中包含这些连接词，但顺序仍然很重要。

还要注意，**什么被认为是"最高级别"将取决于您的应用程序的上下文**。例如，想象一个带有搜索表单的应用程序。它可能包含这样的组件：

```
components/
|- ClearSearchButton.vue
|- ExcludeFromSearchInput.vue
|- LaunchOnStartupCheckbox.vue
|- RunSearchButton.vue
|- SearchInput.vue
|- TermsCheckbox.vue
```

正如您可能注意到的，很难看出哪些组件是特定于搜索的。现在让我们根据规则重命名组件：

```
components/
|- SearchButtonClear.vue
|- SearchButtonRun.vue
|- SearchInputExcludeGlob.vue
|- SearchInputQuery.vue
|- SettingsCheckboxLaunchOnStartup.vue
|- SettingsCheckboxTerms.vue
```

由于编辑器通常按字母顺序组织文件，组件之间的所有重要关系现在一目了然。

您可能会想以不同的方式解决这个问题，将所有搜索组件嵌套在"search"目录下，然后将所有设置组件嵌套在"settings"目录下。我们只建议在非常大的应用程序（例如 100+ 个组件）中考虑这种方法，原因如下：

- 通过嵌套子目录导航通常比滚动浏览单个 `components` 目录需要更多时间。
- 名称冲突（例如多个 `ButtonDelete.vue` 组件）使得在代码编辑器中快速导航到特定组件变得更加困难。
- 重构变得更加困难，因为查找和替换通常不足以更新对移动组件的相对引用。
  :::

<div class="style-example style-example-bad">
<h3>不好</h3>

```
components/
|- ClearSearchButton.vue
|- ExcludeFromSearchInput.vue
|- LaunchOnStartupCheckbox.vue
|- RunSearchButton.vue
|- SearchInput.vue
|- TermsCheckbox.vue
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```
components/
|- SearchButtonClear.vue
|- SearchButtonRun.vue
|- SearchInputQuery.vue
|- SearchInputExcludeGlob.vue
|- SettingsCheckboxTerms.vue
|- SettingsCheckboxLaunchOnStartup.vue
```

</div>

## 自闭合组件 {#self-closing-components}

**在[单文件组件](/guide/scaling-up/sfc)、字符串模板和 [JSX](/guide/extras/render-function#jsx-tsx) 中没有内容的组件应该是自闭合的——但在 DOM 内模板里永远不要这样做。**

自闭合组件表示它们不仅没有内容，而且**刻意**没有内容。其不同之处就好像书上的一页白纸对比贴有"本页有意留白"标签的白纸。而且没有了额外的闭合标签，你的代码也更简洁。

不幸的是，HTML 并不支持自闭合的自定义元素——只有[官方的"空"元素](https://www.w3.org/TR/html/syntax.html#void-elements)。所以这个策略仅适用于进入 DOM 之前 Vue 的模板编译器能够触达的地方，然后再产出符合 DOM 规范的 HTML。

<div class="style-example style-example-bad">
<h3>不好</h3>

```vue-html
<!-- 在单文件组件、字符串模板和 JSX 中 -->
<MyComponent></MyComponent>
```

```vue-html
<!-- 在 DOM 内模板中 -->
<my-component/>
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```vue-html
<!-- 在单文件组件、字符串模板和 JSX 中 -->
<MyComponent/>
```

```vue-html
<!-- 在 DOM 内模板中 -->
<my-component></my-component>
```

</div>

## 模板中的组件名大小写 {#component-name-casing-in-templates}

**在大多数项目中，组件名应该在[单文件组件](/guide/scaling-up/sfc)和字符串模板中总是 PascalCase 的——但是在 DOM 内模板中总是 kebab-case 的。**

PascalCase 相比于 kebab-case 有一些优势：

- 编辑器可以在模板里自动补全组件名，因为 PascalCase 同样适用于 JavaScript。
- `<MyComponent>` 视觉上比 `<my-component>` 更能够和单个单词的 HTML 元素区别开来，因为前者的不同之处有两个大写字母，后者只有一个横线。
- 如果你在模板中使用任何非 Vue 的自定义元素，比如一个 Web Component，PascalCase 确保了你的 Vue 组件在视觉上仍然能够被明显区分出来。

不幸的是，由于 HTML 是大小写不敏感的，在 DOM 模板中必须仍使用 kebab-case。

还请注意，如果你已经是 kebab-case 的重度用户，那么与 HTML 保持一致的命名约定且在多个项目中保持相同的大小写规则就可能比上述优势更为重要了。在这些情况下，**在所有的地方都使用 kebab-case 同样是可以接受的。**

<div class="style-example style-example-bad">
<h3>不好</h3>

```vue-html
<!-- 在单文件组件和字符串模板中 -->
<mycomponent/>
```

```vue-html
<!-- 在单文件组件和字符串模板中 -->
<myComponent/>
```

```vue-html
<!-- 在 DOM 内模板中 -->
<MyComponent></MyComponent>
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```vue-html
<!-- 在单文件组件和字符串模板中 -->
<MyComponent/>
```

```vue-html
<!-- 在 DOM 内模板中 -->
<my-component></my-component>
```

或者

```vue-html
<!-- 在所有地方 -->
<my-component></my-component>
```

</div>

## JS/JSX 中的组件名大小写 {#component-name-casing-in-js-jsx}

**JS/[JSX](/guide/extras/render-function#jsx-tsx) 中的组件名应该始终是 PascalCase 的，尽管在较为简单的应用中只使用 `app.component` 进行全局组件注册时，可以使用 kebab-case 字符串。**

::: details 详细说明
在 JavaScript 中，PascalCase 是类和原型构造函数的约定——本质上，任何可以产生多个不同实例的东西。Vue 组件也有多个实例，所以同样使用 PascalCase 是有意义的。另外，在 JSX（和模板）中使用 PascalCase 使得代码的读者更容易区分组件和 HTML 元素。

然而，对于**只**通过 `app.component` 定义全局组件的应用来说，我们推荐 kebab-case 作为替代。原因如下：

- 全局组件很少在 JavaScript 中引用，所以遵循 JavaScript 的约定意义不大。
- 这些应用往往包含许多 DOM 内的模板，这种情况下[kebab-case **必须**使用](#component-name-casing-in-templates)。
  :::

<div class="style-example style-example-bad">
<h3>不好</h3>

```js
app.component("myComponent", {
  // ...
})
```

```js
import myComponent from "./MyComponent.vue"
```

```js
export default {
  name: "myComponent",
  // ...
}
```

```js
export default {
  name: "my-component",
  // ...
}
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```js
app.component("MyComponent", {
  // ...
})
```

```js
app.component("my-component", {
  // ...
})
```

```js
import MyComponent from "./MyComponent.vue"
```

```js
export default {
  name: "MyComponent",
  // ...
}
```

</div>

## 完整单词的组件名 {#full-word-component-names}

**组件名应该倾向于完整单词而不是缩写。**

编辑器中的自动补全已经让书写长命名的代价非常之低了，而其带来的明确性却是非常宝贵的。不常用的缩写尤其应该避免。

<div class="style-example style-example-bad">
<h3>不好</h3>

```
components/
|- SdSettings.vue
|- UProfOpts.vue
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```
components/
|- StudentDashboardSettings.vue
|- UserProfileOptions.vue
```

</div>

## Prop 名大小写 {#prop-name-casing}

**在声明 prop 的时候，其命名应该始终使用 camelCase，而在模板和 [JSX](/guide/extras/render-function#jsx-tsx) 中应该始终使用 kebab-case。我们单纯的遵循每个语言的约定。在 JavaScript 中更自然的是 camelCase。而在 HTML 中则是 kebab-case。**

<div class="style-example style-example-bad">
<h3>不好</h3>

<div class="options-api">

```js
props: {
  'greeting-text': String
}
```

</div>

<div class="composition-api">

```js
const props = defineProps({
  "greeting-text": String,
})
```

</div>

```vue-html
// 对于 DOM 内模板
<welcome-message greetingText="hi"></welcome-message>
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

<div class="options-api">

```js
props: {
  greetingText: String
}
```

</div>

<div class="composition-api">

```js
const props = defineProps({
  greetingText: String,
})
```

</div>

```vue-html
// 对于单文件组件 - 请确保在整个项目中大小写保持一致
// 您可以使用任一约定，但我们不建议混合使用两种不同的大小写风格
<WelcomeMessage greeting-text="hi"/>
// 或者
<WelcomeMessage greetingText="hi"/>
```

```vue-html
// 对于 DOM 内模板
<welcome-message greeting-text="hi"></welcome-message>
```

</div>

## 多个 attribute 的元素 {#multi-attribute-elements}

**多个 attribute 的元素应该分多行撰写，每个 attribute 一行。**

在 JavaScript 中，用多行分隔对象的多个 property 是很常见的最佳实践，因为这样更易读。模板和 [JSX](/guide/extras/render-function#jsx-tsx) 值得我们做相同的考虑。

<div class="style-example style-example-bad">
<h3>不好</h3>

```vue-html
<img src="https://vuejs.org/images/logo.png" alt="Vue Logo">
```

```vue-html
<MyComponent foo="a" bar="b" baz="c"/>
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```vue-html
<img
  src="https://vuejs.org/images/logo.png"
  alt="Vue Logo"
>
```

```vue-html
<MyComponent
  foo="a"
  bar="b"
  baz="c"
/>
```

</div>

## 模板中简单的表达式 {#simple-expressions-in-templates}

**组件模板应该只包含简单的表达式，复杂的表达式则应该重构为计算属性或方法。**

复杂表达式会让你的模板变得不那么声明式。我们应该努力描述应该出现的*是什么*，而非*如何*计算那个值。而且计算属性和方法使得代码可以重用。

<div class="style-example style-example-bad">
<h3>不好</h3>

```vue-html
{{
  fullName.split(' ').map((word) => {
    return word[0].toUpperCase() + word.slice(1)
  }).join(' ')
}}
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```vue-html
<!-- 在模板中 -->
{{ normalizedFullName }}
```

<div class="options-api">

```js
// 复杂表达式已经移入一个计算属性
computed: {
  normalizedFullName() {
    return this.fullName.split(' ')
      .map(word => word[0].toUpperCase() + word.slice(1))
      .join(' ')
  }
}
```

</div>

<div class="composition-api">

```js
// 复杂表达式已经移入一个计算属性
const normalizedFullName = computed(() =>
  fullName.value
    .split(" ")
    .map((word) => word[0].toUpperCase() + word.slice(1))
    .join(" ")
)
```

</div>

</div>

## 简单的计算属性 {#simple-computed-properties}

**复杂的计算属性应该分割为尽可能多的更简单的 property。**

::: details 详细说明
更简单、命名得当的计算属性是这样的：

- **更易于测试**

  当每个计算属性都包含一个非常简单且很少依赖的表达式时，撰写测试以确定其正确工作会更加容易。

- **更易于阅读**

  Simplifying computed properties forces you to give each value a descriptive name, even if it's not reused. This makes it much easier for other developers (and future you) to focus in on the code they care about and figure out what's going on.

- **More adaptable to changing requirements**

  Any value that can be named might be useful to the view. For example, we might decide to display a message telling the user how much money they saved. We might also decide to calculate sales tax, but perhaps display it separately, rather than as part of the final price.

  Small, focused computed properties make fewer assumptions about how information will be used, so require less refactoring as requirements change.
  :::

<div class="style-example style-example-bad">
<h3>Bad</h3>

<div class="options-api">

```js
computed: {
  price() {
    const basePrice = this.manufactureCost / (1 - this.profitMargin)
    return (
      basePrice -
      basePrice * (this.discountPercent || 0)
    )
  }
}
```

</div>

<div class="composition-api">

```js
const price = computed(() => {
  const basePrice = manufactureCost.value / (1 - profitMargin.value)
  return basePrice - basePrice * (discountPercent.value || 0)
})
```

</div>

</div>

<div class="style-example style-example-good">
<h3>Good</h3>

<div class="options-api">

```js
computed: {
  basePrice() {
    return this.manufactureCost / (1 - this.profitMargin)
  },

  discount() {
    return this.basePrice * (this.discountPercent || 0)
  },

  finalPrice() {
    return this.basePrice - this.discount
  }
}
```

</div>

<div class="composition-api">

```js
const basePrice = computed(() => manufactureCost.value / (1 - profitMargin.value))

const discount = computed(() => basePrice.value * (discountPercent.value || 0))

const finalPrice = computed(() => basePrice.value - discount.value)
```

</div>

</div>

## Quoted attribute values {#quoted-attribute-values}

**Non-empty HTML attribute values should always be inside quotes (single or double, whichever is not used in JS).**

While attribute values without any spaces are not required to have quotes in HTML, this practice often leads to _avoiding_ spaces, making attribute values less readable.

<div class="style-example style-example-bad">
<h3>Bad</h3>

```vue-html
<input type=text>
```

```vue-html
<AppSidebar :style={width:sidebarWidth+'px'}>
```

</div>

<div class="style-example style-example-good">
<h3>Good</h3>

```vue-html
<input type="text">
```

```vue-html
<AppSidebar :style="{ width: sidebarWidth + 'px' }">
```

</div>

## Directive shorthands {#directive-shorthands}

**Directive shorthands (`:` for `v-bind:`, `@` for `v-on:` and `#` for `v-slot`) should be used always or never.**

<div class="style-example style-example-bad">
<h3>Bad</h3>

```vue-html
<input
  v-bind:value="newTodoText"
  :placeholder="newTodoInstructions"
>
```

```vue-html
<input
  v-on:input="onInput"
  @focus="onFocus"
>
```

```vue-html
<template v-slot:header>
  <h1>Here might be a page title</h1>
</template>

<template #footer>
  <p>Here's some contact info</p>
</template>
```

</div>

<div class="style-example style-example-good">
<h3>Good</h3>

```vue-html
<input
  :value="newTodoText"
  :placeholder="newTodoInstructions"
>
```

```vue-html
<input
  v-bind:value="newTodoText"
  v-bind:placeholder="newTodoInstructions"
>
```

```vue-html
<input
  @input="onInput"
  @focus="onFocus"
>
```

```vue-html
<input
  v-on:input="onInput"
  v-on:focus="onFocus"
>
```

```vue-html
<template v-slot:header>
  <h1>Here might be a page title</h1>
</template>

<template v-slot:footer>
  <p>Here's some contact info</p>
</template>
```

```vue-html
<template #header>
  <h1>Here might be a page title</h1>
</template>

<template #footer>
  <p>Here's some contact info</p>
</template>
```

</div>
