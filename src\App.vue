<script setup>
import { RouterView } from "vue-router"
import { onMounted, ref } from "vue"
import { useBrandStore } from "@/stores/brand"
import { useWxSDKStore } from "@/stores/wxSDK"
import { getWxShareInfoRequest } from "@/api/user"
console.time("进入时间")

// 初始化品牌信息和微信SDK
const brandStore = useBrandStore()
const wxSDKStore = useWxSDKStore()

// 初始化微信SDK
const initWxSDK = async (brandKey) => {
  try {
    // 获取微信分享配置
    const res = await getWxShareInfoRequest({
      url: window.location.href,
      brand: brandKey,
    })

    // 使用 store 配置微信SDK
    await wxSDKStore.configWxSDK(res.data.package)
    return true
  } catch (error) {
    wxSDKStore.setInitSuccess(false)
    throw error
  }
}

function setFavicon(url) {
  let link = document.querySelector('link[rel*="icon"]') || document.createElement("link")
  link.type = "image/x-icon"
  link.rel = "shortcut icon"
  link.href = url
  document.getElementsByTagName("head")[0].appendChild(link)
}

onMounted(async () => {
  // 在组件挂载后初始化品牌信息
  brandStore.initBrand()

  // 从 brandStore 获取品牌信息
  const currentBrand = brandStore.brand
  const currentBrandImage = brandStore.image
  const currentBrandColor = brandStore.color

  // 设置网站图标
  if (currentBrandImage?.imageLogoURL) {
    setFavicon(currentBrandImage.imageLogoURL)
  }

  // 设置主题颜色CSS变量
  if (currentBrandColor) {
    document.documentElement.style.setProperty("--main-color", currentBrandColor.mainColor)
    document.documentElement.style.setProperty("--main-color-rgba", currentBrandColor.mainColorRgba)
  }

  // 测试服和本地显示打印
  // if (window.location.origin.indexOf('test') !== -1 || window.location.href.indexOf('localhost:') !== -1) {
  //   new VConsole()
  // }

  // 初始化微信SDK
  if (currentBrand?.brandKey) {
    try {
      await initWxSDK(currentBrand.brandKey)
      console.log("微信SDK初始化完成")
    } catch (error) {
      console.error("微信SDK初始化失败:", error)
    }
  } else {
    console.warn("未找到品牌信息，跳过微信SDK初始化")
  }
})
</script>

<template>
  <div id="app">
    <!-- 路由视图 -->
    <RouterView />
  </div>
</template>

<style lang="scss" scoped>
@font-face {
  font-family: DIN-Bold;
  src: url(./assets/fonts/DIN-Bold.otf);
}

@font-face {
  font-family: DIN;
  src: url(./assets/fonts/DIN-Medium.otf);
}

.wrapper {
  min-height: 100vh;
}

.wrapper,
.buy-button {
  // max-width: $maxWidth;
}

.text-color-main {
  color: var(--main-color) !important;
}

.van-button--primary {
  background-color: var(--main-color) !important;
  border-color: var(--main-color) !important;
}

.jbc-tabs {
  .van-tabs__line {
    background: var(--main-color);
  }
}

.vjs-poster {
  background-size: cover;
}

img {
  max-width: 100%;
}
</style>
