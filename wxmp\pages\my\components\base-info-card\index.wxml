<view class="base-info-card">
  <view class="title-area">
    <view class="title">基本信息</view>
    <view class="tips">{{incompleteTips}}</view>
  </view>
  <view class="form-item" wx:for="{{formList}}" wx:key="index" bindtap="onFormItemTap" data-field="{{item.field}}">
    <view class="left">
      {{item.title}}
      <text wx:if="{{item.required}}" class="cred">*</text>
    </view>
    <view class="right">
      <view class="text {{item.text ? 'has-value' : ''}}">{{item.text || '待完善'}}</view>
      <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
    </view>
  </view>

  <!-- 通用选择器 -->
  <region-selector show="{{selectorShow}}" type="{{selectorType}}" title="{{selectorTitle}}" value="{{selectorValue}}" bind:close="onSelectorClose" bind:confirm="onSelectorConfirm" bind:ready="onSelectorReady" />
</view>