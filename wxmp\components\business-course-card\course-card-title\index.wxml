<view class="course-card-title {{titleEllipsis===1?'text-ellipsis-1':'text-ellipsis-2'}}">
  <!-- 首页不显示标题标签 -->
  <block wx:if="{{showLabel}}">
    <view class="label-tip" wx:if="{{titlePrefix.name_prefix.img}}" style="background-color:{{titlePrefix.name_prefix.bg_color}};color:{{titlePrefix.name_prefix.font_color}}">
      <image class="image" src="{{titlePrefix.name_prefix.img}}"></image>
      <text>{{titlePrefix.name_prefix.text}}</text>
    </view>
  </block>
  <!-- 标题文本 -->
  <block wx:if="{{!isSearch}}">
    <text> {{title}}</text>
  </block>
  <!-- 搜索标题文本 -->
  <block wx:else>
    <text class="{{item.isRedText ? 'red-text' : ''}}" wx:for="{{textArr}}" wx:key="index">{{item.text}}</text>
  </block>
</view>
<view class="course-card-label-text">{{subTitle}}</view>