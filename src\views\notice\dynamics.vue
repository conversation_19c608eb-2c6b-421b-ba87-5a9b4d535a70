<template>
  <template v-if="isRequest">
    <div class="page-container" v-if="noticeData.title">
      <div class="content">
        <div class="main-content">
          <!-- 转换后的HTML代码 -->
          <div class="header-top">
            <div class="title">{{ noticeData.title }}</div>
            <div class="label">
              <span class="address" v-if="noticeData?.exam_type_name">
                {{ noticeData?.exam_type_name }}
              </span>
              <span
                class="time"
                v-if="noticeData?.release_time"
                :class="noticeData?.exam_type_name ? '' : 'pl0'"
              >
                {{ noticeData?.release_time }}
              </span>
            </div>
          </div>

          <!-- 公告详情 -->
          <div class="content-box">
            <div class="content-center">
              <div
                class="rich-text-box"
                :class="noticeData?.detail?.timeline?.length > 0 ? 'pr-box' : ''"
                v-if="noticeData?.content?.body_content"
              >
                <div
                  id="richTextContainer"
                  class="rich-text-container"
                  :class="{ expanded: isExpanded, collapsed: !isExpanded }"
                  :style="{ maxHeight: isExpanded ? 'none' : maxHeight + 'px', overflow: 'hidden' }"
                >
                  <div id="richTextContent" v-html="noticeData?.content?.body_content"></div>
                </div>
                <div v-if="showToggle" class="bottom-pos">
                  <div v-if="!isExpanded" class="gradient-height"></div>
                  <div class="see-all" :class="{ collapsed: !isExpanded }" @click="toggleText">
                    {{ isExpanded ? "收起" : "展开" }}查看全文
                    <img
                      src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_select.png"
                    />
                  </div>
                </div>
              </div>
              <div v-if="noticeData?.content?.attachment?.length > 0" class="attachment-box">
                <div class="title">附件</div>
                <div class="information-list">
                  <a
                    :href="item.url"
                    v-for="(item, index) in noticeData?.content?.attachment"
                    :key="index"
                    class="information-list-item"
                    @click="tapAttachment(item)"
                  >
                    <img
                      class="type-img"
                      src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_information_link.png"
                    />
                    <span class="text">{{ item.name }}</span>
                    <img
                      class="right-arrow"
                      src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_right_h.png"
                    />
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部操作栏 -->
          <div class="action-bar-box">
            <OpenApp :options="openAppOptions" class="action-bar container flex-justify_between">
              <div class="filter-button wp100">前往APP内查看更多考试咨询</div>
            </OpenApp>
          </div>
        </div>
      </div>
    </div>
    <EmptyDefault v-else text="内容走丢啦，去看看别的考试动态吧" :openAppOptions="openAppOptions" />
  </template>
  <!-- 悬浮咨询框 -->
  <FloatingBox :options="openAppOptions" v-if="!isInAPP && noticeData" />
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from "vue"
import { useRoute } from "vue-router"
import { isDeviceAppClientCookie } from "@/utils/cookies/modules/appDevice"
import FloatingBox from "@/components/SideOpenApp.vue"
import EmptyDefault from "@/components/EmptyDefault.vue"
// 导入API
import { getJobDetail } from "@/api/notice"
import OpenApp from "@/components/OpenApp.vue"
import { setShare } from "@/utils/share"

// 获取路由实例
const route = useRoute()

// 响应式数据
const noticeData = ref({})
const pageType = ref("")
const isExpanded = ref(false)
const showToggle = ref(false)
const maxHeight = ref(2500) // 默认收起状态的最大高度(px)
const actualHeight = ref(0) // 内容实际高度
const isRequest = ref(false)

const openAppOptions = ref({
  unitKey: "NoticeJobExamDynamicsDetail",
  param: {
    id: route.query.id,
  },
})

// 组件声明
defineOptions({
  components: {
    FloatingBox,
  },
})

const isInAPP = computed(() => isDeviceAppClientCookie())
// 初始化数据
const initData = async () => {
  // 从路由参数获取ID
  const id = route.query.id
  if (!id) {
    console.error("缺少文章ID参数")
    return
  }

  // 请求文章详情数据
  const response = await getJobDetail({
    id,
    brand: route.query.brand,
    province: route.query.province,
  })
  isRequest.value = true
  console.log("文章详情数据:", response)

  if (response && response.data) {
    noticeData.value = response.data
    pageType.value = "detail"

    // 检查富文本内容高度
    checkContentHeight()
    const shareInfo = response.data.share_info
    setShare({ title: shareInfo.title, desc: shareInfo.desc, imgUrl: shareInfo.img })
  }
}

// 展开/收起文本
const toggleText = () => {
  isExpanded.value = !isExpanded.value
  console.log("切换文本展开状态:", isExpanded.value)
}

// 客服相关
const tapCustomerService = (item) => {
  console.log("点击客服相关:", item)
}

// 检查内容高度
const checkContentHeight = () => {
  // 延迟执行，确保rich-text内容已经渲染
  setTimeout(() => {
    checkHeight(0, 3)
  }, 300)
}

// 检查高度的具体实现
const checkHeight = (retryCount, maxRetries) => {
  nextTick(() => {
    const richTextContent = document.getElementById("richTextContent")
    if (richTextContent && richTextContent.offsetHeight > 0) {
      const contentHeight = richTextContent.offsetHeight
      const needToggle = contentHeight > maxHeight.value

      console.log("Rich-text content height:", contentHeight)
      console.log("Max height:", maxHeight.value)
      console.log("Need toggle:", needToggle)

      actualHeight.value = contentHeight
      showToggle.value = needToggle
      // 如果内容高度小于等于maxHeight，默认展开
      isExpanded.value = !needToggle
    } else if (retryCount < maxRetries) {
      // 如果高度获取失败，进行重试
      retryCount++
      console.log(`Rich-text高度检测失败，正在重试 ${retryCount}/${maxRetries}`)
      setTimeout(() => checkHeight(retryCount, maxRetries), 300)
    } else {
      // 重试失败，设置默认状态
      console.warn("Rich-text高度检测失败，使用默认状态")
      showToggle.value = false
      isExpanded.value = true
    }
  })
}

// 生命周期
onMounted(() => {
  initData()
})

onUnmounted(() => {})
</script>

<style lang="scss" scoped>
:global(#app) {
  background-color: #ffffff;
}
.page-container {
  background: #fff;
  background-repeat: no-repeat;
  background-size: 100%;
  box-sizing: border-box;
}

.content {
  .main-content {
    // 转换后的样式 (1rpx = 0.5px, 1rem = 37.5px)
    .header-top {
      padding: 0.43rem 0.43rem 0.53rem 0.43rem; // 26rpx×0.5=13px→0.347rem, 32rpx×0.5=16px→0.427rem, 40rpx×0.5=20px→0.533rem
      background: #fff;
      border-bottom: 0.213rem solid rgba(247, 248, 250, 1); // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem

      .title {
        font-size: 0.48rem; // 36rpx × 0.5 = 18px, 18px ÷ 37.5 = 0.48rem
        color: rgba(34, 36, 46, 1);
        line-height: 0.72rem; // 54rpx × 0.5 = 27px, 27px ÷ 37.5 = 0.72rem
        font-weight: bold;
      }

      .label {
        display: flex;
        align-items: center;
        margin-top: 0.333rem; // 25rpx × 0.5 = 12.5px, 12.5px ÷ 37.5 = 0.333rem

        .address {
          font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
          color: rgba(145, 148, 153, 0.8);
        }

        .time {
          font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
          color: rgba(145, 148, 153, 0.8);
          padding-left: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
          position: relative;
          &.pl0 {
            padding-left: 0;
            &::after {
              display: none;
            }
          }

          &::after {
            position: absolute;
            display: block;
            content: " ";
            width: 0.013rem; // 1rpx × 0.5 = 0.5px, 0.5px ÷ 37.5 = 0.013rem
            height: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
            left: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
            top: 50%;
            transform: translateY(-50%);
            background: rgba(235, 236, 240, 1);
          }
        }
      }
    }

    .content-box-top {
      position: sticky;
      top: var(--header-height, 100px);
      display: flex;
      align-items: center;
      padding: 0 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
      justify-content: space-between;
      background: #fff;
      padding-bottom: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
      z-index: 22;
    }

    .tab-list {
      display: flex;
      align-items: center;

      .tab-list-item {
        font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
        color: rgba(102, 102, 102, 1);
        margin-right: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
        position: relative;
        padding-top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
        padding-bottom: 0.453rem; // 34rpx × 0.5 = 17px, 17px ÷ 37.5 = 0.453rem
        cursor: pointer;

        &:last-child {
          margin-right: 0;
        }

        &.active {
          color: rgba(34, 36, 46, 1);
          font-size: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
          position: relative;
          font-weight: bold;

          &::after {
            position: absolute;
            display: block;
            content: " ";
            width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
            height: 0.08rem; // 6rpx × 0.5 = 3px, 3px ÷ 37.5 = 0.08rem
            background-color: rgba(230, 0, 3, 1);
            bottom: 0.187rem; // 14rpx × 0.5 = 7px, 7px ÷ 37.5 = 0.187rem
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0.067rem; // 5rpx × 0.5 = 2.5px, 2.5px ÷ 37.5 = 0.067rem
          }
        }

        .dian {
          width: 0.16rem; // 12rpx × 0.5 = 6px, 6px ÷ 37.5 = 0.16rem
          height: 0.16rem;
          position: absolute;
          right: -0.16rem; // -12rpx × 0.5 = -6px, -6px ÷ 37.5 = -0.16rem
          top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
        }
      }
    }

    .search-box {
      display: flex;
      align-items: center;
      background: rgba(247, 248, 250, 1);
      width: 2.267rem; // 170rpx × 0.5 = 85px, 85px ÷ 37.5 = 2.267rem
      padding: 0 0.267rem; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
      box-sizing: border-box;
      border-radius: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
      height: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
      cursor: pointer;

      input {
        flex: 1;
        min-width: 0;
        padding-left: 0.107rem; // 8rpx × 0.5 = 4px, 4px ÷ 37.5 = 0.107rem
        font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        border: none;
        background: transparent;
        outline: none;
      }

      img {
        width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        height: 0.427rem;
      }
    }

    // 公告详情样式
    .content-box {
      background: #fff;
      // margin-bottom: 0.267rem; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem

      &.bor_bb {
        border-bottom: 0.267rem solid #f7f8fa; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
      }

      .content-center {
        padding: 0.533rem 0.427rem; // 40rpx×0.5=20px→0.533rem, 32rpx×0.5=16px→0.427rem
      }

      .notice-box {
        background: rgba(247, 248, 250, 1);
        padding: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem

        .notice-box-top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-bottom: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
          border-bottom: 0.013rem solid rgba(235, 236, 240, 1); // 1rpx × 0.5 = 0.5px, 0.5px ÷ 37.5 = 0.013rem

          &.bt_0 {
            border-bottom: none;
            padding-bottom: 0;
          }

          .left {
            .left-text {
              font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
              color: rgba(60, 61, 66, 1);
              margin-right: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem

              .num {
                font-weight: bold;
              }
            }
          }

          .status {
            font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
            color: rgba(19, 191, 128, 1);
          }
        }

        .time-line {
          margin-top: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem

          &-item {
            display: flex;
            align-items: center;
            position: relative;
            padding-left: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
            padding-bottom: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem

            &:last-child {
              padding-bottom: 0;

              &::after {
                display: none;
              }
            }

            &.active {
              .dian {
                width: 7px;
                height: 7px;
                background: #fff;
                border: 0.027rem solid rgba(230, 0, 3, 1); // 2rpx × 0.5 = 1px, 1px ÷ 37.5 = 0.027rem
                left: -2px;
                top: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem

                &::after {
                  display: block;
                  content: " ";
                  position: absolute;
                  width: 3px;
                  height: 3px;
                  background-color: rgba(230, 0, 3, 1);
                  left: 50%;
                  top: 50%;
                  border-radius: 50%;
                  transform: translate(-50%, -50%);
                }
              }

              .text-word {
                color: rgba(230, 0, 3, 1);
              }

              .time {
                color: rgba(230, 0, 3, 1);
              }
            }

            &::after {
              position: absolute;
              display: block;
              content: " ";
              width: 1px;
              height: 100%;
              border-left: 1px dashed rgba(194, 197, 204, 1);
              left: 2px;
              z-index: 1;
              top: 0.2rem; // 15rpx × 0.5 = 7.5px, 7.5px ÷ 37.5 = 0.2rem
              box-sizing: border-box;
            }

            .text-word {
              font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
              color: rgba(60, 61, 66, 1);
            }

            .time {
              font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
              color: rgba(145, 148, 153, 1);
              padding-left: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
              flex: 1;
              min-width: 0;
            }

            .dian {
              width: 5px;
              height: 5px;
              background-color: rgba(194, 197, 204, 1);
              border-radius: 50%;
              position: absolute;
              z-index: 2;
              left: 0;
              top: 0.2rem; // 15rpx × 0.5 = 7.5px, 7.5px ÷ 37.5 = 0.2rem
            }
          }
        }
      }

      .rich-text-box {
        // &.pr-box {
        //   margin-top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
        // }
        box-sizing: border-box;
        .rich-text-container {
          position: relative;
          transition: max-height 0.3s ease;

          &.collapsed {
            overflow: hidden;
          }

          &.expanded {
            max-height: none !important;
          }

          // 富文本内容样式
          #richTextContent {
            font-size: 0.373rem !important; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
            line-height: 2;
            color: #3c3d42;

            :deep(p) {
              font-size: 0.373rem !important;
              line-height: 2 !important;
            }

            :deep(img) {
              max-width: 100% !important;
              height: auto !important;
              display: block !important;
              margin: 0.533rem auto !important; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
            }

            :deep(table) {
              width: 100% !important;
              box-sizing: border-box;
              border-collapse: collapse !important;
              margin: 0.533rem 0 !important; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
              table-layout: auto;

              // td,
              // th {
              //   border: 0.013rem solid #ddd !important; // 1rpx × 0.5 = 0.5px, 0.5px ÷ 37.5 = 0.013rem
              //   padding: 0.267rem !important; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
              //   font-size: 0.32rem !important; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
              // }

              // th {
              //   background: #f5f5f5 !important;
              //   font-weight: bold !important;
              // }
            }
          }
        }

        .bottom-pos {
          position: relative;

          .gradient-height {
            position: absolute;
            bottom: 1rem; // 100rpx × 0.5 = 50px, 50px ÷ 37.5 = 1.333rem
            left: 0;
            right: 0;
            height: 1.333rem; // 100rpx × 0.5 = 50px, 50px ÷ 37.5 = 1.333rem
            background: linear-gradient(transparent, #fff);
            pointer-events: none;
          }

          .see-all {
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(60, 61, 66, 1);
            padding: 0.267rem 0; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
            font-size: 0.43rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
            cursor: pointer;

            img {
              width: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
              height: 0.32rem;
              margin-left: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
            }
          }
        }
      }

      .see-address {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.533rem 0; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
        margin-top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
        background: #fff2f2;
        border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
        font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
        color: #e60003;
        cursor: pointer;

        img {
          width: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
          height: 0.32rem;
          margin-left: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
        }
      }

      .attachment-box {
        background: rgba(247, 248, 250, 1);
        padding: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
        margin-top: 0.8rem; // 60rpx × 0.5 = 30px, 30px ÷ 37.5 = 0.8rem

        .title {
          font-size: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
          color: rgba(34, 36, 46, 1);
          font-weight: bold;
        }

        .information-list {
          margin-top: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem

          &-item {
            display: flex;
            align-items: center;
            background: #fff;
            padding: 0.32rem 0.427rem; // 24rpx×0.5=12px→0.32rem, 32rpx×0.5=16px→0.427rem
            border-radius: 0.16rem; // 12rpx × 0.5 = 6px, 6px ÷ 37.5 = 0.16rem
            box-sizing: border-box;
            margin-bottom: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem

            &:last-child {
              margin-bottom: 0;
            }

            .type-img {
              width: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
              height: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
            }

            .text {
              padding: 0 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
              font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
              color: rgba(60, 61, 66, 1);
              flex: 1;
              line-height: 0.48rem; // 36rpx × 0.5 = 18px, 18px ÷ 37.5 = 0.48rem
              word-break: break-all;
            }

            .right-arrow {
              width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
              height: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
            }
          }
        }
      }

      .originally {
        margin-top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
        font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        color: #999;
        display: flex;
        align-items: center;

        .title {
          color: #333;
          margin: 0 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
          flex: 1;
          min-width: 0;
        }

        .copy-img {
          width: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
          height: 0.32rem;
          margin-right: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
        }

        .copy-c {
          color: #007bff;
          cursor: pointer;
        }
      }
    }

    // 热门问答样式
    .popular-answer {
      padding: 0.64rem 0.427rem 0.533rem 0.427rem; // 48rpx×0.5=24px→0.64rem, 32rpx×0.5=16px→0.427rem, 40rpx×0.5=20px→0.533rem
      background: #fff;

      .item-title {
        font-size: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        color: rgba(34, 36, 46, 1);
        font-weight: bold;
      }

      .answer-list {
        margin-top: 0.4rem; // 30rpx × 0.5 = 15px, 15px ÷ 37.5 = 0.4rem
      }

      .answer-list-item {
        margin-bottom: 0.4rem; // 30rpx × 0.5 = 15px, 15px ÷ 37.5 = 0.4rem

        &:last-child {
          margin-bottom: 0;
        }
      }

      .ask-box {
        display: flex;

        img {
          width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
          height: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
          margin-right: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
          margin-top: 0.2rem; // 15rpx × 0.5 = 7.5px, 7.5px ÷ 37.5 = 0.2rem
        }

        .text-t {
          font-size: 0.4rem; // 30rpx × 0.5 = 15px, 15px ÷ 37.5 = 0.4rem
          line-height: 0.8rem; // 60rpx × 0.5 = 30px, 30px ÷ 37.5 = 0.8rem
          color: rgba(60, 61, 66, 1);
          flex: 1;
        }
      }

      .answer-box {
        display: flex;
        margin-top: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem

        img {
          width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
          height: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
          margin-right: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
          margin-top: 0.08rem; // 6rpx × 0.5 = 3px, 3px ÷ 37.5 = 0.08rem
        }

        .text-t {
          font-size: 0.347rem; // 26rpx × 0.5 = 13px, 13px ÷ 37.5 = 0.347rem
          line-height: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
          color: rgba(102, 102, 102, 1);
          flex: 1;
        }
      }

      .more-button {
        font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        color: rgba(60, 61, 66, 1);
        background: rgba(247, 248, 250, 1);
        border: 0.013rem solid rgba(194, 197, 204, 0.5); // 1rpx × 0.5 = 0.5px, 0.5px ÷ 37.5 = 0.013rem
        height: 1.067rem; // 80rpx × 0.5 = 40px, 40px ÷ 37.5 = 1.067rem
        border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
        display: flex;
        align-items: center;
        justify-content: center;
        width: 7.867rem; // 590rpx × 0.5 = 295px, 295px ÷ 37.5 = 7.867rem
        margin: 0 auto;
        margin-top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
        cursor: pointer;

        img {
          width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
          height: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
          margin-top: 0.053rem; // 4rpx × 0.5 = 2px, 2px ÷ 37.5 = 0.053rem
        }
      }
    }

    // 工具样式
    .tool-box {
      padding: 0.64rem 0.427rem; // 48rpx×0.5=24px→0.64rem, 32rpx×0.5=16px→0.427rem
      background: #fff;

      .item-title {
        font-size: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        color: rgba(34, 36, 46, 1);
        font-weight: bold;
      }

      .tool-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem

        &-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 25%;
          font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
          color: rgba(60, 61, 66, 1);
          margin-bottom: 0.64rem; // 48rpx × 0.5 = 24px, 24px ÷ 37.5 = 0.64rem

          img {
            width: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
            height: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
            margin-bottom: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
          }
        }
      }

      .written-list {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.32rem;
        margin-top: 0.427rem;
        .written-list-item {
          border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
          margin-bottom: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
          height: 1.813rem; // 136rpx × 0.5 = 68px, 68px ÷ 37.5 = 1.813rem
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          padding-right: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
          background: linear-gradient(270deg, rgba(247, 248, 250, 0.5) 0%, #f7f8fa 100%);

          .img {
            width: 1.067rem; // 80rpx × 0.5 = 40px, 40px ÷ 37.5 = 1.067rem
            height: 1.067rem; // 80rpx × 0.5 = 40px, 40px ÷ 37.5 = 1.067rem
          }

          .title {
            font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
            padding-left: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
            color: rgba(60, 61, 66, 1);
          }
        }
      }

      .bd-box {
        .bd-img {
          width: 100%;
          border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
          object-fit: cover;
          max-height: 2.933rem; // 220rpx × 0.5 = 110px, 110px ÷ 37.5 = 2.933rem
        }
      }
    }

    // 声明样式
    .statement-box {
      background: rgba(247, 248, 250, 1);
      padding: 0.533rem 0.427rem; // 40rpx×0.5=20px→0.533rem, 32rpx×0.5=16px→0.427rem
      // margin-bottom: 0.267rem; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
      font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
      color: #999;
      line-height: 1.5;
      text-align: justify;
    }

    // 底部操作栏样式
    .action-bar-box {
      display: flex;
      height: 1.893rem; // 142rpx × 0.5 = 71px, 71px ÷ 37.5 = 1.893rem
      z-index: 998;
      position: relative;
      box-sizing: border-box;
    }

    .action-bar {
      z-index: 1;
      position: fixed;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100%;
      height: 1.893rem; // 142rpx × 0.5 = 71px, 71px ÷ 37.5 = 1.893rem
      background-color: #fff;
      box-sizing: border-box;
      border-top: 0.013rem solid #ebecf0; // 1rpx × 0.5 = 0.5px, 0.5px ÷ 37.5 = 0.013rem
      padding: 0.32rem 0.533rem 0.453rem 0.533rem; // 24rpx×0.5=12px→0.32rem, 40rpx×0.5=20px→0.533rem, 34rpx×0.5=17px→0.453rem
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .bottom-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
      position: relative;

      .new-box {
        position: absolute;
        font-size: 0.24rem; // 18rpx × 0.5 = 9px, 9px ÷ 37.5 = 0.24rem
        color: rgba(230, 0, 3, 1);
        background: rgba(230, 0, 3, 0.1);
        padding: 0.027rem 0.133rem; // 2rpx×0.5=1px→0.027rem, 10rpx×0.5=5px→0.133rem
        right: -0.187rem; // -14rpx × 0.5 = -7px, -7px ÷ 37.5 = -0.187rem
        border-radius: 0.267rem 0.267rem 0.267rem 0.053rem; // 20rpx×0.5=10px→0.267rem, 4rpx×0.5=2px→0.053rem
        top: -5px;
      }

      img {
        width: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
        height: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
        margin-bottom: 0.053rem; // 4rpx × 0.5 = 2px, 2px ÷ 37.5 = 0.053rem
      }

      font-size: 0.267rem; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
      color: rgba(145, 148, 153, 1);
    }

    .filter-button {
      background: rgba(236, 62, 51, 1);
      font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
      color: #fff;
      width: 5.333rem; // 400rpx × 0.5 = 200px, 200px ÷ 37.5 = 5.333rem
      height: 1.12rem; // 84rpx × 0.5 = 42px, 42px ÷ 37.5 = 1.12rem
      border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;

      &.wp100 {
        width: 100%;
      }
    }

    .flex-c {
      display: flex;
      align-items: center;
    }

    // 通用边框样式
    .bor_bb {
      border-bottom: 0.267rem solid #f7f8fa; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
    }
  }
}
</style>

<!-- 全局样式，确保富文本内容样式生效 -->
<style lang="scss">
// 富文本内容全局样式控制
#richTextContent {
  * {
    font-size: 0.373rem !important; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
    line-height: 2 !important;
  }
  a {
    text-decoration: underline !important;
  }
  p {
    font-size: 0.373rem !important;
    line-height: 2 !important;
  }

  img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
    margin: 0.533rem auto !important; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
  }

  table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin: 0.533rem 0 !important; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
    table-layout: fixed;
    word-break: break-all;
  }
}
</style>
