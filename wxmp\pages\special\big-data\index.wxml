<wxs module="utils" src="./index.wxs"></wxs>
<view class="big-data" bindtap="onPageClick" data-path="root">
  <!-- 固定头部导航栏 -->
  <view class="fixed-header {{showFixedHeader ? 'show' : 'hide'}}" style="padding-top: {{statusBarHeight}}px;">
    <view class="header-content">
      <view class="header-left" catch:tap="onBackClick">
        <view class="back-icon"></view>
      </view>
      <view class="header-title">报名大数据</view>
      <view class="header-right"></view>
    </view>
  </view>

  <image class="back-btn" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data-top_back.png" mode="" catch:tap="onBackClick" />
  <view class="top-bg">
    <image class="bg-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_bg.png" mode="" />
    <view class="top-area">
      <image class="top-title-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_title.png" mode="" />
      <view class="title">最后更新时间：2025.06.12 15:35</view>
      <view class="select-one">
        <view class="select-item" bindtap="onExamTypeClick">
          <view class="text">{{selectedExamTypeText}}</view>
          <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
        <view class="select-item" bindtap="onRegionClick">
          <view class="text">{{selectedRegionText}}</view>
          <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
      </view>
      <view class="select-item w100" bindtap="onSpecificExamClick">
        <view class="text">{{project_name}}</view>
        <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
      </view>
    </view>
  </view>
  <view class="main-contnet">
    <!-- Tab导航区域 -->
    <view class="tab-container">
      <scroll-view class="tab-scroll" scroll-x="true" scroll-with-animation="true" scroll-left="{{scrollLeft}}" show-scrollbar="{{false}}" enhanced="{{true}}">
        <view class="tab-list">
          <view wx:for="{{tabList}}" wx:key="index" class="tab-item {{currentTab === index ? 'active' : ''}}" data-index="{{index}}" bindtap="onTabClick" id="tab-{{index}}">
            {{item.name}}
          </view>
        </view>
      </scroll-view>
    </view>
    <!-- 内容区域 -->
    <view class="content-container">
      <view class="content-area">
        <!-- 招聘公告标题 -->
        <view class="announcement-title" catch:tap="goDetail">
          <text class="text"> {{detailData.title}}</text>
          <!-- <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/announcement_title_arrow.png" mode="" /> -->
        </view>

        <!-- 数据统计卡片 -->
        <view class="stats-container">
          <!-- 报名总人数 -->
          <view class="stats-card large-card orange-card">
            <view class="card-title">报名总人数</view>
            <view class="card-number">
              <text class="number">{{detailData.apply_num_info.total_apply_num}}</text>
              <text class="unit">人</text>
            </view>
          </view>

          <!-- 报名人数较多的单位 -->
          <view class="stats-card large-card blue-card">
            <view class="card-title">
              报名人数较多的单位
              <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_right_1.png" mode="" />
            </view>
            <view class="card-number">
              <text class="number">{{detailData.apply_num_info.max_work_unit_apply_num}}</text>
              <text class="unit">人</text>
              <view class="card-type">最高单位报名</view>
            </view>
          </view>

          <!-- 竞争激烈职位 -->
          <view class="stats-card large-card light-blue-card">
            <view class="card-title">
              竞争激烈职位
              <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_right_2.png" mode="" />
            </view>
            <view class="card-number">
              <text class="number">{{detailData.apply_num_info.max_competitive_rate}}</text>
              <view class="card-type">最高竞争比</view>
            </view>
            <view class="card-desc">{{detailData.apply_num_info.avg_competitive_rate_text}}</view>
          </view>

          <!-- 报名人数较少职位 -->
          <view class="stats-card large-card cyan-card">
            <view class="card-title">
              报名人数较少职位
              <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_right_3.png" mode="" />
            </view>
            <view class="card-number">
              <text class="number">{{detailData.apply_num_info.zero_apply_num_job}}</text>
              <text class="unit">个</text>
              <view class="card-type">无人报名职位</view>
            </view>
            <view class="card-desc">{{detailData.apply_num_info.zero_apply_num_job_text}}</view>
          </view>
        </view>

        <view class="data-desc {{dataDescExpanded ? 'expanded' : 'collapsed'}}">
          <view class="data-desc-wrapper">
            <text class="data-desc-text">数据说明：本站发布的招考资讯均来源于招录官方网站，由事考帮整理编辑，如遇报考疑问请咨询招考官方，若涉及版权或错误信息，请提交反馈到本站予以更新或删除。数据仅供参考，具体请以官方公告为准。</text>
            <view wx:if="{{dataDescExpanded}}" class="expand-btn inline" bindtap="onToggleDataDesc">
              <text class="blue-text">收起</text>
              <image class="arrow-icon rotated" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_down.png" mode="" />
            </view>
          </view>
          <view wx:if="{{!dataDescExpanded}}" class="expand-btn" bindtap="onToggleDataDesc">
            <text class="blue-text">展开</text>
            <image class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_down.png" mode="" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 各地域报名数据 -->
  <view class="area-box">
    <view class="title">各地域报名数据</view>
    <view catch:tap>
      <score-all-echarts id="score-bar-id-multiple" scoreData="{{detailData.region_apply_num_list}}"></score-all-echarts>
    </view>
  </view>

  <!-- 职位数据表格区域 -->
  <view class="position-table-section">
    <!-- 标题区域 -->
    <view class="position-header">
      <view class="position-title">职位大数据</view>
      <view class="position-location" bindtap="onPositionLocationClick">
        <text class="location-text">{{selectedPositionLocationText}}</text>
        <image class="location-arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_gray_select.png" mode="" />
      </view>
    </view>

    <!-- Tab导航 -->
    <view class="position-tab-container">
      <view class="position-tab-list">
        <view class="position-tab-item {{currentMainTab === 0 ? 'active' : ''}}" data-index="0" bindtap="onMainTabClick">
          热门
        </view>
        <view class="position-tab-item {{currentMainTab === 1 ? 'active' : ''}}" data-index="1" bindtap="onMainTabClick">
          冷门
        </view>
      </view>
      <view class="position-sub-tab-list">
        <view class="position-sub-tab-item {{selectedSubTab === 0 ? 'active' : ''}}" data-index="0" bindtap="onSubTabClick">
          按报名数
        </view>
        <view class="position-sub-tab-item {{selectedSubTab === 1 ? 'active' : ''}}" data-index="1" bindtap="onSubTabClick">
          按竞争比
        </view>
      </view>
    </view>

    <!-- 表格1内容 -->
    <view class="position-table-content">
      <!-- 表格头部 -->
      <view class="position-table-header">
        <view class="header-item header-rank"></view>
        <view class="header-item header-position">
          <text>职位</text>
          <text>名称</text>
        </view>
        <view class="header-item header-unit">
          <text>招考</text>
          <text>单位</text>
        </view>
        <view class="header-item header-recruit">
          <text>招录</text>
          <text>人数</text>
        </view>
        <view class="header-item header-apply">
          <text>报名</text>
          <text>人数</text>
        </view>
      </view>

      <!-- 表格数据 -->
      <view class="position-table-body">
        <view wx:for="{{currentTableData}}" wx:key="index" class="position-table-row">
          <view class="table-item table-rank">
            <view class="rank-badge rank-{{index + 1}}">{{index + 1}}</view>
          </view>
          <view class="table-item table-position">
            <text class="position-title">{{item.positionName}}</text>
          </view>
          <view class="table-item table-unit">
            <text class="unit-title">{{item.recruitUnit}}</text>
          </view>
          <view class="table-item table-recruit">
            <text class="recruit-num">{{item.recruitCount}}</text>
          </view>
          <view class="table-item table-apply">
            <text class="apply-num">{{item.applyCount}}</text>
          </view>
        </view>
      </view>

      <!-- 查看更多 -->
      <view class="load-more-section" wx:if="{{showLoadMore}}" bindtap="onLoadMoreClick">
        <view class="load-more-text">查看更多</view>
        <image class="load-more-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_gray_select.png" mode="" />
      </view>
    </view>

  </view>

  <view class="recruiting-unit">
    <view class="recruit-title">
      招考单位 TOP10
    </view>
    <!-- 表格2内容 -->
    <view class="position-table-content-2">
      <!-- 表格头部 -->
      <view class="position-table-header">
        <view class="header-item header-rank"></view>
        <view class="header-item header-position">
          <text>招考</text>
          <text>单位</text>
        </view>
        <view class="header-item header-unit">
          <text>职位</text>
          <text>数</text>
        </view>
        <view class="header-item header-recruit">
          <text>招录</text>
          <text>人数</text>
        </view>
        <view class="header-item header-apply">
          <text>报名</text>
          <text>人数</text>
        </view>
        <view class="header-item header-apply">
          <text>竞争</text>
          <text>比</text>
        </view>
      </view>

      <!-- 表格数据 -->
      <view class="position-table-body">
        <view wx:for="{{detailData.work_unit_list}}" wx:key="index" class="position-table-row">
          <view class="table-item table-rank">
            <view class="rank-badge rank-{{index + 1}}">{{index + 1}}</view>
          </view>
          <view class="table-item table-position">
            <text class="position-title">{{item.work_unit}}</text>
          </view>
          <view class="table-item table-unit">
            <text class="unit-title">{{item.job_num}}</text>
          </view>
          <view class="table-item table-recruit">
            <text class="recruit-num">{{item.need_num}}</text>
          </view>
          <view class="table-item table-apply">
            <text class="apply-num">{{item.apply_num}}</text>
          </view>
          <view class="table-item table-apply">
            <text class="apply-num">{{item.competitive_rate}}</text>
          </view>
        </view>
      </view>
    </view>
    <image class="zhaokao-bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/zhaokao-bg.png" mode="" />
  </view>

  <view class="job-search" id="job-search-section">
    <view class="title">岗位报考查询</view>
    <view class="search-box">
      <image class="bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_bg.png" mode="" />
      <!-- <view class="top-area">
        <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_top_icon.png" mode="" />
        <view class="text">2025年重庆市公务员</view>
      </view> -->
      <view class="white-box">
        <view class="select-item" bindtap="onSearchRegionClick">
          <view class="left">
            <view class="sp5">
              <text>地</text>
              <text>区：</text>
            </view>
            <view>{{selectedSearchRegionText}}</view>
          </view>
          <image class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
        <view class="select-item" bindtap="onSearchUnitClick">
          <view class="left">
            <view class="sp5">
              用人单位：
            </view>
            <view>{{selectedSearchUnitText}}</view>
          </view>
          <image class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
        <view class="select-item input-item">
          <view class="left">
            <view class="sp5">
              <text>职</text>
              <text>位：</text>
            </view>
            <input class="position-input" type="text" placeholder="请输入职位名称或代码" value="{{positionInput}}" bindinput="onPositionInput" placeholder-class="input-placeholder" />
          </view>
        </view>
        <view class="search-btn">
          <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_search_icon.png" mode="" />
          <text>查询</text>
        </view>
        <view class="delete-text" bindtap="onClearClick">清除</view>
      </view>
    </view>
  </view>

  <view class="result-area">
    <view class="top-area">
      <view class="left">
        <view class="text-area">
          <text>共</text>
          <text class="red">192</text>
          <text>个职位</text>
        </view>
        <view class="btn" bind:tap="changeSort">
          <view class="text">竞争比</view>
          <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/{{sortIndex === 1?'arrow_top':sortIndex === 2?'arrow_bottom':'arrow_all'}}.png" mode="" />
        </view>
      </view>
      <view class="right" bind:tap="goFocus">
        <view class="text">我的关注</view>
        <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_right_blue.png" mode="" />
      </view>
    </view>
    <view wx:for="{{ resultList }}" wx:key="index">
      <job-card jobData="{{item}}"></job-card>
    </view>
  </view>

  <tabbar-box>
    <view class="bottom-box">
      <view class="left">
        <view class="icon-item" bind:tap="goJobs">
          <image class="icon-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/job_service.png" mode="" />
          <view class="icon-text">选岗咨询</view>
        </view>
        <view class="icon-item" bind:tap="checkNews">
          <image class="icon-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/news.png" mode="" />
          <view class="icon-text">查看公告</view>
        </view>
      </view>
      <view class="right-btn" bind:tap="goWeb">岗位报考查询</view>
    </view>
  </tabbar-box>

  <!-- 下拉框弹窗 -->
  <van-popup show="{{ optPopShow }}" round position="bottom" bind:close="OptClose">
    <van-picker id="myPicker" show-toolbar title="{{ optTitle }}" columns="{{ optList }}" value-key="key" columns-field-names="{{ {text: 'name', value: 'key'} }}" default-index="{{ defaultIndex }}" toolbar-class="top-area" column-class="column-area" active-class="active-item" bind:cancel="OptClose" bind:confirm="onConfirm" />
  </van-popup>
</view>

<!-- 底部导航栏 -->
<!-- <home-tabbar active="my" /> -->