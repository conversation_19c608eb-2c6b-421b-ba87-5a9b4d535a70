# 优先级 A 规则：必要的 {#priority-a-rules-essential}

::: warning 注意
此 Vue.js 风格指南已过时，需要重新审查。如果您有任何问题或建议，请[提交 issue](https://github.com/vuejs/docs/issues/new)。
:::

这些规则有助于防止错误，因此请不惜一切代价学习并遵守它们。可能存在例外情况，但应该非常罕见，并且只能由同时具备 JavaScript 和 Vue 专业知识的人员制定。

## 使用多词组件名 {#use-multi-word-component-names}

用户组件名应该始终是多词的，除了根 `App` 组件。这可以[避免冲突](https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name)与现有和未来的 HTML 元素，因为所有 HTML 元素都是单个词。

<div class="style-example style-example-bad">
<h3>不好</h3>

```vue-html
<!-- in pre-compiled templates -->
<Item />

<!-- in in-DOM templates -->
<item></item>
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```vue-html
<!-- in pre-compiled templates -->
<TodoItem />

<!-- in in-DOM templates -->
<todo-item></todo-item>
```

</div>

## 使用详细的 prop 定义 {#use-detailed-prop-definitions}

在提交的代码中，prop 定义应该尽可能详细，至少指定类型。

::: details 详细说明
详细的 [prop 定义](/guide/components/props#prop-validation) 有两个优势：

- 它们记录了组件的 API，因此很容易看出组件的预期用法。
- 在开发中，如果组件提供了格式不正确的 props，Vue 会警告您，帮助您捕获潜在的错误源。
  :::

<div class="options-api">

<div class="style-example style-example-bad">
<h3>不好</h3>

```js
// 这只在原型开发时可以接受
props: ["status"]
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```js
props: {
  status: String
}
```

```js
// 更好！
props: {
  status: {
    type: String,
    required: true,

    validator: value => {
      return [
        'syncing',
        'synced',
        'version-conflict',
        'error'
      ].includes(value)
    }
  }
}
```

</div>

</div>

<div class="composition-api">

<div class="style-example style-example-bad">
<h3>不好</h3>

```js
// 这只在原型开发时可以接受
const props = defineProps(["status"])
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```js
const props = defineProps({
  status: String,
})
```

```js
// 更好！

const props = defineProps({
  status: {
    type: String,
    required: true,

    validator: (value) => {
      return ["syncing", "synced", "version-conflict", "error"].includes(value)
    },
  },
})
```

</div>

</div>

## 使用带 key 的 `v-for` {#use-keyed-v-for}

在组件上使用 `v-for` 时，`key` _总是_ 必需的，以便维护子树下的内部组件状态。即使对于元素，保持可预测的行为也是一个好习惯，比如动画中的[对象恒定性](https://bost.ocks.org/mike/constancy/)。

::: details 详细说明
假设您有一个待办事项列表：

<div class="options-api">

```js
data() {
  return {
    todos: [
      {
        id: 1,
        text: '学习使用 v-for'
      },
      {
        id: 2,
        text: '学习使用 key'
      }
    ]
  }
}
```

</div>

<div class="composition-api">

```js
const todos = ref([
  {
    id: 1,
    text: "学习使用 v-for",
  },
  {
    id: 2,
    text: "学习使用 key",
  },
])
```

</div>

然后您按字母顺序对它们进行排序。更新 DOM 时，Vue 将优化渲染以执行尽可能便宜的 DOM 变更。这可能意味着删除第一个待办事项元素，然后在列表末尾再次添加它。

问题是，在某些情况下，不删除将保留在 DOM 中的元素很重要。例如，您可能希望使用 `<transition-group>` 来为列表排序设置动画，或者如果渲染的元素是 `<input>`，则保持焦点。在这些情况下，为每个项目添加唯一的 key（例如 `:key="todo.id"`）将告诉 Vue 如何更可预测地行为。

根据我们的经验，_总是_ 添加唯一的 key 更好，这样您和您的团队就永远不必担心这些边缘情况。然后在罕见的、性能关键的场景中，如果不需要对象恒定性，您可以有意识地做出例外。
:::

<div class="style-example style-example-bad">
<h3>不好</h3>

```vue-html
<ul>
  <li v-for="todo in todos">
    {{ todo.text }}
  </li>
</ul>
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```vue-html
<ul>
  <li
    v-for="todo in todos"
    :key="todo.id"
  >
    {{ todo.text }}
  </li>
</ul>
```

</div>

## 避免 `v-if` 和 `v-for` 同时使用 {#avoid-v-if-with-v-for}

**永远不要在同一个元素上使用 `v-if` 和 `v-for`。**

有两种常见情况可能会诱使您这样做：

- 过滤列表中的项目（例如 `v-for="user in users" v-if="user.isActive"`）。在这些情况下，将 `users` 替换为返回过滤列表的新计算属性（例如 `activeUsers`）。

- 避免渲染应该隐藏的列表（例如 `v-for="user in users" v-if="shouldShowUsers"`）。在这些情况下，将 `v-if` 移动到容器元素（例如 `ul`、`ol`）。

::: details 详细说明
当 Vue 处理指令时，`v-if` 的优先级比 `v-for` 更高，所以这个模板：

```vue-html
<ul>
  <li
    v-for="user in users"
    v-if="user.isActive"
    :key="user.id"
  >
    {{ user.name }}
  </li>
</ul>
```

将抛出错误，因为 `v-if` 指令将首先被评估，而此时迭代变量 `user` 不存在。

这可以通过迭代计算属性来修复，如下所示：

<div class="options-api">

```js
computed: {
  activeUsers() {
    return this.users.filter(user => user.isActive)
  }
}
```

</div>

<div class="composition-api">

```js
const activeUsers = computed(() => {
  return users.filter((user) => user.isActive)
})
```

</div>

```vue-html
<ul>
  <li
    v-for="user in activeUsers"
    :key="user.id"
  >
    {{ user.name }}
  </li>
</ul>
```

或者，我们可以使用带有 `v-for` 的 `<template>` 标签来包装 `<li>` 元素：

```vue-html
<ul>
  <template v-for="user in users" :key="user.id">
    <li v-if="user.isActive">
      {{ user.name }}
    </li>
  </template>
</ul>
```

:::

<div class="style-example style-example-bad">
<h3>不好</h3>

```vue-html
<ul>
  <li
    v-for="user in users"
    v-if="user.isActive"
    :key="user.id"
  >
    {{ user.name }}
  </li>
</ul>
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```vue-html
<ul>
  <li
    v-for="user in activeUsers"
    :key="user.id"
  >
    {{ user.name }}
  </li>
</ul>
```

```vue-html
<ul>
  <template v-for="user in users" :key="user.id">
    <li v-if="user.isActive">
      {{ user.name }}
    </li>
  </template>
</ul>
```

</div>

## 使用组件作用域样式 {#use-component-scoped-styling}

对于应用程序，顶级 `App` 组件和布局组件中的样式可能是全局的，但所有其他组件应该始终是作用域的。

这仅与[单文件组件](/guide/scaling-up/sfc)相关。它*不*要求使用 [`scoped` 属性](https://vue-loader.vuejs.org/guide/scoped-css.html)。作用域可以通过 [CSS 模块](https://vue-loader.vuejs.org/guide/css-modules.html)、基于类的策略（如 [BEM](http://getbem.com/)）或其他库/约定来实现。

**但是，组件库应该优先使用基于类的策略，而不是使用 `scoped` 属性。**

这使得覆盖内部样式更容易，具有人类可读的类名，这些类名没有太高的特异性，但仍然很不可能导致冲突。

::: details 详细说明
如果您正在开发大型项目、与其他开发人员合作，或有时包含第三方 HTML/CSS（例如来自 Auth0），一致的作用域将确保您的样式仅应用于它们所针对的组件。

除了 `scoped` 属性之外，使用唯一的类名可以帮助确保第三方 CSS 不会应用于您自己的 HTML。例如，许多项目使用 `button`、`btn` 或 `icon` 类名，因此即使不使用 BEM 等策略，添加应用程序特定和/或组件特定的前缀（例如 `ButtonClose-icon`）也可以提供一些保护。
:::

<div class="style-example style-example-bad">
<h3>不好</h3>

```vue-html
<template>
  <button class="btn btn-close">×</button>
</template>

<style>
.btn-close {
  background-color: red;
}
</style>
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```vue-html
<template>
  <button class="button button-close">×</button>
</template>

<!-- 使用 `scoped` 属性 -->
<style scoped>
.button {
  border: none;
  border-radius: 2px;
}

.button-close {
  background-color: red;
}
</style>
```

```vue-html
<template>
  <button :class="[$style.button, $style.buttonClose]">×</button>
</template>

<!-- 使用 CSS 模块 -->
<style module>
.button {
  border: none;
  border-radius: 2px;
}

.buttonClose {
  background-color: red;
}
</style>
```

```vue-html
<template>
  <button class="c-Button c-Button--close">×</button>
</template>

<!-- 使用 BEM 约定 -->
<style>
.c-Button {
  border: none;
  border-radius: 2px;
}

.c-Button--close {
  background-color: red;
}
</style>
```

</div>
