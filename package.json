{"name": "vue-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"amfe-flexible": "^2.2.1", "axios": "^1.10.0", "echarts": "^5.6.0", "js-cookie": "^3.0.5", "pinia": "^3.0.3", "vant": "^4.9.21", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^6.0.0", "autoprefixer": "^10.4.21", "postcss-pxtorem": "^6.1.0", "prettier": "3.5.3", "sass": "^1.89.2", "sass-loader": "^16.0.5", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7"}, "packageManager": "pnpm@10.12.3+sha512.467df2c586056165580ad6dfb54ceaad94c5a30f80893ebdec5a44c5aa73c205ae4a5bb9d5ed6bb84ea7c249ece786642bbb49d06a307df218d03da41c317417"}