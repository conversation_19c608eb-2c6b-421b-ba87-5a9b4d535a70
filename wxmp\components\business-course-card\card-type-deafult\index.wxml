<wxs module="utils">
  function findFour(arr) {
    if (arr.length > 3) {
      return arr.slice(0, 3);
    } else {
      return arr;
    }
  }
  module.exports = {
    findFour: findFour,
  };
</wxs>
<view class="course-card {{collectionHeight?'flex-column':''}}" wx:if="{{item}}" style="height: {{ collectionHeight !== undefined ? collectionHeight + 'px' : '' }}" bindtap="goDetail" data-item="{{item}}">
  <view class="{{collectionHeight?'flex1':''}}">
    <!-- 卡片标题 -->
    <view style="height:{{isInHome?'130rpx':''}}">
      <course-card-title title="{{item.name?item.name:''}}" titlePrefix="{{item}}" subTitle="{{item.summary?item.summary:''}}" isSearch="{{isSearch}}" showLabel="{{!isInHome}}" />
    </view>
    <!-- 首页不显示标签 -->
    <view class="label-list" wx:if="{{!isInHome}}">
      <view class="try-listen" wx:if="{{item.free_audition === 1}}">
        <image class="image" src="{{IMAGE_PREFIX}}images/course/coures_listen.png"></image>
        <text>免费试听</text>
      </view>
      <block wx:if="{{item.new_tags.length>0}}">
        <view class="try-listen" wx:for="{{item.new_tags}}" wx:key="index" style="background-color:{{item.color}};color:{{item.font_color}}">
          {{item.tag}}
        </view>
      </block>
    </view>
  </view>

  <!-- 课程列表时的底部样式 -->
  <view class="fonts-bootom" wx:if="{{!isInHome}}">
    <!-- 拼团 -->
    <block wx:if="{{!item.is_bought}}">

      <view class="group-buy" wx:if="{{item.pintuan&&item.pintuan.card_tag}}">
        <course-card-groupon-tags tagInfo="{{item.pintuan.card_tag}}" />
      </view>
      <view class="teacher-list" wx:else>
        <block wx:if="{{item.teachers.length>0 && item.show_teacher === 1}}">
          <image class="img" wx:for="{{utils.findFour(item.teachers)}}" wx:key="index" src="{{item.portrait}}"></image>
        </block>
      </view>
    </block>
    <view class="right-box">
      <!-- 价格文本 -->
      <course-card-price-text isQualification="{{!!item.is_bought}}" usability="{{item.usability}}" isHasGroupon="{{!!item.pintuan}}" isJoinGroupon="{{item.pintuan.record_info.status===1}}" couponPrice="{{item.coupon_price || ''}}" price="{{item.pintuan?item.pintuan.group_price:item.product.price}}" oldPrice="{{item.product.o_price}}" desc="{{item.participants}}" />
    </view>
  </view>


  <!--首页时的底部样式  -->
  <view class="fonts-bootom" wx:else>
    <!-- 价格文本 -->
    <course-card-price-text usability="{{item.usability}}" couponPrice="{{item.coupon_price || ''}}" isQualification="{{!!item.is_bought}}" isHasGroupon="{{!!item.pintuan}}" isJoinGroupon="{{item.pintuan.record_info.status===1}}" price="{{item.pintuan?item.pintuan.group_price:item.product.price}}" oldPrice="{{item.product.o_price}}" desc="{{item.right_bottom_txt}}" isHideDescText="{{true}}" isInHome="{{true}}" />
    <course-card-button usability="{{item.usability}}" isQualification="{{!!item.is_bought}}" isHasGroupon="{{!!item.pintuan}}" isJoinGroupon="{{item.pintuan.record_info.status===1}}"></course-card-button>
  </view>
</view>