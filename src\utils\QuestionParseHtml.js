// import { QuillDeltaToHtmlConverter } from "../node_modules/quill-delta-to-html/dist/QuillDeltaToHtmlConverter.bundle.js"
import { QuillDeltaToHtmlConverter } from "quill-delta-to-html"

// 获取JSON文本中的html
function getQuestionJsonText(options) {
  return new QuillDeltaToHtmlConverter(options.ops).convert()
}

// 清除最外层p标签
function clearPLabel(html) {
  if (html.slice(0, 3) === "<p>") {
    return html.slice(3, html.length - 4)
  }
  return html
}

// 获取json文本显示内容
export function parseQuestionHtml(jsonText) {
  let html = ""
  try {
    jsonText = JSON.parse(jsonText)

    if (Object.prototype.toString.call(jsonText) === "[object Object]") {
      html = getQuestionJsonText(jsonText)
      html = clearPLabel(html)
      html = parseImageHtml(html)
    } else {
      html = parseImageHtml(jsonText)
    }
  } catch {
    html = parseImageHtml(jsonText)
  }
  return html
}

// 获取图片文本HTML
export function parseImageHtml(title) {
  title = title.toString().replace("<F", "&lt")
  return title.toString().replace(/\[img=.*?\]/g, function (s) {
    const url = s.toString().replace(/\[img=|\((.*)\)\]$/g, "")
    return '<img src="' + url + '" style="max-width:100%"/>'
  })
}
