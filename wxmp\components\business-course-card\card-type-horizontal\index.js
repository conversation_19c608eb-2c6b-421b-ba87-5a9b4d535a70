const APP = getApp()
const ROUTER = require("@/services/mpRouter")
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    item: {
      type: Object,
      value: null,
    },
    // 当前是否是在首页展示
    isInHome: {
      type: Boolean,
      value: false,
    },
    isSearch: {
      type: <PERSON>olean,
      value: false,
    },
    attachParams: { type: Object, value: {} },
    collectionHeight: {
      type: String,
      value: "",
    },
  },
  options: {},

  /**
   * 组件的初始数据
   */
  data: {
    IMAGE_PREFIX: APP.globalData.CONFIG.IMAGE_PREFIX,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    goDetail(e) {
      const data = e.currentTarget.dataset.item
      ROUTER.navigateTo({
        path: "/package-goods/course/detail/index",
        query: {
          no: data.no,
          ...this.data.attachParams,
        },
      })
    },
  },
})
