/**
 * 计算选中的职位数量
 * @param {Array} jobList 职位列表
 * @returns {Number} 选中数量
 */
function getSelectedCount(jobList) {
  if (!jobList || jobList.length === 0) {
    return 0
  }

  var count = 0
  for (var i = 0; i < jobList.length; i++) {
    if (jobList[i].isChecked) {
      count++
    }
  }
  return count
}

/**
 * 获取总数量
 * @param {Array} jobList 职位列表
 * @returns {Number} 总数量
 */
function getTotalCount(jobList) {
  if (!jobList) {
    return 0
  }
  return jobList.length
}

/**
 * 判断是否有选中项
 * @param {Array} jobList 职位列表
 * @returns {Boolean} 是否有选中项
 */
function hasSelected(jobList) {
  return getSelectedCount(jobList) > 1
}

/**
 * 生成对比按钮文案
 * @param {Array} jobList 职位列表
 * @returns {String} 按钮文案
 */
function getCompareButtonText(jobList) {
  var selectedCount = getSelectedCount(jobList)
  var totalCount = getTotalCount(jobList)
  return selectedCount + "/" + totalCount
}

/**
 * 生成完整的对比按钮文案
 * @param {Array} jobList 职位列表
 * @returns {String} 完整按钮文案
 */
function getFullCompareButtonText(jobList) {
  var selectedCount = getSelectedCount(jobList)
  var totalCount = getTotalCount(jobList)
  return "对比 " + selectedCount + "/" + totalCount
}

module.exports = {
  getSelectedCount: getSelectedCount,
  getTotalCount: getTotalCount,
  hasSelected: hasSelected,
  getCompareButtonText: getCompareButtonText,
  getFullCompareButtonText: getFullCompareButtonText,
}
