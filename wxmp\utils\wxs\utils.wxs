function clearPriceZero(price) {
  // 拷贝一份价格字符串
  var newPrice = price
  // 找到小数点的位置
  var dotIndex = (price + "").indexOf(".")

  if (dotIndex > -1) {
    // 从末尾开始遍历
    while (newPrice[newPrice.length - 1] === "0") {
      newPrice = newPrice.slice(0, -1)
    }

    // 如果最后一位是小数点，去掉小数点
    if (newPrice[newPrice.length - 1] === ".") {
      newPrice = newPrice.slice(0, -1)
    }
  }

  return newPrice
}

function getStyle(status, type) {
  var styles = {
    answer: [16, 7, 3, 4, 14, 17, 18],
    correction: [7, 4, 14],
    score: [8, 5],
    interpret: [8],
  }

  // 如果type存在于styles对象中，则检查status是否在对应的数组中
  if (styles[type] && styles[type].indexOf(status) !== -1) {
    if (type == "answer" || type == "score" || type == "interpret") {
      return "line_hui"
    } else if (type == "correction") {
      return "none_red"
    }
  }
}

function getTextStyle(status, type) {
  var styles = {
    answer: [20, 17, 3, 21, 4, 10, 22],
  }

  // 如果type存在于styles对象中，则检查status是否在对应的数组中
  if (styles[type] && styles[type].indexOf(status) !== -1) {
    return "text_hui"
  }
}

function showbtn(status, type) {
  var statusRules = {
    answer: [1, 2, 3, 4, 5, 6, 8, 14, 16], // 除0外的所有状态
    correction: [4, 14, 7, 21, 22], // 批改中状态
    score: [5, 6, 8, 9], // 可查看分数状态
    interpret: [8], // 可查看解析状态
    live: [5], // 可查看直播状态
  }

  if (type === "answer") {
    return status !== 0
  }

  // 检查type是否存在，如果存在则检查status是否在数组中
  return statusRules[type] && statusRules[type].indexOf(status) !== -1
}

function getNum(num) {
  var str = num.toString()
  // 检查是否包含小数点
  var dotIndex = str.indexOf(".")
  if (dotIndex !== -1) {
    var len = str.length
    var lastNonZeroIndex = len

    // 从后往前遍历，找到第一个非零字符的位置
    for (var i = len - 1; i >= dotIndex; i--) {
      if (str[i] !== "0") {
        lastNonZeroIndex = i
        break
      }
    }

    // 如果最后只剩下一个小数点，则也去掉小数点
    if (lastNonZeroIndex === dotIndex) {
      return str.substring(0, dotIndex)
    }

    // 截取到第一个非零字符的位置
    return str.substring(0, lastNonZeroIndex + 1)
  } else {
    // 如果没有小数点，直接返回原字符串
    return str
  }
}

// 新增：格式化倒计时时间（秒）
function formatCountdown(ms) {
  var totalSeconds = Math.floor(ms)
  // 计算天数
  var days = Math.floor(totalSeconds / 86400)

  // 计算剩余小时
  var hours = Math.floor((totalSeconds % 86400) / 3600)

  // 计算剩余分钟
  var minutes = Math.floor((totalSeconds % 3600) / 60)

  // 计算剩余秒数
  var seconds = totalSeconds % 60

  console.log(days, "天数") // 日志输出用于调试

  // 补零函数（ES5 写法）
  function pad(n) {
    n = n.toString()
    return n.length < 2 ? "0" + n : n
  }

  if (days > 0) {
    // 如果天数大于0，则返回包含天数的格式化字符串
    return days + "天" + pad(hours) + ":" + pad(minutes) + ":" + pad(seconds)
  } else {
    // 否则仅返回标准的时间格式
    return {
      hours: pad(hours),
      minutes: pad(minutes),
      seconds: pad(seconds),
    }
  }
}

function isDisable(time) {
  var nowTimestamp = Math.floor(Date.now() / 1000)
  if (time >= nowTimestamp) {
    return true
  } else {
    return false
  }
}

module.exports = {
  clearPriceZero: clearPriceZero,
  getStyle: getStyle,
  showbtn: showbtn,
  getNum: getNum,
  formatCountdown: formatCountdown,
  isDisable: isDisable,
  getTextStyle: getTextStyle,
}
