.major-selector-inline {
  width: 100%;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  height: 100%;

  // 搜索框样式
  .search-container {
    padding: 40rpx;
    border-bottom: 2rpx solid #ebecf0;

    .search-box {
      border: 2rpx solid rgba(235, 236, 240, 1);
      padding: 22rpx 24rpx;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      background: #ffffff;

      input {
        font-size: 28rpx;
        flex: 1;
        min-width: 0;
        color: rgba(49, 52, 54, 1);
      }

      .search-icon {
        width: 32rpx;
        height: 32rpx;
        margin-left: 16rpx;
      }
    }
  }

  // 搜索结果样式
  .search-result {
    flex: 1;
    overflow-y: auto;
    padding: 0 24rpx;
    max-height: 600rpx;

    .search-item {
      padding: 24rpx 0;
      // border-bottom: 1rpx solid rgba(235, 236, 240, 1);

      &:last-child {
        border-bottom: none;
      }

      .search-item-text {
        font-size: 28rpx;
        color: rgba(49, 52, 54, 1);
        line-height: 40rpx;
      }

      &:active {
        background: rgba(247, 248, 250, 1);
      }
    }
  }

  // 无搜索结果样式
  .no-result {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 80rpx 0;

    .no-result-text {
      font-size: 28rpx;
      color: rgba(194, 197, 204, 1);
    }
  }

  .major-content-center {
    display: flex;
    flex: 1;
    min-height: 0;

    .select-item {
      flex: 1;
      height: 100%;
      position: relative;

      &:last-child {
        &::after {
          display: none;
        }
      }

      &::after {
        position: absolute;
        content: "";
        display: block;
        width: 1rpx;
        height: 100%;
        background: rgba(235, 236, 240, 1);
        right: 0;
        top: 0;
      }

      .select-text {
        font-size: 26rpx;
        padding: 24rpx 32rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 40rpx;
        color: rgba(49, 52, 54, 1);
        transition: all 0.2s ease;

        &.active {
          color: rgba(230, 0, 3, 1);
          font-weight: bold;
          position: relative;
          background: #fff;
        }
      }
    }

    // 第一列样式
    .select-item:first-child {
      background: rgba(247, 248, 250, 0.5);
      .select-text {
        background: transparent;
        &.active::after {
          display: block;
          position: absolute;
          content: " ";
          width: 4rpx;
          height: 100%;
          background-color: #fff;
          top: 0;
          right: 0;
          z-index: 2;
        }

        &.active {
          background: #fff;
        }
      }
    }
  }
}
