// 微信浏览器
export function isWXBrowser() {
  var ua = window.navigator.userAgent.toLowerCase()
  if (ua.match(/MicroMessenger/i) == "micromessenger") {
    return true
  } else {
    return false
  }
}
// 企业微信
export function isWxWorkBrowser() {
  var ua = navigator.userAgent.toLowerCase()
  let isWx = ua.match(/MicroMessenger/i) == "micromessenger"
  // console.log(ua)
  if (!isWx) {
    return false
  } else {
    let isWxWork = ua.match(/WxWork/i) == "wxwork"
    if (isWxWork) {
      return true
    } else {
      return false
    }
  }
}

//QQ浏览器
export function isQQBrowser() {
  var ua = navigator.userAgent
  return (
    /(iPad|iPhone|iPod).*? (IPad)?QQ\/([\d]+)/.test(ua) ||
    /\bV1_AND_SQI?_([\d]+)(.*? QQ\/([\d]+))?/.test(ua)
  )
}

export function isIOSBrowser() {
  return !!navigator.userAgent.match(/iPhone|iPad|iPod|Mac/i)
}

export function IsPCBrowser() {
  var userAgentInfo = navigator.userAgent
  var Agents = ["Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"]
  var flag = true
  for (var v = 0; v < Agents.length; v++) {
    if (userAgentInfo.indexOf(Agents[v]) > 0) {
      flag = false
      break
    }
  }
  return flag
}
