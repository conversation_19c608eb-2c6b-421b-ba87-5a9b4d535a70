<template>
  <div class="report-card" :class="{ 'pd-style': isCollapse, 'right-angle': isNeedRightAngle }">
    <div class="report-card-header">
      <div class="title">
        <img
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_ai_interview/report/report_tips.png"
          class="img"
        />
        <span class="text">{{ title }}</span>
      </div>
      <div v-if="isHaveRight" class="right" @click="clickEvent">
        <div v-if="isHaveRight === '1'" class="time-text">
          {{ updateTime }}
        </div>
      </div>
    </div>

    <div
      v-if="isCollapse"
      class="report-card-content"
      :class="{
        'max-style': isOverFlow && !isOpen,
        mx7: isHaveVideo && isOverFlow && !isOpen,
      }"
    >
      <slot />
    </div>
    <div v-else class="report-card-content">
      <slot />
    </div>

    <div v-if="isCollapse && isOverFlow" class="collapse">
      <div class="collapse-image" @click="changeCollapse">
        <img
          v-if="!isOpen"
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/question_result/report_zhankai.png"
        />
        <img
          v-else
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/question_result/report_shouqi.png"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted } from "vue"

// Props定义
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  isCollapse: {
    type: Boolean,
    default: false,
  },
  isHaveVideo: {
    type: Boolean,
    default: false,
  },
  isHaveRight: {
    type: String,
    default: "",
  },
  isNeedRightAngle: {
    type: Boolean,
    default: false,
  },
  activeIndex: {
    type: Number,
    default: 999,
  },
  updateTime: {
    type: String,
    default: "更新时间：2025/06/13 18:21",
  },
})

// Emits定义
const emit = defineEmits(["clickEvent"])

// 响应式数据
const isOpen = ref(false)
const isOverFlow = ref(false)
const isGetInfo = ref(false)

// 方法
const changeCollapse = () => {
  isOpen.value = !isOpen.value
}

const clickEvent = () => {
  emit("clickEvent")
}

const calculateHeight = () => {
  if (props.isCollapse) {
    if (isGetInfo.value) {
      return
    }

    nextTick(() => {
      const element = document.querySelector(".report-card-content")
      if (element) {
        const height = element.offsetHeight
        let overFlow = false
        if (props.isHaveVideo) {
          overFlow = height > 290 * (37.5 / 75) // 转换为rem对应的px值
        } else {
          overFlow = height > 220 * (37.5 / 75) // 转换为rem对应的px值
        }
        isOverFlow.value = overFlow
        isGetInfo.value = true
      }
    })
  }
}

// 监听activeIndex变化
watch(
  () => props.activeIndex,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      calculateHeight()
    }
  }
)

// 生命周期
onMounted(() => {
  calculateHeight()
})
</script>

<style lang="scss" scoped>
.report-card {
  box-sizing: border-box;
  padding: 0.533rem 0.427rem; // 40rpx 32rpx = 40/75 32/75
  background: #ffffff;
  border-radius: 0.213rem; // 16rpx = 16/75
  margin-bottom: 0.213rem; // 16rpx = 16/75

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.533rem; // 40rpx = 40/75

    .title {
      display: flex;
      align-items: center;

      .img {
        height: 0.453rem; // 34rpx = 34/75
        width: 0.107rem; // 8rpx = 8/75
        margin-right: 0.32rem; // 24rpx = 24/75
      }

      .text {
        font-size: 0.48rem; // 36rpx = 36/75
        font-weight: bold;
        color: #22242e;
        line-height: 1;
      }
    }

    .right {
      display: flex;
      align-items: center;

      .time-text {
        font-size: 0.293rem; // 22rpx = 22/75
        color: #e60003;
      }
    }
  }

  .report-card-content {
    &.max-style {
      max-height: 5.333rem; // 400rpx = 400/75
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
    }

    &.mx7 {
      max-height: 9.333rem; // 700rpx = 700/75
      -webkit-line-clamp: unset;
    }
  }

  .collapse {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    margin-top: 0.213rem; // 16rpx = 16/75

    &-image {
      width: 2.347rem; // 176rpx = 176/75
      text-align: center;

      img {
        width: 0.32rem; // 24rpx = 24/75
        height: 0.32rem; // 24rpx = 24/75
      }
    }
  }
}

.pd-style {
  padding-bottom: 0.32rem !important; // 24rpx = 24/75
}

.right-angle {
  border-radius: 0;
}
</style>
