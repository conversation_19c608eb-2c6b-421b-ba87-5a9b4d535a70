// 排序列表样式
.sort-list {
  display: flex;
  flex-direction: column;

  &-item {
    padding: 24rpx 0;
    font-size: 26rpx;
    color: rgba(102, 102, 102, 1);
    position: relative;
    text-align: center;

    &:last-child {
      border-bottom: none;
    }

    &.active {
      color: #e60003;

      &::after {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 32rpx;
        height: 32rpx;
        background: url("https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/correct/check_red.png")
          no-repeat center;
        background-size: contain;
      }
    }
  }
}
