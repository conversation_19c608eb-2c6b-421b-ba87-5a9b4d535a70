.score-trend {
  background: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
}

.chart-header {
  margin-bottom: 24rpx;
}

.chart-title {
  font-size: 28rpx;
  color: #3C3D42;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 24rpx;
  color: #919499;
  margin-left: 8rpx;
}

.score-info {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}

.score-item {
  display: flex;
  align-items: center;
  margin-right: 32rpx;
}

.score-item .label {
  font-size: 24rpx;
  color: #919499;
  margin-right: 8rpx;
}

.score-item .value {
  font-size: 28rpx;
  font-weight: bold;
}

.score-item .my-score {
  color: #D62828;
}

.score-item .avg-score {
  color: #919499;
}

.chart-container {
  width: 100%;
  height: 500rpx;
}

.chart-scroll-container {
  width: 100%;
  height: 500rpx;
  background: #fff;
  overflow: hidden;
}

.chart-scroll-view {
  width: 100%;
  height: 100%;
  
  .chart-container-inner {
    height: 100%;
    min-width: 100%;
    position: relative;
    
    ec-canvas {
      width: 100% !important;
      height: 100% !important;
      display: block;
    }
  }
}

/* 隐藏滚动条但保持可滚动 */
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}