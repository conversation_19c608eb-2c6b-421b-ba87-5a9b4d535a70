.education-add {
  min-height: 100vh;
  background: #ffffff;
  .bg-area {
    width: 100%;
    height: 16rpx;
    background: #f7f8fa;
  }
  .form-area {
    .form-item {
      padding: 48rpx 40rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 2rpx solid #ebecf0;
      .left {
        color: #666666;
        font-size: 28rpx;
        display: flex;
        .cred {
          font-size: 26rpx;
          color: #e60003;
          margin-left: 8rpx;
          margin-top: 2rpx;
        }
      }
      .right {
        display: flex;
        align-items: center;
        .text {
          color: #c2c5cc;
          font-size: 26rpx;
          margin-right: 4rpx;
          max-width: 410rpx;

          &.has-value {
            color: #3c3d42;
            font-size: 26rpx;
          }
        }
        .arrow {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }
  .bottom-btn {
    width: 100%;
    padding: 14rpx 32rpx;
    box-sizing: border-box;
    .btn {
      width: 100%;
      height: 84rpx;
      background: #ec3e33;
      border-radius: 16rpx;
      color: #ffffff;
      font-size: 28rpx;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0.5;

      &.active {
        opacity: 1;
      }
    }
  }
}

/* 选择器样式 */
.top-area {
  .van-picker__confirm {
    color: #e60003 !important;
  }
}

.column-area {
  .van-picker-column__item {
    &.van-picker-column__item--selected {
      color: #3c3d42;
    }
  }
}

.active-item {
  color: #3c3d42;
}
