/**
 * 构建考试动态API请求参数
 * @param {Object} filterConditions 筛选条件
 * @returns {Object} API请求参数
 */
export function buildNewsApiParams(filterConditions = {}) {
  console.log("构建考试动态API参数，输入的filterConditions:", filterConditions)
  const params = {}

  // 地区参数 - 使用与公告Tab相同的地区代码提取逻辑
  if (
    filterConditions.apply_region &&
    filterConditions.apply_region.length > 0
  ) {
    params.region = filterConditions.apply_region
      .map((region) => {
        // 如果已经是字符串格式，直接使用
        return region.key
      })
      .filter((regionCode) => regionCode) // 过滤掉空值
    console.log("考试动态地区参数处理:", params.region)
  } else {
    params.region = []
    console.log("考试动态地区参数处理: 传递空数组")
  }

  // 考试类型参数 - 直接使用value数组
  if (filterConditions.exam_type && filterConditions.exam_type.length > 0) {
    params.exam_type = filterConditions.exam_type
    console.log("考试动态考试类型参数处理:", params.exam_type)
  } else {
    params.exam_type = []
    console.log("考试动态考试类型参数处理: 传递空数组")
  }

  // 其他筛选条件
  Object.keys(filterConditions).forEach((key) => {
    if (
      key !== "region" &&
      key !== "exam_type" &&
      filterConditions[key] !== null &&
      filterConditions[key] !== undefined
    ) {
      params[key] = filterConditions[key]
    }
  })

  console.log("最终构建的考试动态API参数:", params)
  return params
}
