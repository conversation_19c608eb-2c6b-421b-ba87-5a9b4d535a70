/**
 * 菜单筛选弹窗组件（Slot版）
 * 提供遮罩层和弹窗容器，内容通过 slot 传入
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false,
    },
    // 弹窗顶部位置（像素值）
    popupTop: {
      type: Number,
      value: 0,
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: "",
    },
    // 遮罩层是否可点击关闭
    maskClosable: {
      type: Boolean,
      value: true,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 动画显示状态（合并后的弹窗状态）
    showPopup: false,
    // 遮罩层动画状态
    showOverlay: false,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 遮罩层点击事件
     */
    handleOverlayTap() {
      if (this.properties.maskClosable) {
        this.triggerEvent("close", {}, {})
      }
    },
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
    },

    detached() {
      // 组件实例被从页面节点树移除时执行
    },
  },

  /**
   * 数据监听器
   */
  observers: {
    show: function (newVal) {
      // 直接同步状态，避免异步导致的状态不一致
      if (newVal) {
        // 显示弹窗 - 确保遮罩和弹窗同时显示
        this.setData({
          showOverlay: true,
          showPopup: true,
        })
      } else {
        // 隐藏弹窗 - 确保遮罩和弹窗同时隐藏
        this.setData({
          showOverlay: false,
          showPopup: false,
        })
      }
    },
  },
})
