import Cookies from "js-cookie"

// 清除价格零
export const clearPriceZero = (old) => {
  if (!old) return old

  // 拷贝一份 返回去掉零的新串
  let newstr = old
  // 循环变量 小数部分长度
  const leng = old.length - old.indexOf(".") - 1

  // 判断是否有效数
  if (old.indexOf(".") > -1) {
    // 循环小数部分
    for (let i = leng; i > 0; i--) {
      // 如果newstr末尾有0
      if (newstr.lastIndexOf("0") > -1 && parseInt(newstr.substr(newstr.length - 1, 1)) === 0) {
        const k = newstr.lastIndexOf("0")
        // 如果小数点后只有一个0 去掉小数点
        if (newstr.charAt(k - 1) === ".") {
          return newstr.substring(0, k - 1)
        } else {
          // 否则 去掉一个0
          newstr = newstr.substring(0, k)
        }
      } else {
        // 如果末尾没有0
        return newstr
      }
    }
  }

  return old
}

// 判断是否为json
export function isJSON(str) {
  if (typeof str === "string") {
    try {
      const obj = JSON.parse(str)
      return !!(typeof obj === "object" && obj)
    } catch (e) {
      console.log("error：" + str + "!!!" + e)
      return false
    }
  }
  console.log("It is not a string!")
}

export const formatSingleNumber = (number) => {
  return number <= 9 ? "0" + number : number
}

// 格式化日期
export const formatDate = (time) => {
  const date = new Date(time * 1000),
    year = date.getFullYear(),
    month = formatSingleNumber(date.getMonth() + 1),
    day = formatSingleNumber(date.getDate()),
    hour = formatSingleNumber(date.getHours()),
    minute = formatSingleNumber(date.getMinutes()),
    second = formatSingleNumber(date.getSeconds()),
    md = month + "月" + day + "日",
    ms = minute + ":" + second,
    hm = hour + ":" + minute,
    hms = hour + ":" + ms

  return {
    year,
    month,
    day,
    hour,
    minute,
    second,
    md,
    ms,
    hm,
    hms,
  }
}
// 格式化时间戳
export const formatTime = (time) => {
  if (time <= 0) time = 0
  time = time >>> 0
  const day = parseInt(time / (3600 * 24)),
    hour = parseInt((time % (3600 * 24)) / 3600),
    minute = parseInt((time % 3600) / 60),
    second = parseInt(time % 60),
    ms = formatSingleNumber(minute) + ":" + formatSingleNumber(second),
    hms = formatSingleNumber(hour) + ":" + ms
  return {
    day,
    hour,
    minute,
    second,
    ms,
    hms,
  }
}

/**
 * 节流函数
 * @param {*} fn 是我们需要包装的事件回调
 * @param {*} wait 是每次推迟执行的等待时间
 */
export const throttle = (fn, wait = 1000) => {
  let last
  let timer
  return function () {
    let context = this,
      args = arguments
    let now = new Date()

    if (last && now - last < wait) {
      // 如果在指定的时间内，则重置定时器
      clearTimeout(timer)
      timer = setTimeout(function () {
        last = now
        // fn.apply(context, args);
      }, wait)
    } else {
      // 如果超过了指定的时间，则直接执行函数
      last = now
      fn.apply(context, args)
    }
  }
}

/**
 *
 * @param {*} fn  是我们需要包装的事件回调
 * @param {*} delay 是每次推迟执行的等待时间
 */
export const debounce = (fn, delay = 1000) => {
  // 定时器
  let timer = null
  // 将debounce处理结果当作函数返回
  return function () {
    // 保留调用时的this上下文
    let context = this
    // 保留调用时传入的参数
    let args = arguments
    // 每次事件被触发时，都去清除之前的旧定时器
    if (timer) {
      clearTimeout(timer)
    }
    // 设立新定时器
    timer = setTimeout(function () {
      fn.apply(context, args)
    }, delay)
  }
}
export function isPcBrowser() {
  var userAgentInfo = navigator.userAgent
  var Agents = new Array("Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod")
  var flag = true
  for (var v = 0; v < Agents.length; v++) {
    if (userAgentInfo.indexOf(Agents[v]) > 0) {
      flag = false
      break
    }
  }

  return flag
}

// 判断是否是数组
export function isArray(array) {
  return Object.prototype.toString.call(array) === "[object Array]"
}
// 判断数组是否存在某值
export function isInArray(array, val) {
  return isArray(array) && array.includes(val)
}
// 获取数组元素最大长度
export function getArrayMaxLength(array) {
  if (!isArray(array) || array.length === 0) {
    return 0
  }

  let result = 0
  array.forEach((element) => {
    let len = this.getStringLength(element)
    if (result < len) {
      result = len
    }
  })
  return result
}
// 获取字符长度
export function getStringLength(str) {
  if (
    Object.prototype.toString.call(str) === "[object Number]" ||
    Object.prototype.toString.call(str) === "[object String]"
  ) {
    return str.toString().length
  }
  return 0
}
// 对比数组是否一样
export function compareArrays(array1, array2) {
  if (array1.length !== array2.length) {
    return false
  }

  return array1.every((element, index) => {
    return array2.includes(element)
  })
}

export function add(num1, num2) {
  return Number(((num1 * 100 + num2 * 100) / 100).toFixed(2))
}

export function calculateAccuracy(correctAnswers = 0, totalQuestions = 0) {
  // 计算正确率
  const accuracy = (correctAnswers / totalQuestions) * 100 // 结果以百分比形式

  // 返回保留两位小数的正确率
  return parseFloat(accuracy.toFixed(2))
}

/**
 * 将url和参数拼接成完整地址
 * @param {string} url url地址
 * @param {Json} data json对象
 * @returns {string}
 */
export const getUrl = (url, data) => {
  let paramUrl = ""
  for (const k in data) {
    const value = data[k] !== undefined ? data[k] : ""
    paramUrl += `&${k}=${encodeURIComponent(value)}`
  }
  paramUrl = paramUrl ? paramUrl.substring(1) : ""
  // 看原始url地址中开头是否带?，然后拼接处理好的参数
  return (url += (url.indexOf("?") < 0 ? "?" : "") + paramUrl)
}

/*
 * changeURLStatic 修改地址栏URL参数 不跳转
 *   @param name 参数名
 *   @param value 参数值
 *
 * */
export function changeURLStatic(name, value) {
  var url = location.href
  var reg = eval("/([?|&]" + name + "=)[^&]*/gi")
  value = value.toString().replace(/(^\s*)|(\s*$)/g, "") // 移除首尾空格
  if (!value) {
    var url2 = url.replace(reg, "") // 正则替换
  } else {
    if (url.match(reg)) {
      var url2 = url.replace(reg, "$1" + value) // 正则替换
    } else {
      var url2 = url + (url.indexOf("?") > -1 ? "&" : "?") + name + "=" + value // 没有参数添加参数
    }
  }
  history.replaceState(null, null, url2) // 替换地址栏
}

/**
 * 时间戳转时长
 * 把秒数转化为天、时、分、秒
 * 参数value是秒数
 */
export function formatSeconds(value) {
  let secondTime = parseInt(value) // 秒
  let minuteTime = 0 // 分
  let hourTime = 0 // 小时
  let dayTime = 0 // 天
  let result = ""
  if (value < 60) {
    result = secondTime + "秒"
  } else {
    if (secondTime >= 60) {
      // 如果秒数大于60，将秒数转换成整数
      // 获取分钟，除以60取整数，得到整数分钟
      minuteTime = parseInt(secondTime / 60)
      // 获取秒数，秒数取佘，得到整数秒数
      secondTime = parseInt(secondTime % 60)
      // 如果分钟大于60，将分钟转换成小时
      if (minuteTime >= 60) {
        // 获取小时，获取分钟除以60，得到整数小时
        hourTime = parseInt(minuteTime / 60)
        // 获取小时后取佘的分，获取分钟除以60取佘的分
        minuteTime = parseInt(minuteTime % 60)
        if (hourTime >= 24) {
          // 获取天数， 获取小时除以24，得到整数天
          dayTime = parseInt(hourTime / 24)
          // 获取小时后取余小时，获取分钟除以24取余的分；
          hourTime = parseInt(hourTime % 24)
        }
      }
    }
    if (secondTime > 0) {
      secondTime = parseInt(secondTime) >= 10 ? secondTime : "0" + secondTime
      result = "" + secondTime
    }
    if (minuteTime > 0) {
      minuteTime = parseInt(minuteTime) >= 10 ? minuteTime : "0" + minuteTime
      result = "" + minuteTime + ":" + result
      if (hourTime === 0 && dayTime === 0) {
        hourTime = parseInt(hourTime) >= 10 ? hourTime : "0" + hourTime
        result = "" + hourTime + ":" + result
      }
    }
    if (hourTime > 0) {
      hourTime = parseInt(hourTime) >= 10 ? hourTime : "0" + hourTime
      result = "" + hourTime + ":" + result
    }
    if (dayTime > 0) {
      result = "" + parseInt(dayTime) + ":" + result
    }
  }
  return result
}

// 获取html字符串body内容
export function getHtmlStringBodyContent(htmlString) {
  const rx = /<body[^>]*>([\s\S]+?)<\/body>/i ///
  const m = rx.exec(htmlString)
  return m[1]
}

export function pushWindowHistoryState(newPath) {
  // 使用history API手动更改浏览器URL
  window.history.pushState(null, null, newPath)
}
export function replaceWindowHistoryState(newPath) {
  // 使用history API手动更改浏览器URL
  window.history.replaceState(null, null, newPath)
}

export function windowLocationHref(url) {
  window.location.href = url
}
export function windowLocationOpen(url) {
  window.open(url)
}
export function windowLocationReplace(url) {
  window.location.replace(url)
}

export function isMobileDevice() {
  return typeof window !== "undefined" && /Mobi|Android/i.test(navigator.userAgent)
}

// 设置 meta
export function setMeta(attributes) {
  console.log(attributes)

  // 确保至少有 property 或 name 以及 content 属性被提供
  if ((!attributes.property && !attributes.name) || attributes.content === undefined) {
    console.warn('setMeta至少需要一个 "property" or "name" 和一个 "content" 属性.')
    return
  }

  // 获取 head 部分
  let head = document.head || document.getElementsByTagName("head")[0]

  // 构建选择器字符串用于查找已存在的 meta 标签
  let selector = attributes.property
    ? `meta[property="${attributes.property}"]`
    : `meta[name="${attributes.name}"]`

  // 查找是否已经存在相同 property 或 name 的 meta 标签
  let existingMeta = head.querySelector(selector)

  if (existingMeta) {
    // 如果存在，则更新 content 属性
    existingMeta.setAttribute("content", attributes.content)
  } else {
    // 如果不存在，则创建一个新的 meta 元素
    let meta = document.createElement("meta")

    // 设置 meta 的属性
    if (attributes.property) {
      meta.setAttribute("property", attributes.property)
    } else if (attributes.name) {
      meta.setAttribute("name", attributes.name)
    }

    meta.setAttribute("content", attributes.content)

    // 将新的 meta 添加到 head 中
    head.appendChild(meta)
  }
}
