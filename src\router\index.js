import { createRouter, createWebHistory } from "vue-router"
import BigDataView from "@/views/exam/bigData.vue"

const router = createRouter({
  history: createWebHistory("/position"),
  routes: [
    {
      path: "/exam/bigData",
      name: "big-data",
      component: BigDataView,
      meta: {
        title: "报名大数据",
        requiresAuth: false,
      },
    },
    {
      path: "/exam/interviewScore",
      name: "interview-score",
      component: () => import("@/views/exam/interviewScore.vue"),
      meta: {
        title: "进面分数线",
        requiresAuth: false,
      },
    },
    {
      path: "/job/detail",
      name: "job-detail",
      component: () => import("@/views/job/detail.vue"),
      meta: {
        title: "职位详情",
        requiresAuth: false,
      },
    },
    {
      path: "/notice/collection",
      name: "notice-collection",
      component: () => import("@/views/notice/collection.vue"),
      meta: {
        title: "公告合辑",
        requiresAuth: false,
      },
    },
    {
      path: "/notice/detail",
      name: "notice-detail",
      component: () => import("@/views/notice/detail.vue"),
      meta: {
        title: "公告详情",
        requiresAuth: false,
      },
    },
    {
      path: "/notice/dynamics",
      name: "notice-dynamics",
      component: () => import("@/views/notice/dynamics.vue"),
      meta: {
        title: "考试动态",
        requiresAuth: false,
      },
    },
  ],
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title}`
  }

  next()
})

export default router
