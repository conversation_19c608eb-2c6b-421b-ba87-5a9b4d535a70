<view class="card-type-advclass  {{(item.card_type ===4|| item.card_type ===5) ?'min':''}} {{(item.card_type ===3||item.card_type ===1)?'big':''}}" style="background: url({{(item.card_type === 3 || item.card_type === 5) && item.course_bgimg ? item.course_bgimg : IMAGE_PREFIX + 'images/course/course_bg_small.png'}}) top / 100% 100% no-repeat;" bindtap="goDetail" data-item="{{item}}">
  <view class="advclass-status" wx:if="{{item.card_left_top.show===1}}">
    <!-- {{((item.card_type ===3|| item.card_type ===5) && item.course_bgimg)?item.course_bgimg:'{{IMAGE_PREFIX}}images/course/course_bg_small.png'}} -->
    <image class="image" wx:if="{{item.card_left_top.show_master_headimg&&item.teacher.portrait}}" src="{{item.teacher.portrait}}"></image>
    {{item.card_left_top.text}}
  </view>
  <view class="desk-list">
    <advclass-desk wx:for="{{item.student_list}}" wx:key="index" wx:for-item="student" studentList="{{student}}"></advclass-desk>
  </view>
  <view class="fonts-box">
    <!-- 中卡片 -->
    <block wx:if="{{item.card_type ===3||item.card_type ===1}}">
      <view class="fonts">
        <view class="flex-1">
          <view class="title text-ellipsis-1">{{item.name}}</view>
          <view class="intro-box">
            <text class="student-label">{{item.student_num_label}}</text>
            <text class="texts text-ellipsis-1">{{item.intro}}</text>
          </view>
        </view>
        <view class="right-btn-s">
          <view class="text ">{{item.btn_txt}}</view>
          <image class="image" src="{{IMAGE_PREFIX}}images/course/course_red_arrow.png"></image>
        </view>
      </view>
    </block>
    <view class="fonts" wx:else>
      <view class="title text-ellipsis-1">{{item.name}}</view>
      <image class="image" src="{{IMAGE_PREFIX}}images/course/course_arrow-right.png"></image>
    </view>
  </view>
</view>
<!-- <view class="card-type-advclass min" style="background-image:url({{IMAGE_PREFIX}}images/course/course_bg-default.png) top / 100% 100% no-repeat;">
  <view class="advclass-status">
    <image src="https://central-1253756937.cos.ap-chengdu.myqcloud.com/course/teacher/2022-3-14/16472546267532.png"></image>
    老师督学中
  </view>
  <view class="desk-list">
  </view>
  <view class="fonts-box">
    <view class="fonts">
      <view class="title text-ellipsis-1">事业单位备考无忧高端班事业单位备考无忧高端班事业单位备考无忧高端班</view>
      <image src="{{IMAGE_PREFIX}}images/course/course_arrow-right.png"></image>
    </view>
  </view>
</view> -->