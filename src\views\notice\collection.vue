<template>
  <template v-if="isRequest">
    <template v-if="noticeData.detail">
      <template v-if="pageType == 'collection'">
        <!-- 专题合辑 -->
        <div
          class="page-container"
          :style="{ backgroundImage: `url(${noticeData?.detail?.cover_img})` }"
        >
          <div class="top-content">
            <div class="title">{{ noticeData?.detail?.title }}</div>
            <div class="label">{{ noticeData?.detail?.summary }}</div>
          </div>

          <div class="center-content">
            <div class="update-time-all">
              共收录
              <span class="num">{{ noticeData?.detail?.total_num }}</span>
              场考试
              <span class="time">更新时间：{{ noticeData?.detail?.update_time }}</span>
            </div>
            <div class="list-content">
              <!-- 公告卡片列表组件 -->
              <NoticeCard
                v-if="articleList?.length > 0"
                :list="articleList"
                @goDetail="handleGoDetail"
              />
              <!-- 空状态 -->
              <div v-else-if="articleList?.length === 0 && !loading" class="empty-default">
                暂无更多公告~
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-if="pageType == 'detail'">
        <div class="main-content">
          <!-- 转换后的HTML代码 -->
          <div class="header-top">
            <div class="title">{{ noticeData?.detail?.title }}</div>
            <div class="label">
              <span class="address" v-if="noticeData?.detail?.release_source">
                {{ noticeData?.detail?.release_source }}
              </span>
              <span
                class="time"
                v-if="noticeData?.detail?.release_source"
                :class="noticeData?.detail?.release_source ? '' : 'pl0'"
              >
                {{ noticeData?.detail?.release_time }}
              </span>
            </div>
          </div>

          <!-- 公告详情 -->
          <template v-if="indexToTabKey[activeIndex] === 'detail'">
            <div class="content-box bor_bb">
              <div class="content-center">
                <div class="notice-box" v-if="noticeData?.detail?.timeline?.length > 0">
                  <div
                    class="notice-box-top"
                    :class="{ bt_0: noticeData?.detail?.timeline?.length === 0 }"
                  >
                    <div class="left">
                      <span v-if="noticeData?.detail?.need_num >= 0" class="left-text">
                        共招
                        <span class="num">{{ noticeData?.detail?.need_num }}</span>
                        人
                      </span>
                      <span v-if="noticeData?.detail?.job_num >= 0" class="left-text">
                        <span class="num">{{ noticeData?.detail?.job_num }}</span>
                        个职位
                      </span>
                    </div>
                    <div
                      v-if="noticeData?.detail?.apply_status?.text"
                      class="status"
                      :style="{ color: noticeData?.detail?.apply_status?.color }"
                    >
                      {{ noticeData?.detail?.apply_status?.text }}
                    </div>
                  </div>
                  <div v-if="noticeData?.detail?.timeline?.length > 0" class="time-line">
                    <div
                      v-for="(item, index) in noticeData?.detail?.timeline"
                      :key="index"
                      class="time-line-item"
                      :class="{ active: item.is_progressing }"
                    >
                      <div class="dian"></div>
                      <div class="text-word">{{ item.title }}</div>
                      <div class="time">{{ item.text }}</div>
                    </div>
                  </div>
                </div>

                <div
                  class="rich-text-box"
                  :class="noticeData?.detail?.timeline?.length > 0 ? 'pr-box' : ''"
                  v-if="noticeData?.detail?.content?.body_content"
                >
                  <div
                    id="richTextContainer"
                    class="rich-text-container"
                    :class="{ expanded: isExpanded, collapsed: !isExpanded }"
                    :style="{
                      maxHeight: isExpanded ? 'none' : maxHeight + 'px',
                      overflow: 'hidden',
                    }"
                  >
                    <div
                      id="richTextContent"
                      v-html="noticeData?.detail?.content?.body_content"
                    ></div>
                  </div>
                  <div v-if="showToggle" class="bottom-pos">
                    <div v-if="!isExpanded" class="gradient-height"></div>
                    <div class="see-all" :class="{ collapsed: !isExpanded }" @click="toggleText">
                      {{ isExpanded ? "收起" : "展开" }}查看全文
                      <img
                        src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_select.png"
                      />
                    </div>
                  </div>
                  <OpenApp
                    :options="openAppOptions"
                    class="originally"
                    v-if="noticeData?.detail?.release_source"
                  >
                    来源：
                    <span class="title">{{ noticeData?.detail?.release_source }}</span>
                    <a
                      :href="noticeData?.detail?.source_url"
                      v-if="noticeData?.detail?.source_url"
                      style="display: inline-block"
                      @click="copyUrl(noticeData?.detail?.source_url)"
                    >
                      <img
                        class="copy-img"
                        src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_copy_blue.png"
                      />
                      <span class="copy-c">复制</span>
                    </a>
                  </OpenApp>
                </div>

                <OpenApp
                  :options="openAppOptions"
                  v-if="noticeData?.type === 'article_list'"
                  class="see-address"
                  @click="openExam"
                >
                  点击查看各地区及单位考试列表
                  <img
                    src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_right_red.png"
                  />
                </OpenApp>

                <div
                  v-if="noticeData?.detail?.content?.attachment?.length > 0"
                  class="attachment-box"
                >
                  <div class="title">附件</div>
                  <div class="information-list">
                    <a
                      :href="item.url"
                      v-for="(item, index) in noticeData?.detail?.content?.attachment"
                      :key="index"
                      class="information-list-item"
                      @click="tapAttachment(item)"
                    >
                      <img
                        class="type-img"
                        src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_information_link.png"
                      />
                      <span class="text">{{ item.name }}</span>
                      <img
                        class="right-arrow"
                        src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_right_h.png"
                      />
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- 热门问答 -->
            <div v-if="noticeData?.detail?.hot_qna" class="popular-answer bor_bb">
              <div class="item-title">备考热门问答</div>
              <div v-if="noticeData?.detail?.hot_qna?.qna_list?.length > 0" class="answer-list">
                <div
                  v-for="(item, index) in noticeData?.detail?.hot_qna?.qna_list"
                  :key="index"
                  class="answer-list-item"
                >
                  <div class="ask-box">
                    <img
                      src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_ask.png"
                    />
                    <span class="text-t">{{ item.q }}</span>
                  </div>
                  <div class="answer-box">
                    <img
                      src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_answer.png"
                    />
                    <span class="text-t">{{ item.a }}</span>
                  </div>
                </div>
              </div>
              <OpenApp
                :options="openAppOptions"
                v-if="noticeData?.detail?.hot_qna?.consult"
                class="more-button"
                @click="tapCustomerService(noticeData?.detail?.hot_qna?.consult)"
              >
                {{ noticeData?.detail?.hot_qna?.consult?.btn_title }}
                <img
                  src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_right_h.png"
                />
              </OpenApp>
            </div>

            <!-- 工具 -->
            <div v-if="noticeData?.detail?.exam_tool?.length > 0" class="tool-box bor_bb">
              <div class="item-title">考试工具</div>
              <div class="tool-list">
                <OpenApp
                  :options="openAppOptions"
                  v-for="(item, index) in noticeData?.detail?.exam_tool"
                  :key="index"
                  class="tool-list-item"
                  @click="tapCustomerService(item)"
                >
                  <img :src="item.icon" />
                  {{ item.title }}
                </OpenApp>
              </div>
            </div>

            <!-- 笔试备考 -->
            <div
              v-if="
                noticeData?.detail?.written_exam_prepare?.length > 0 ||
                noticeData?.detail?.bottom_ad?.length > 0
              "
              class="tool-box"
            >
              <div class="item-title">笔试备考</div>
              <div class="written-list">
                <OpenApp
                  :options="openAppOptions"
                  v-for="(item, index) in noticeData?.detail?.written_exam_prepare"
                  :key="index"
                  class="written-list-item"
                  @click="tapCustomerService(item)"
                >
                  <span class="title">{{ item.title }}</span>
                  <img class="img" :src="item.icon" />
                </OpenApp>
              </div>
              <OpenApp
                :options="openAppOptions"
                v-if="noticeData?.detail?.bottom_ad?.[0]?.img"
                class="bd-box"
              >
                <img
                  class="bd-img"
                  :src="noticeData?.detail?.bottom_ad?.[0]?.img"
                  @click="tapCustomerService(noticeData?.detail?.bottom_ad?.[0])"
                />
              </OpenApp>
            </div>

            <!-- 声明 -->
            <div class="statement-box">
              声明：本站发布的招考资讯均来源于招录官方网站，由金标尺整理编辑，如遇报考疑问请咨询招考官方，若涉及版权或错误信息，请提交反馈到本站予以更新或删除。
            </div>
            <!-- 底部操作栏 -->
            <div class="action-bar-box">
              <div
                v-if="indexToTabKey[activeIndex] === 'detail'"
                class="action-bar container flex-justify_between"
              >
                <div class="flex-c">
                  <OpenApp
                    :options="openAppOptions"
                    v-if="noticeData?.join_group"
                    class="bottom-item"
                    @click="tapCustomerService(noticeData?.join_group)"
                  >
                    <img
                      src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_qq.png"
                    />
                    加入备考群
                  </OpenApp>
                  <OpenApp
                    :options="openAppOptions"
                    v-if="noticeData?.type === 'article_list'"
                    class="bottom-item"
                    @click="openExam"
                  >
                    <img
                      src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_exam_list.png"
                    />
                    考试列表
                    <div class="new-box">{{ noticeData?.detail?.article_num }}</div>
                  </OpenApp>
                </div>
                <OpenApp
                  :options="openAppOptions"
                  class="filter-button"
                  :class="{ wp100: !noticeData?.join_group && noticeData?.type !== 'article_list' }"
                  @click="changeActiveIndex('position')"
                >
                  查看职位
                </OpenApp>
              </div>
            </div>
          </template>
        </div>
      </template>
    </template>
    <EmptyDefault v-else :openAppOptions="openAppOptions" />
  </template>
  <!-- 悬浮咨询框 -->
  <FloatingBox :options="openAppOptions" v-if="!isInAPP && noticeData.detail" />
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from "vue"
import { useRoute, useRouter } from "vue-router"

// 导入组件
import NoticeCard from "@/components/NoticeCard.vue"
import EmptyDefault from "@/components/EmptyDefault.vue"
// 导入API
import { getArticleDetailRequest, getArticleChildListRequest } from "@/api/notice"
import { isDeviceAppClientCookie } from "@/utils/cookies/modules/appDevice"

import OpenApp from "@/components/OpenApp.vue"
import FloatingBox from "@/components/SideOpenApp.vue"
import { setShare } from "@/utils/share"

// 获取路由实例
const route = useRoute()
const router = useRouter()

// 响应式数据
const noticeData = ref({})
const pageType = ref("")
const loading = ref(false) // 加载状态
const tabList = ref([])
const activeIndex = ref(0)
const indexToTabKey = ref({})
const headerHeight = ref(100) // 默认值，会动态更新
const isExpanded = ref(false)
const showToggle = ref(false)
const maxHeight = ref(2500) // 默认收起状态的最大高度(px)
const actualHeight = ref(0) // 内容实际高度
const pageScrollDisabled = ref(false) // 页面滚动禁用状态
const articleList = ref([]) // 文章列表
const page = ref(1)
const hasMore = ref(true) // 是否还有更多数据
const loadingMore = ref(false) // 加载更多状态
const isRequest = ref(false)

const openAppOptions = ref({
  unitKey: "NoticeJobNoticeDetail",
  param: {
    id: route.query.id,
  },
})

// 组件声明
defineOptions({
  components: {
    FloatingBox,
  },
})

const isInAPP = computed(() => isDeviceAppClientCookie())

// 初始化数据
const initData = async () => {
  try {
    loading.value = true

    // 从路由参数获取ID
    const id = route.query.id
    if (!id) {
      console.error("缺少文章ID参数")
      return
    }

    // 请求文章详情数据
    const response = await getArticleDetailRequest({
      id,
      brand: route.params.brand,
      province: route.params.province,
    })
    console.log("文章详情数据:", response)
    isRequest.value = true
    if (response && response.data) {
      noticeData.value = response.data
      const resData = response.data

      // 根据数据类型判断页面类型
      if (resData.type === "article_list" && resData.show_type === "list") {
        pageType.value = "collection"
        // 如果是专题合辑，可能需要额外请求文章列表数据
        // 这里可以根据实际API调整
        // 重置分页状态
        page.value = 1
        hasMore.value = true
        articleList.value = []

        // 获取合辑列表
        await getArticleChildList(false)
      } else {
        pageType.value = "detail"
        // 初始化详情页的tab数据
        initDetailTabs()
      }
      const shareInfo = response.data.share_info
      setShare({ title: shareInfo.title, desc: shareInfo.desc, imgUrl: shareInfo.img })
    }
  } catch (error) {
    isRequest.value = true
    console.error("获取文章详情失败:", error)
  } finally {
    isRequest.value = true
    loading.value = false
  }
}

// 初始化详情页tabs
const initDetailTabs = () => {
  const allTabsConfig = [
    { key: "detail", title: "公告详情", alwaysShow: true },
    { key: "position", title: "职位列表", conditionField: "job_num" },
    { key: "official", title: "官方动态", conditionField: "notice_num" },
  ]

  const detail = noticeData.value.detail
  const dynamicTabs = []
  const indexToTabKeyObj = {}

  allTabsConfig.forEach((tabConfig) => {
    const { key, title, alwaysShow, conditionField } = tabConfig

    // 总是显示的tab或满足条件的tab
    if (alwaysShow || (conditionField && detail[conditionField] > 0)) {
      const index = dynamicTabs.length
      dynamicTabs.push(title)
      indexToTabKeyObj[index] = key
    }
  })

  tabList.value = dynamicTabs
  indexToTabKey.value = indexToTabKeyObj

  // tabs生成后重新获取header高度
  updateHeaderHeight()

  // 检查富文本内容高度
  checkContentHeight()
}

// 获取合辑列表
const getArticleChildList = async (isLoadMore = false) => {
  try {
    if (loadingMore.value) return // 防止重复请求

    loadingMore.value = true

    // 从路由参数获取ID
    const id = route.query.id
    if (!id) {
      console.error("缺少文章ID参数")
      return
    }

    const response = await getArticleChildListRequest({
      page: page.value,
      article_id: id,
      brand: route.params.brand,
      province: route.params.province,
    })

    console.log("合辑列表数据:", response)

    if (response && response.data) {
      const newList = response.data.list || []

      if (isLoadMore) {
        // 加载更多：追加到现有列表
        articleList.value = [...articleList.value, ...newList]
      } else {
        // 首次加载：替换列表
        articleList.value = newList
      }

      // 判断是否还有更多数据
      hasMore.value = newList.length > 0 && (response.data.has_more || newList.length >= 10)

      // 如果有数据，页码+1，为下次加载做准备
      if (newList.length > 0) {
        page.value += 1
      }
    }
  } catch (error) {
    console.error("获取合辑列表失败:", error)
  } finally {
    loadingMore.value = false
  }
}

// 触底加载更多
const loadMore = () => {
  if (hasMore.value && !loadingMore.value) {
    getArticleChildList(true)
  }
}

// 滚动事件处理
const handleScroll = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const windowHeight = window.innerHeight
  const documentHeight = document.documentElement.scrollHeight

  // 距离底部50px时触发加载
  if (scrollTop + windowHeight >= documentHeight - 50) {
    loadMore()
  }
}

// 防抖函数
let scrollTimer = null
const debouncedHandleScroll = () => {
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
  scrollTimer = setTimeout(handleScroll, 100)
}

// 切换tab
const changeTab = (item, index) => {
  activeIndex.value = index
  console.log("切换到tab:", item, "索引:", index)
}

// 切换到指定tab
const changeActiveIndex = (tabKey) => {
  // 根据tabKey找到对应的索引
  // for (const [index, key] of Object.entries(indexToTabKey.value)) {
  //   if (key === tabKey) {
  //     activeIndex.value = parseInt(index)
  //     console.log("切换到tab:", tabKey, "索引:", index)
  //     break
  //   }
  // }
}

// 搜索
const goSearch = () => {
  console.log("跳转到搜索页面")
}

// 展开/收起文本
const toggleText = () => {
  isExpanded.value = !isExpanded.value
  console.log("切换文本展开状态:", isExpanded.value)
}

// 检查内容高度
const checkContentHeight = () => {
  // 延迟执行，确保rich-text内容已经渲染
  setTimeout(() => {
    checkHeight(0, 3)
  }, 300)
}

// 检查高度的具体实现
const checkHeight = (retryCount, maxRetries) => {
  nextTick(() => {
    const richTextContent = document.getElementById("richTextContent")
    if (richTextContent && richTextContent.offsetHeight > 0) {
      const contentHeight = richTextContent.offsetHeight
      const needToggle = contentHeight > maxHeight.value

      console.log("Rich-text content height:", contentHeight)
      console.log("Max height:", maxHeight.value)
      console.log("Need toggle:", needToggle)

      actualHeight.value = contentHeight
      showToggle.value = needToggle
      // 如果内容高度小于等于maxHeight，默认展开
      isExpanded.value = !needToggle
    } else if (retryCount < maxRetries) {
      // 如果高度获取失败，进行重试
      retryCount++
      console.log(`Rich-text高度检测失败，正在重试 ${retryCount}/${maxRetries}`)
      setTimeout(() => checkHeight(retryCount, maxRetries), 300)
    } else {
      // 重试失败，设置默认状态
      console.warn("Rich-text高度检测失败，使用默认状态")
      showToggle.value = false
      isExpanded.value = true
    }
  })
}

// 打开考试列表
const openExam = () => {
  console.log("打开考试列表")
}

// 点击附件
const tapAttachment = (item) => {
  console.log("点击附件:", item)
}

// 复制链接
const copyUrl = (url) => {
  console.log("复制链接:", url)
  // 这里可以添加复制到剪贴板的逻辑
}

// 客服相关
const tapCustomerService = (item) => {
  console.log("点击客服相关:", item)
}

// 处理公告详情跳转
const handleGoDetail = (item) => {
  console.log("跳转公告详情:", item)
  // 这里可以添加路由跳转逻辑
  if (item.type === "article_list") {
    // 跳转到合辑页面，替换id参数
    router.push({
      name: route.name, // 保持当前路由名称
      // params: {
      //   ...route.params, // 保留其他params参数

      // },
      query: {
        ...route.query, // 保留其他query参数
        id: item.id, // 替换为点击项的id
      },
    })
  } else {
    // 跳转到详情页面，替换id参数
    router.push({
      name: "notice-detail", // 假设详情页路由名为'notice-detail'，请根据实际项目调整
      // params: {
      //   ...route.params, // 保留其他params参数
      //   brand: item.brand || route.params.brand,
      //   province: item.province || route.params.province,
      // },
      query: {
        ...route.query, // 保留其他query参数
        id: item.id, // 替换为点击项的id
      },
    })
  }
}

// 动态获取header高度
const updateHeaderHeight = () => {
  nextTick(() => {
    const headerElement = document.querySelector(".header-top")
    if (headerElement) {
      const height = headerElement.offsetHeight
      headerHeight.value = 0
      console.log("动态获取的header高度:", height + "px")
    }
  })
}

// 防抖函数，避免resize事件频繁触发
let resizeTimer = null
const debouncedUpdateHeaderHeight = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(() => {
    updateHeaderHeight()
    // 窗口大小变化时也重新检查内容高度
    checkContentHeight()
  }, 100) // 100ms防抖
}

// 生命周期
onMounted(() => {
  initData()
  // 在数据初始化后获取header高度
  updateHeaderHeight()

  // 监听窗口大小变化，重新计算header高度（使用防抖）
  window.addEventListener("resize", debouncedUpdateHeaderHeight)

  // 监听滚动事件，实现触底加载
  window.addEventListener("scroll", debouncedHandleScroll)
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener("resize", debouncedUpdateHeaderHeight)
  window.removeEventListener("scroll", debouncedHandleScroll)

  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
})
</script>

<style lang="scss" scoped>
// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #f7f8fa;

  .loading-text {
    font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
    color: #666;
  }
}

.page-container {
  background: #f7f8fa;
  background-repeat: no-repeat;
  background-size: 100%;

  // 专题合辑页面样式
  &.scroll-disabled {
    overflow: hidden;
  }

  // 当有背景图片时的样式
  //   &[style*="background-image"] {
  //     background-size: cover;
  //     background-position: center;
  //     background-repeat: no-repeat;
  //   }
}

// 专题合辑页面样式
.top-content {
  padding: 0 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
  padding-top: 0.53rem;
  .title {
    font-size: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
    color: #fff;
    font-weight: bold;
    letter-spacing: 0.027rem; // 2rpx × 0.5 = 1px, 1px ÷ 37.5 = 0.027rem
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
    line-height: 0.693rem; // 52rpx × 0.5 = 26px, 26px ÷ 37.5 = 0.693rem
    // padding-top: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
    max-width: 7.973rem; // 598rpx × 0.5 = 299px, 299px ÷ 37.5 = 7.973rem
  }

  .label {
    font-size: 0.347rem; // 26rpx × 0.5 = 13px, 13px ÷ 37.5 = 0.347rem
    color: rgba(255, 255, 255, 0.8);
    margin-top: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
  }
}

.center-content {
  border-radius: 0.32rem 0.32rem 0 0; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
  background-color: rgba(247, 248, 250, 1);
  margin-top: 0.64rem; // 68rpx × 0.5 = 34px, 34px ÷ 37.5 = 0.907rem
  flex: 1;

  .line-height {
    height: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
    background: linear-gradient(180deg, #ffffff 0%, #f7f8fa 100%);
    border-radius: 0.32rem 0.32rem 0 0; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
  }

  .update-time-all {
    font-size: 0.293rem; // 22rpx × 0.5 = 11px, 11px ÷ 37.5 = 0.293rem
    color: rgba(145, 148, 153, 1);
    text-align: center;
    padding-top: 0.53rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem

    .num {
      color: rgba(230, 0, 3, 1);
      margin: 0 0.067rem; // 5rpx × 0.5 = 2.5px, 2.5px ÷ 37.5 = 0.067rem
    }

    .time {
      margin-left: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
    }
  }

  .list-content {
    padding: 0.533rem 0.427rem; // 40rpx×0.5=20px→0.533rem, 32rpx×0.5=16px→0.427rem

    .empty-default {
      text-align: center;
      color: rgba(145, 148, 153, 1);
      font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
      padding: 2rem 0;
    }

    .loading-more {
      text-align: center;
      padding: 0.533rem 0; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem

      .loading-text {
        font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        color: rgba(145, 148, 153, 1);
      }
    }

    .no-more {
      text-align: center;
      padding: 0.533rem 0; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem

      .no-more-text {
        font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        color: rgba(145, 148, 153, 1);
      }
    }
  }
}

.content {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;

  h1 {
    color: #333;
    margin-bottom: 1rem;
    text-align: center;
  }

  .mock-data {
    background: #fff;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      color: #666;
      margin-bottom: 0.5rem;
      font-size: 1.2rem;
    }

    pre {
      background: #f5f5f5;
      padding: 1rem;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 0.9rem;
      line-height: 1.4;
      color: #333;
    }
  }
}

// 转换后的样式 (1rpx = 0.5px, 1rem = 37.5px)
.main-content {
  .header-top {
    padding: 0.43rem 0.43rem 0.53rem 0.43rem; // 26rpx×0.5=13px→0.347rem, 32rpx×0.5=16px→0.427rem, 40rpx×0.5=20px→0.533rem
    background: #fff;
    border-bottom: 0.213rem solid rgba(247, 248, 250, 1); // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem

    .title {
      font-size: 0.48rem; // 36rpx × 0.5 = 18px, 18px ÷ 37.5 = 0.48rem
      color: rgba(34, 36, 46, 1);
      line-height: 0.72rem; // 54rpx × 0.5 = 27px, 27px ÷ 37.5 = 0.72rem
      font-weight: bold;
    }

    .label {
      display: flex;
      align-items: center;
      margin-top: 0.333rem; // 25rpx × 0.5 = 12.5px, 12.5px ÷ 37.5 = 0.333rem

      .address {
        font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        color: rgba(145, 148, 153, 0.8);
      }

      .time {
        font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        color: rgba(145, 148, 153, 0.8);
        padding-left: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        position: relative;
        &.pl0 {
          padding-left: 0;
          &::after {
            display: none;
          }
        }

        &::after {
          position: absolute;
          display: block;
          content: " ";
          width: 0.013rem; // 1rpx × 0.5 = 0.5px, 0.5px ÷ 37.5 = 0.013rem
          height: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
          left: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
          top: 50%;
          transform: translateY(-50%);
          background: rgba(235, 236, 240, 1);
        }
      }
    }
  }

  .content-box-top {
    position: sticky;
    top: var(--header-height, 100px);
    display: flex;
    align-items: center;
    padding: 0 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
    justify-content: space-between;
    background: #fff;
    padding-bottom: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
    z-index: 22;
  }

  .tab-list {
    display: flex;
    align-items: center;

    .tab-list-item {
      font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
      color: rgba(102, 102, 102, 1);
      margin-right: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
      position: relative;
      padding-top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
      padding-bottom: 0.453rem; // 34rpx × 0.5 = 17px, 17px ÷ 37.5 = 0.453rem
      cursor: pointer;

      &:last-child {
        margin-right: 0;
      }

      &.active {
        color: rgba(34, 36, 46, 1);
        font-size: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        position: relative;
        font-weight: bold;

        &::after {
          position: absolute;
          display: block;
          content: " ";
          width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
          height: 0.08rem; // 6rpx × 0.5 = 3px, 3px ÷ 37.5 = 0.08rem
          background-color: rgba(230, 0, 3, 1);
          bottom: 0.187rem; // 14rpx × 0.5 = 7px, 7px ÷ 37.5 = 0.187rem
          left: 50%;
          transform: translateX(-50%);
          border-radius: 0.067rem; // 5rpx × 0.5 = 2.5px, 2.5px ÷ 37.5 = 0.067rem
        }
      }

      .dian {
        width: 0.16rem; // 12rpx × 0.5 = 6px, 6px ÷ 37.5 = 0.16rem
        height: 0.16rem;
        position: absolute;
        right: -0.16rem; // -12rpx × 0.5 = -6px, -6px ÷ 37.5 = -0.16rem
        top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
      }
    }
  }

  .search-box {
    display: flex;
    align-items: center;
    background: rgba(247, 248, 250, 1);
    width: 2.267rem; // 170rpx × 0.5 = 85px, 85px ÷ 37.5 = 2.267rem
    padding: 0 0.267rem; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
    box-sizing: border-box;
    border-radius: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
    height: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
    cursor: pointer;

    input {
      flex: 1;
      min-width: 0;
      padding-left: 0.107rem; // 8rpx × 0.5 = 4px, 4px ÷ 37.5 = 0.107rem
      font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
      border: none;
      background: transparent;
      outline: none;
    }

    img {
      width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
      height: 0.427rem;
    }
  }

  // 公告详情样式
  .content-box {
    background: #fff;
    // margin-bottom: 0.267rem; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem

    &.bor_bb {
      border-bottom: 0.267rem solid #f7f8fa; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
    }

    .content-center {
      padding: 0.533rem 0.427rem; // 40rpx×0.5=20px→0.533rem, 32rpx×0.5=16px→0.427rem
    }

    .notice-box {
      background: rgba(247, 248, 250, 1);
      padding: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
      border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
      .notice-box-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        border-bottom: 0.013rem solid rgba(235, 236, 240, 1); // 1rpx × 0.5 = 0.5px, 0.5px ÷ 37.5 = 0.013rem

        &.bt_0 {
          border-bottom: none;
          padding-bottom: 0;
        }

        .left {
          display: flex;
          align-items: center;
          .left-text {
            font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
            color: rgba(60, 61, 66, 1);
            margin-right: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem

            .num {
              font-weight: bold;
            }
          }
        }

        .status {
          font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
          color: rgba(19, 191, 128, 1);
        }
      }

      .time-line {
        margin-top: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem

        &-item {
          display: flex;
          align-items: center;
          position: relative;
          padding-left: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
          padding-bottom: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem

          &:last-child {
            padding-bottom: 0;

            &::after {
              display: none;
            }
          }

          &.active {
            .dian {
              width: 7px;
              height: 7px;
              background: #fff;
              border: 0.027rem solid rgba(230, 0, 3, 1); // 2rpx × 0.5 = 1px, 1px ÷ 37.5 = 0.027rem
              left: -2px;
              top: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem

              &::after {
                display: block;
                content: " ";
                position: absolute;
                width: 3px;
                height: 3px;
                background-color: rgba(230, 0, 3, 1);
                left: 50%;
                top: 50%;
                border-radius: 50%;
                transform: translate(-50%, -50%);
              }
            }

            .text-word {
              color: rgba(230, 0, 3, 1);
            }

            .time {
              color: rgba(230, 0, 3, 1);
            }
          }

          &::after {
            position: absolute;
            display: block;
            content: " ";
            width: 1px;
            height: 100%;
            border-left: 1px dashed rgba(194, 197, 204, 1);
            left: 2px;
            z-index: 1;
            top: 0.2rem; // 15rpx × 0.5 = 7.5px, 7.5px ÷ 37.5 = 0.2rem
            box-sizing: border-box;
          }

          .text-word {
            font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
            color: rgba(60, 61, 66, 1);
          }

          .time {
            font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
            color: rgba(145, 148, 153, 1);
            padding-left: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
            flex: 1;
            min-width: 0;
          }

          .dian {
            width: 5px;
            height: 5px;
            background-color: rgba(194, 197, 204, 1);
            border-radius: 50%;
            position: absolute;
            z-index: 2;
            left: 0;
            top: 0.2rem; // 15rpx × 0.5 = 7.5px, 7.5px ÷ 37.5 = 0.2rem
          }
        }
      }
    }

    .rich-text-box {
      &.pr-box {
        margin-top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
      }
      .rich-text-container {
        position: relative;
        transition: max-height 0.3s ease;

        &.collapsed {
          overflow: hidden;
        }

        &.expanded {
          max-height: none !important;
        }

        // 富文本内容样式
        #richTextContent {
          font-size: 0.373rem !important; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
          line-height: 2;
          color: #3c3d42;

          :deep(p) {
            font-size: 0.373rem !important;
            line-height: 2 !important;
          }

          :deep(img) {
            max-width: 100% !important;
            height: auto !important;
            display: block !important;
            margin: 0.533rem auto !important; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
          }

          :deep(table) {
            width: 100% !important;
            border-collapse: collapse !important;
            margin: 0.533rem 0 !important; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem

            td,
            th {
              border: 0.013rem solid #ddd !important; // 1rpx × 0.5 = 0.5px, 0.5px ÷ 37.5 = 0.013rem
              padding: 0.267rem !important; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
              font-size: 0.32rem !important; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
            }

            th {
              background: #f5f5f5 !important;
              font-weight: bold !important;
            }
          }
        }
      }

      .bottom-pos {
        position: relative;
        padding-bottom: 0.6rem;
        border-bottom: 0.01rem solid #ebecf0;
        .gradient-height {
          position: absolute;
          bottom: 1.6rem; // 100rpx × 0.5 = 50px, 50px ÷ 37.5 = 1.333rem
          left: 0;
          right: 0;
          height: 1.333rem; // 100rpx × 0.5 = 50px, 50px ÷ 37.5 = 1.333rem
          background: linear-gradient(transparent, #fff);
          pointer-events: none;
        }

        .see-all {
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba(60, 61, 66, 1);
          padding: 0.267rem 0; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
          font-size: 0.43rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
          cursor: pointer;

          img {
            width: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
            height: 0.32rem;
            margin-left: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
          }
        }
      }
    }

    .see-address {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0.533rem 0; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
      margin-top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
      background: #fff2f2;
      border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
      font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
      color: #e60003;
      cursor: pointer;

      img {
        width: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        height: 0.32rem;
        margin-left: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
      }
    }

    .attachment-box {
      background: #f7f8fa;
      background: rgba(247, 248, 250, 1);
      padding: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
      border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
      margin-top: 0.8rem; // 60rpx × 0.5 = 30px, 30px ÷ 37.5 = 0.8rem

      .title {
        font-size: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        color: rgba(34, 36, 46, 1);
        font-weight: bold;
      }

      .information-list {
        margin-top: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem

        &-item {
          display: flex;
          align-items: center;
          background: #fff;
          padding: 0.32rem 0.427rem; // 24rpx×0.5=12px→0.32rem, 32rpx×0.5=16px→0.427rem
          border-radius: 0.16rem; // 12rpx × 0.5 = 6px, 6px ÷ 37.5 = 0.16rem
          box-sizing: border-box;
          margin-bottom: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem

          &:last-child {
            margin-bottom: 0;
          }

          .type-img {
            width: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
            height: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
          }

          .text {
            padding: 0 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
            font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
            color: rgba(60, 61, 66, 1);
            flex: 1;
            line-height: 0.48rem; // 36rpx × 0.5 = 18px, 18px ÷ 37.5 = 0.48rem
            word-break: break-all;
          }

          .right-arrow {
            width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
            height: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
          }
        }

        .information-item {
          .fu-j {
            font-size: 0.347rem; // 26rpx × 0.5 = 13px, 13px ÷ 37.5 = 0.347rem
            color: rgba(60, 61, 66, 1);
          }

          .content {
            font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
            color: rgba(102, 102, 102, 1);
          }

          .copy-img {
            width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
            height: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
            transform: translateY(0.08rem); // 6rpx × 0.5 = 3px, 3px ÷ 37.5 = 0.08rem
            margin-right: 0.053rem; // 4rpx × 0.5 = 2px, 2px ÷ 37.5 = 0.053rem
            margin-left: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
          }

          .copy-c {
            font-size: 0.293rem; // 22rpx × 0.5 = 11px, 11px ÷ 37.5 = 0.293rem
            color: rgba(95, 126, 149, 1);
          }
        }
      }
    }

    .originally {
      margin-top: 0.43rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
      font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
      color: #999;
      .title {
        color: #333;
        margin: 0 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
      }

      .copy-img {
        width: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        height: 0.32rem;
        margin-right: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
        transform: translateY(0.08rem); // 6rpx × 0.5 = 3px, 3px ÷ 37.5 = 0.08rem
      }

      .copy-c {
        color: rgba(95, 126, 149, 1);
        cursor: pointer;
      }
    }
  }

  // 热门问答样式
  .popular-answer {
    padding: 0.64rem 0.427rem 0.533rem 0.427rem; // 48rpx×0.5=24px→0.64rem, 32rpx×0.5=16px→0.427rem, 40rpx×0.5=20px→0.533rem
    background: #fff;

    .item-title {
      font-size: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
      color: rgba(34, 36, 46, 1);
      font-weight: bold;
    }

    .answer-list {
      margin-top: 0.4rem; // 30rpx × 0.5 = 15px, 15px ÷ 37.5 = 0.4rem
    }

    .answer-list-item {
      margin-bottom: 0.4rem; // 30rpx × 0.5 = 15px, 15px ÷ 37.5 = 0.4rem

      &:last-child {
        margin-bottom: 0;
      }
    }

    .ask-box {
      display: flex;

      img {
        width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        height: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        margin-right: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
        margin-top: 0.2rem; // 15rpx × 0.5 = 7.5px, 7.5px ÷ 37.5 = 0.2rem
      }

      .text-t {
        font-size: 0.4rem; // 30rpx × 0.5 = 15px, 15px ÷ 37.5 = 0.4rem
        line-height: 0.8rem; // 60rpx × 0.5 = 30px, 30px ÷ 37.5 = 0.8rem
        color: rgba(60, 61, 66, 1);
        flex: 1;
      }
    }

    .answer-box {
      display: flex;
      margin-top: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem

      img {
        width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        height: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        margin-right: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
        margin-top: 0.08rem; // 6rpx × 0.5 = 3px, 3px ÷ 37.5 = 0.08rem
      }

      .text-t {
        font-size: 0.347rem; // 26rpx × 0.5 = 13px, 13px ÷ 37.5 = 0.347rem
        line-height: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
        color: rgba(102, 102, 102, 1);
        flex: 1;
      }
    }

    .more-button {
      font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
      color: rgba(60, 61, 66, 1);
      background: rgba(247, 248, 250, 1);
      border: 0.013rem solid rgba(194, 197, 204, 0.5); // 1rpx × 0.5 = 0.5px, 0.5px ÷ 37.5 = 0.013rem
      height: 1.067rem; // 80rpx × 0.5 = 40px, 40px ÷ 37.5 = 1.067rem
      border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
      display: flex;
      align-items: center;
      justify-content: center;
      width: 7.867rem; // 590rpx × 0.5 = 295px, 295px ÷ 37.5 = 7.867rem
      margin: 0 auto;
      margin-top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
      cursor: pointer;

      img {
        width: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        height: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
        margin-top: 0.053rem; // 4rpx × 0.5 = 2px, 2px ÷ 37.5 = 0.053rem
      }
    }
  }

  // 工具样式
  .tool-box {
    padding: 0.64rem 0.427rem; // 48rpx×0.5=24px→0.64rem, 32rpx×0.5=16px→0.427rem
    background: #fff;

    .item-title {
      font-size: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
      color: rgba(34, 36, 46, 1);
      font-weight: bold;
    }

    .tool-list {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem

      &-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25%;
        font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        color: rgba(60, 61, 66, 1);
        margin-bottom: 0.64rem; // 48rpx × 0.5 = 24px, 24px ÷ 37.5 = 0.64rem

        img {
          width: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
          height: 0.853rem; // 64rpx × 0.5 = 32px, 32px ÷ 37.5 = 0.853rem
          margin-bottom: 0.133rem; // 10rpx × 0.5 = 5px, 5px ÷ 37.5 = 0.133rem
        }
      }
    }

    .written-list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.32rem;
      margin-top: 0.427rem;
      .written-list-item {
        border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
        margin-bottom: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        height: 1.813rem; // 136rpx × 0.5 = 68px, 68px ÷ 37.5 = 1.813rem
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding-right: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        background: linear-gradient(270deg, rgba(247, 248, 250, 0.5) 0%, #f7f8fa 100%);

        .img {
          width: 1.067rem; // 80rpx × 0.5 = 40px, 40px ÷ 37.5 = 1.067rem
          height: 1.067rem; // 80rpx × 0.5 = 40px, 40px ÷ 37.5 = 1.067rem
        }

        .title {
          font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
          padding-left: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
          color: rgba(60, 61, 66, 1);
        }
      }
    }

    .bd-box {
      .bd-img {
        width: 100%;
        border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
        object-fit: cover;
        max-height: 2.933rem; // 220rpx × 0.5 = 110px, 110px ÷ 37.5 = 2.933rem
      }
    }
  }

  // 声明样式
  .statement-box {
    background: rgba(247, 248, 250, 1);
    padding: 0.533rem 0.427rem; // 40rpx×0.5=20px→0.533rem, 32rpx×0.5=16px→0.427rem
    font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
    color: #999;
    line-height: 1.5;
    text-align: justify;
  }

  // 通用边框样式
  .bor_bb {
    border-bottom: 0.267rem solid #f7f8fa; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
  }

  // 底部操作栏样式
  .action-bar-box {
    display: flex;
    height: 1.893rem; // 142rpx × 0.5 = 71px, 71px ÷ 37.5 = 1.893rem
    z-index: 998;
    position: relative;
    box-sizing: border-box;
  }

  .action-bar {
    z-index: 1;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1.893rem; // 142rpx × 0.5 = 71px, 71px ÷ 37.5 = 1.893rem
    background-color: #fff;
    box-sizing: border-box;
    border-top: 0.013rem solid #ebecf0; // 1rpx × 0.5 = 0.5px, 0.5px ÷ 37.5 = 0.013rem
    padding: 0.32rem 0.533rem 0.453rem 0.533rem; // 24rpx×0.5=12px→0.32rem, 40rpx×0.5=20px→0.533rem, 34rpx×0.5=17px→0.453rem
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .bottom-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
    position: relative;

    .new-box {
      position: absolute;
      font-size: 0.24rem; // 18rpx × 0.5 = 9px, 9px ÷ 37.5 = 0.24rem
      color: rgba(230, 0, 3, 1);
      background: rgba(230, 0, 3, 0.1);
      padding: 0.027rem 0.133rem; // 2rpx×0.5=1px→0.027rem, 10rpx×0.5=5px→0.133rem
      right: -0.187rem; // -14rpx × 0.5 = -7px, -7px ÷ 37.5 = -0.187rem
      border-radius: 0.267rem 0.267rem 0.267rem 0.053rem; // 20rpx×0.5=10px→0.267rem, 4rpx×0.5=2px→0.053rem
      top: -5px;
    }

    img {
      width: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
      height: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
      margin-bottom: 0.053rem; // 4rpx × 0.5 = 2px, 2px ÷ 37.5 = 0.053rem
    }

    font-size: 0.267rem; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
    color: rgba(145, 148, 153, 1);
  }

  .filter-button {
    background: rgba(236, 62, 51, 1);
    font-size: 0.373rem; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
    color: #fff;
    width: 5.333rem; // 400rpx × 0.5 = 200px, 200px ÷ 37.5 = 5.333rem
    height: 1.12rem; // 84rpx × 0.5 = 42px, 42px ÷ 37.5 = 1.12rem
    border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;

    &.wp100 {
      width: 100%;
    }
  }

  .flex-c {
    display: flex;
    align-items: center;
  }

  //   // 响应式设计
  //   @media (max-width: 768px) {
  //     .content {
  //       padding: 0.5rem;
  //     }

  //     .main-content {
  //       .header-top {
  //         padding: 0.267rem 0.32rem 0.427rem 0.32rem; // 减少移动端的内边距
  //       }

  //       .content-box-top {
  //         padding: 0 0.32rem;
  //         padding-bottom: 0.107rem;
  //       }

  //       .tab-list {
  //         .tab-list-item {
  //           margin-right: 0.533rem; // 减少移动端的间距
  //           font-size: 0.32rem; // 减小移动端字体

  //           &.active {
  //             font-size: 0.373rem;
  //           }
  //         }
  //       }

  //       .search-box {
  //         width: 2rem; // 减小移动端搜索框宽度
  //         height: 0.8rem;
  //         padding: 0 0.213rem;

  //         input {
  //           font-size: 0.293rem; // 减小移动端输入框字体
  //         }

  //         img {
  //           width: 0.373rem;
  //           height: 0.373rem;
  //         }
  //       }
  //     }

  //     .content-box {
  //       .content-center {
  //         padding: 0.427rem 0.32rem; // 减少移动端内边距
  //       }
  //     }

  //     .tool-box {
  //       .tool-list {
  //         padding: 0.427rem 0.32rem;

  //         .tool-list-item {
  //           width: 33.33%; // 移动端改为3列布局

  //           img {
  //             width: 0.933rem; // 减小移动端图标尺寸
  //             height: 0.933rem;
  //           }

  //           font-size: 0.293rem; // 减小移动端字体
  //         }
  //       }
  //     }
  //   }
}
</style>

<!-- 全局样式，确保富文本内容样式生效 -->
<style lang="scss">
// 富文本内容全局样式控制
#richTextContent {
  * {
    font-size: 0.373rem !important; // 28rpx × 0.5 = 14px, 14px ÷ 37.5 = 0.373rem
    line-height: 2 !important;
  }

  a {
    text-decoration: underline !important;
  }

  p {
    font-size: 0.373rem !important;
    line-height: 2 !important;
  }

  img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
    margin: 0.533rem auto !important; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem
  }

  table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin: 0.533rem 0 !important; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem

    td,
    th {
      border: 0.013rem solid #ddd !important; // 1rpx × 0.5 = 0.5px, 0.5px ÷ 37.5 = 0.013rem
      padding: 0.267rem !important; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
      font-size: 0.32rem !important; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
    }

    th {
      background: #f5f5f5 !important;
      font-weight: bold !important;
    }
  }
}
</style>
