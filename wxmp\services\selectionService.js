/**
 * 数据选择处理服务
 * 统一处理单选、多选、重置等选择逻辑
 * 用于薪资选择、考试类型选择、职位类型选择等场景
 */

/**
 * 单选方法 - 用于薪资选择场景
 * @param {Array} list - 当前数据列表
 * @param {Object} selectedItem - 被选中的项目
 * @returns {Array} 更新后的数据列表，确保只有一个项目被选中
 */
function handleSingleSelect(list, selectedItem) {
  console.log(selectedItem)
  return [selectedItem]
}

/**
 * 多选方法 - 用于考试类型、职位类型选择场景
 * @param {Array} list - 当前数据列表
 * @param {Object} selectedItem - 被选中/取消选中的项目
 * @returns {Array} 更新后的数据列表，支持多个项目同时选中
 */
function handleMultiSelect(arr, value) {
  const index = arr.indexOf(value)
  if (index > -1) {
    // 存在，移除
    return arr.filter((item) => item !== value)
  } else {
    // 不存在，添加
    return [...arr, value]
  }
}

/**
 * 重置方法 - 用于清空所有选择
 * @param {Array} list - 当前数据列表
 * @returns {Array} 所有项目都未选中状态的数据列表
 */
function handleReset(list) {
  if (!Array.isArray(list)) {
    console.warn("handleReset: 参数无效", { list })
    return []
  }

  // 创建新的数据列表副本，清除所有选中状态
  const resetList = list.map((item) => ({
    ...item,
    selected: false,
  }))

  return resetList
}

/**
 * 获取选中项数组 - 辅助方法
 * @param {Array} list - 数据列表
 * @returns {Array} 选中的项目数组
 */
function getSelectedItems(list) {
  if (!Array.isArray(list)) {
    console.warn("getSelectedItems: 参数无效", { list })
    return []
  }

  return list.filter((item) => item.selected === true)
}

/**
 * 设置选中项 - 辅助方法
 * @param {Array} list - 数据列表
 * @param {Array} selectedValues - 要选中的value数组
 * @returns {Array} 更新后的数据列表
 */
function setSelectedItems(list, selectedValues = []) {
  if (!Array.isArray(list)) {
    console.warn("setSelectedItems: 参数无效", { list, selectedValues })
    return []
  }

  if (!Array.isArray(selectedValues)) {
    console.warn("setSelectedItems: selectedValues必须是数组", {
      selectedValues,
    })
    return list
  }

  // 创建新的数据列表副本
  const updatedList = list.map((item) => ({
    ...item,
    selected: selectedValues.includes(item.value),
  }))

  return updatedList
}

// 导出所有方法
module.exports = {
  handleSingleSelect,
  handleMultiSelect,
  handleReset,
  getSelectedItems,
  setSelectedItems,
}
