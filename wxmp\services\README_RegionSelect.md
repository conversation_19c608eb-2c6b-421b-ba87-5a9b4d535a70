# 地区选择功能使用说明

## 概述

为了减少地区选择功能的重复代码，我们创建了两个工具：

1. **RegionSelectMixin** - 完整的地区选择 mixin，提供所有地区选择功能
2. **RegionSelectUtils** - 地区选择工具类，提供常用的工具方法

## 使用方式

### 方式一：使用 RegionSelectMixin（推荐新页面使用）

```javascript
// 1. 导入 mixin
const RegionSelectMixin = require("@/services/regionSelectMixin")

// 2. 混入到页面配置中
const pageConfig = Object.assign({}, RegionSelectMixin, {
  data: {
    // 页面数据
  },
  
  onLoad() {
    // 3. 初始化 mixin
    this.initRegionSelectMixin()
    
    // 4. 初始化地区数据
    this.initRegionData('announcement', 'regionPopupData')
    this.initRegionData('news', 'newsRegionPopupData')
  },
  
  // 5. 创建事件处理方法
  handleRegionProvinceClick(e) {
    this.handleProvinceClick(e, 'regionPopupData')
  },
  
  handleRegionConfirmSelection(e) {
    const callback = (selectedRegions) => {
      this.updateRegionSelectState(selectedRegions, 'noticeSelectForTemplate', 'apply_region')
      this.applyFilter()
    }
    this.handleConfirmSelection(e, 'regionPopupData', 'announcement', callback)
  },
})

Page(pageConfig)
```

### 方式二：使用 RegionSelectUtils（推荐现有页面使用）

```javascript
// 1. 导入工具类
const RegionSelectUtils = require("@/services/regionSelectUtils")

Page({
  onLoad() {
    // 2. 初始化地区数据
    RegionSelectUtils.initRegionData(this, 'announcement', 'regionPopupData')
    RegionSelectUtils.initRegionData(this, 'news', 'newsRegionPopupData')
  },
  
  // 3. 简化现有方法
  loadCityList(provinceId) {
    // 原来需要写很多代码，现在一行搞定
    RegionSelectUtils.loadCityList(this, provinceId, 'regionPopupData')
  },
  
  loadDistrictList(cityId) {
    RegionSelectUtils.loadDistrictList(this, cityId, 'regionPopupData')
  },
  
  handleRegionConfirm() {
    const { regionPopupData } = this.data
    const selectedRegions = regionPopupData.selectedRegions
    
    // 4. 更新选中状态
    RegionSelectUtils.updateRegionSelectState(
      this, 
      selectedRegions, 
      'noticeSelectForTemplate', 
      'apply_region'
    )
    
    // 5. 保存到缓存
    RegionSelectUtils.saveRegionToCache(selectedRegions, 'announcement')
    
    // 6. 应用筛选
    this.applyFilter()
  },
})
```

## 工具方法说明

### RegionSelectUtils 主要方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| `initRegionData` | 初始化地区数据 | `(context, cacheKey, dataKey)` |
| `loadProvinceList` | 加载省份列表 | `(context, dataKey)` |
| `loadCityList` | 加载城市列表 | `(context, provinceId, dataKey)` |
| `loadDistrictList` | 加载区县列表 | `(context, cityId, dataKey)` |
| `updateRegionSelectState` | 更新选中状态 | `(context, selectedRegions, stateKey, filterKey)` |
| `saveRegionToCache` | 保存到缓存 | `(selectedRegions, cacheKey)` |
| `loadRegionFromCache` | 从缓存加载 | `(cacheKey)` |
| `getRegionApiParams` | 获取API参数 | `(selectedRegions)` |
| `createRegionObject` | 创建地区对象 | `(province, city, district)` |
| `isRegionSelected` | 检查是否已选择 | `(selectedRegions, districtId)` |
| `addRegionToSelection` | 添加到选择列表 | `(selectedRegions, regionObject)` |
| `removeRegionFromSelection` | 从选择列表移除 | `(selectedRegions, districtId)` |

## 缓存键名约定

| 页面/功能 | 缓存键名 | 数据键名 |
|-----------|----------|----------|
| 公告筛选 | `'announcement'` | `'regionPopupData'` |
| 考试动态筛选 | `'news'` | `'newsRegionPopupData'` |
| 职位筛选 | `'job'` | `'jobRegionPopupData'` |
| 收藏筛选 | `'collection'` | `'collectionRegionPopupData'` |

## 现有代码改造示例

### 改造前（重复代码）

```javascript
loadCityList(provinceId) {
  this.setData({
    "regionPopupData.loading.city": true,
    "regionPopupData.cityList": [],
    "regionPopupData.districtList": [],
  })

  UTIL.request(API.getCityList, { province_id: provinceId })
    .then((res) => {
      if (res && res.error && res.error.code === 0 && res.data) {
        this.setData({
          "regionPopupData.cityList": res.data,
          "regionPopupData.loading.city": false,
        })
      } else {
        this.setData({
          "regionPopupData.loading.city": false,
        })
      }
    })
    .catch((error) => {
      this.setData({
        "regionPopupData.loading.city": false,
      })
    })
}
```

### 改造后（使用工具类）

```javascript
loadCityList(provinceId) {
  RegionSelectUtils.loadCityList(this, provinceId, 'regionPopupData')
}
```

## 优势

1. **代码复用**：减少重复代码，提高开发效率
2. **统一管理**：所有地区选择逻辑集中管理，便于维护
3. **缓存统一**：统一的缓存键名约定，避免冲突
4. **错误处理**：统一的错误处理逻辑
5. **类型安全**：统一的数据结构和参数格式
6. **易于扩展**：新增功能只需要在工具类中添加

## 注意事项

1. 确保 API 接口路径正确（`API.getProvinceList`, `API.getCityList`, `API.getDistrictList`）
2. 确保缓存工具正确导入（`regionCache.js`）
3. 数据键名要与页面 data 中的结构保持一致
4. 缓存键名要与其他页面区分，避免冲突

## 迁移建议

对于现有页面（如 home/index.js），建议：

1. 保留现有的事件处理方法结构
2. 使用 `RegionSelectUtils` 替换重复的加载方法
3. 使用工具方法简化数据处理逻辑
4. 逐步迁移，确保功能正常

对于新页面，建议：

1. 直接使用 `RegionSelectMixin`
2. 按照标准模式实现地区选择功能
3. 遵循缓存键名约定
