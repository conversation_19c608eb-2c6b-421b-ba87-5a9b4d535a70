<!-- 引入筛选相关的 WXS 函数 -->
<wxs module="filterUtils" src="/utils/wxs/filter.wxs"></wxs>

<view class="business-menu-container {{customClass}}">
  <!-- 根据menuList长度决定布局方式 -->
  <!-- 长度为2：分组布局（左右分组） -->
  <view wx:if="{{menuList.length === 2}}" class="menu-layout-fixed">
    <!-- 左侧滚动菜单列表 -->
    <scroll-view scroll-x class="menu-list left-menu" wx:if="{{menuList[0].length}}" show-scrollbar="{{false}}" enhanced>
      <!-- 完整版本 -->
      <view class="menu-list-item  {{item.type === 'check' ? 'no-arrow' : ''}}  {{item.type === 'check' ? (filterUtils.isMenuItemSelected(item.filter_key, selectData) ? 'selected' : '') : ((activeExpanded == item.key&& item.type!='sort_time')? 'selected' : '')}} {{filterUtils.isMenuItemSelected(item.filter_key, selectData) ? 'expanded' : ''}}" wx:for="{{menuList[0]}}" wx:key="key" bindtap="handleMenuClick" data-type="{{item.type}}" data-key="{{item.key}}">
        <view class="menu-text">
          {{item.title}}
          <block wx:if="{{item.type=='sort_time'}}">
            <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/{{selectData.apply_time[0] === 1?'arrow_top':selectData.apply_time[0] === 2?'arrow_bottom':'arrow_all'}}.png" mode="" />
          </block>
          <block wx:else>
            <!-- 箭头图标 - 支持 select、dialog_grid、dialog_list、region 类型 -->
            <image wx:if="{{item.type!= 'check'}}" class="arrow-icon {{filterUtils.isMenuItemSelected(item.filter_key, selectData) ? 'expanded' : ''}} {{(activeExpanded !== item.key && filterUtils.isMenuItemSelected(item.filter_key, selectData))?'zhuan':''}}" src="{{activeExpanded === item.key || filterUtils.isMenuItemSelected(item.filter_key, selectData) ? 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_up_red.png' : 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_down.png'}}">
            </image>
          </block>
        </view>
        <!-- 选中状态指示器 -->
        <view wx:if="{{filterUtils.isMenuItemSelected(item.filter_key, selectData)}}" class="selected-indicator"></view>
      </view>
    </scroll-view>

    <!-- 右侧固定菜单列表 -->
    <view class="menu-list right-menu" wx:if="{{menuList[1] && menuList[1].length}}">
      <block wx:for="{{menuList[1]}}" wx:key="key">
        <view wx:if="{{item.type!='page_icon'}}" class="menu-list-item {{item.type === 'check' ? 'no-arrow' : ''}} {{activeExpanded == item.key ? 'selected' : ''}} {{filterUtils.isMenuItemSelected(item.filter_key, selectData) ? 'expanded' : ''}}" bindtap="handleMenuClick" data-type="{{item.type}}" data-key="{{item.key}}">
          <view class="menu-text">
            {{item.title}}
            <!-- 箭头图标 - 支持 的 类型 -->
            <image wx:if="{{item.type!= 'check'}}" class="arrow-icon {{filterUtils.isMenuItemSelected(item.filter_key, selectData) ? 'expanded' : ''}} {{(activeExpanded !== item.key && filterUtils.isMenuItemSelected(item.filter_key, selectData))?'zhuan':''}}" src="{{activeExpanded === item.key || filterUtils.isMenuItemSelected(item.filter_key, selectData) ? 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_biao_u.png' : 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_biao_x.png'}}">
            </image>
          </view>
          <!-- 选中状态指示器 -->
          <!-- <view wx:if="{{filterUtils.isMenuItemSelected(item.filter_key, selectData)}}" class="selected-indicator"></view> -->
        </view>
      </block>
    </view>
  </view>

  <!-- 长度为1：单行滚动布局 -->
  <view wx:else>
    <scroll-view scroll-x class="menu-list" show-scrollbar="{{false}}" enhanced>
      <view class="menu-list-item {{item.type === 'check' ? 'no-arrow' : ''}} {{activeExpanded == item.key ? 'selected' : ''}} {{filterUtils.isMenuItemSelected(item.filter_key, selectData) ? 'expanded' : ''}}" wx:for="{{menuList[0]}}" wx:key="key" bindtap="handleMenuClick" data-type="{{item.type}}" data-key="{{item.key}}">
        <view class="menu-text">
          {{item.title}}
          <!-- 箭头图标 - 支持 的 类型 -->
          <image wx:if="{{item.type !='check'}}" class="arrow-icon {{filterUtils.isMenuItemSelected(item.filter_key, selectData) ? 'expanded' : ''}} {{(activeExpanded !== item.key && filterUtils.isMenuItemSelected(item.filter_key, selectData))?'zhuan':''}}" src="{{activeExpanded === item.key || filterUtils.isMenuItemSelected(item.filter_key, selectData) ? 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_up_red.png' : 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_arrow_down.png'}}">
          </image>
        </view>
      </view>
    </scroll-view>
  </view>
</view>