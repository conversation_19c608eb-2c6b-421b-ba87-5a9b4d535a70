.position-list-item {
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  position: relative;
  margin-bottom: 24rpx;
  &:last-child {
    margin-bottom: 0;
  }
  .label-img {
    width: 136rpx;
    height: 54rpx;
    position: absolute;
    right: 32rpx;
    top: 0;
  }
  .title-area {
    margin-bottom: 22rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 28rpx;
      color: rgba(34, 36, 46, 1);
      font-weight: bold;
    }
    .options-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .label-item {
    display: flex;
    font-size: 24rpx;
    color: rgba(145, 148, 153, 1);
    margin-bottom: 16rpx;
    .status {
      font-size: 24rpx;
      // font-weight: bold;
      color: rgba(19, 191, 128, 0.8);
      &.blue {
        color: rgba(68, 138, 255, 0.8);
      }
      &.over {
        color: rgba(255, 106, 77, 0.8);
      }
      &.end {
        color: rgba(145, 148, 153, 1);
      }
    }
    .num {
      font-size: 24rpx;
      color: rgba(60, 61, 66, 1);
      margin-left: 32rpx;
      flex: 1;
    }
  }
}
.r64 {
  right: 64rpx !important;
}
