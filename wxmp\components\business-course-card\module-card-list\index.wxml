<view class="module-list">
  <block wx:for="{{list}}" wx:key="index">
    <!-- 普通课程 -->
    <block wx:if="{{item.module_type === 'course'}}">
      <!-- 有图 -->
      <card-type-horizontal wx:if="{{item.mp_img &&item.show_cover === 1 }}" item="{{item}}" isInHome="{{isInHome}}" attachParams="{{attachParams}}"></card-type-horizontal>
      <!-- 无图 -->
      <card-type-deafult wx:else item="{{item}}" isInHome="{{isInHome}}" attachParams="{{attachParams}}"></card-type-deafult>
    </block>
    <!-- 拼团课程 -->
    <block wx:if="{{item.module_type === 'pintuan'}}">
      <card-type-groupen item="{{item}}" isInHome="{{isInHome}}" attachParams="{{attachParams}}" />
    </block>
    <!-- 面授课 -->
    <block wx:if="{{item.module_type === 'offline_classes'}}">
      <card-type-face item="{{item}}" isInHome="{{isInHome}}" attachParams="{{attachParams}}" />
    </block>
    <!-- 题集 -->
    <block wx:if="{{item.module_type === 'qs'}}">
      <card-type-questionset item="{{item}}" isInHome="{{isInHome}}" attachParams="{{attachParams}}" />
    </block>

    <!-- 批改班 -->
    <block wx:if="{{item.module_type === 'correction'}}">
      <card-type-correct item="{{item}}" isInHome="{{isInHome}}" attachParams="{{attachParams}}" />
    </block>


    <!-- 高端版课程 -->
    <block wx:elif="{{item.module_type === 'advclass'}}">
      <!-- 高端班其他卡片  -->
      <!-- <card-type-advclass-other wx:if="{{item.card_mode ==='advclass'}}" item="{{item}}" isInHome="{{isInHome}}" attachParams="{{attachParams}}"></card-type-advclass-other> -->
      <!-- 高端班卡片  -->
      <card-type-advclass item="{{item}}" isInHome="{{isInHome}}" attachParams="{{attachParams}}"> </card-type-advclass>
    </block>
  </block>
</view>