const majorData = require("./majorData.js")

Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    defaultValue: {
      type: String,
      value: "",
    },
  },

  data: {
    // Tab
    activeTab: 1,
    tabs: [
      { id: 0, name: "专科" },
      { id: 1, name: "普通本科" },
      { id: 2, name: "硕士研究生" },
      { id: 3, name: "博士研究生" },
    ],

    // 专业数据
    majorCategories: [], // 左侧分类
    majorSecondLevel: {}, // 第二级专业数据
    activeSecondLevelId: null, // 当前选中的二级分类ID
    majorThirdLevel: {}, // 第三级专业数据
    selectedMajor: null, // 已选中的专业 {id, name, fullName}
    syncToResume: true, // 默认同步至我的简历
    isSearching: false,
    searchResults: [],
    selectedMajorNames: "", // 用于显示已选专业名称

    // 搜索
    searchKeyword: "",
  },

  observers: {
    selectedMajor: function (selectedMajor) {
      this.setData({
        selectedMajorNames: selectedMajor ? selectedMajor.fullName : "",
      })
    },
  },

  lifetimes: {
    attached() {
      this.initData()
    },
  },

  methods: {
    // 初始化数据
    initData() {
      this.setData({
        majorCategories: majorData.benke.categories,
        majorSecondLevel: majorData.benke.secondLevel,
        majorThirdLevel: majorData.benke.thirdLevel,
        activeCategoryId: majorData.benke.categories[0].id,
      })
    },

    // 切换学历Tab
    onTabChange(e) {
      const tabId = e.currentTarget.dataset.id
      let data
      if (tabId === 0) {
        data = majorData.zhuanke
      } else if (tabId === 1) {
        data = majorData.benke
      } else if (tabId === 2) {
        data = majorData.shuoshi
      } else {
        data = majorData.boshi
      }

      this.setData({
        activeTab: tabId,
        majorCategories: data.categories,
        majorSecondLevel: data.secondLevel,
        majorThirdLevel: data.thirdLevel,
        activeCategoryId: data.categories[0].id,
        activeSecondLevelId: null,
        selectedMajor: null,
      })
    },

    // 切换一级分类
    onCategoryClick(e) {
      this.setData({
        activeCategoryId: e.currentTarget.dataset.id,
        activeSecondLevelId: null,
      })
    },

    // 切换二级分类
    onSecondLevelClick(e) {
      this.setData({
        activeSecondLevelId: e.currentTarget.dataset.id,
      })
    },

    // 选择专业
    onMajorClick(e) {
      const { id, name } = e.currentTarget.dataset
      const category = this.data.majorCategories.find(
        (c) => c.id === this.data.activeCategoryId
      )
      const secondLevel = this.data.majorSecondLevel[
        this.data.activeCategoryId
      ].find((c) => c.id === this.data.activeSecondLevelId)

      this.setData({
        selectedMajor: {
          id,
          name,
          fullName: `${category.name}-${secondLevel.name}-${name}`,
        },
      })
    },

    // 重置
    onReset() {
      this.setData({
        activeTab: "undergraduate",
        searchKeyword: "",
        isSearching: false,
        selectedMajor: null,
        activeCategoryId: null,
        activeSecondLevelId: null,
      })
      this.loadMajorData()
    },

    clearSelection() {
      this.setData({
        selectedMajor: null,
      })
    },

    toggleSyncToResume() {
      this.setData({
        syncToResume: !this.data.syncToResume,
      })
    },

    onSave() {
      // 未选择专业时，点击无效
      if (!this.data.selectedMajor) {
        return
      }
      const { selectedMajor, syncToResume } = this.data
      this.triggerEvent("confirm", {
        value: selectedMajor,
        sync: syncToResume,
      })
      this.onClose()
    },

    // 关闭
    onClose() {
      this.triggerEvent("close")
    },

    // 搜索相关方法
    onSearchInput(e) {
      const keyword = e.detail.value
      this.setData({
        searchKeyword: keyword,
        isSearching: keyword.length > 0,
      })
      this.searchMajors(keyword)
    },

    searchMajors(keyword) {
      if (!keyword) {
        this.setData({ searchResults: [] })
        return
      }
      const results = []
      const allMajors = [
        ...Object.values(majorData.benke.thirdLevel).flat(),
        ...Object.values(majorData.zhuanke.thirdLevel).flat(),
        ...Object.values(majorData.shuoshi.thirdLevel).flat(),
        ...Object.values(majorData.boshi.thirdLevel).flat(),
      ]

      allMajors.forEach((major) => {
        if (major.name.includes(keyword)) {
          results.push(major)
        }
      })

      this.setData({ searchResults: results })
    },
  },
})
