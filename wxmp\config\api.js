const API_BASE_URL = "https://mp.test.tikutech.com/position-search-mp"
// const API_BASE_URL = "https://mp.test.jbcgk.com/trainingmp/"

module.exports = {
  API_BASE_URL,
  getCommonConfiguration: API_BASE_URL + "/api/mp/v1/common/get_config", //获取配置信息
  getRegionChildList: API_BASE_URL + "/api/mp/v1/common/get_region_child_list", //获取省市区三级联动数据
  getRegionTree: API_BASE_URL + "/api/mp/v1/common/get_region_tree", //获取省市区三级数据
  saveEnterExamRecord: API_BASE_URL + "/api/mp/v1/user/save_enter_exam_record", //保存报考地区

  getHome: API_BASE_URL + "/api/mp/v1/home/<USER>", //获取首页
  getArticleList: API_BASE_URL + "/api/mp/v1/article/list", //获取公告列表
  getArticleDetail: API_BASE_URL + "/api/mp/v1/article/detail", //获取公告详情
  getNoticeFilterMenu:
    API_BASE_URL + "/api/mp/v1/article/get-notice-list-filter-menu", //获取考试动态顶部筛选列表
  getNoticeList: API_BASE_URL + "/api/mp/v1/article/notice-list", //获取考试动态列表

  getArticleChildList: API_BASE_URL + "/api/mp/v1/article/child-list", //获取合辑列表

  getJobFilterMenu: API_BASE_URL + "/api/mp/v1/job/get-list-filter-menu", //获取职位筛选顶部列表
  getJobList: API_BASE_URL + "/api/mp/v1/job/list", //获取职位列表

  getUserInfo: API_BASE_URL + "/api/mp/v1/home/<USER>", //获取用户信息
  userLogin: API_BASE_URL + "/api/mp/v1/home/<USER>", // 用户登录
  getCosTempKeys: API_BASE_URL + "api/mp/v1/common/get-cos-temp-keys", // 获取上传文件临时key
  editUserInfo: API_BASE_URL + "api/mp/v1/user/edit_user_info", // 修改用户个人信息
  getIndexCodePath: API_BASE_URL + "api/mp/v1/qrcode/get-path-by-code", // 通过code获取路径
  getGuideInfo: API_BASE_URL + "api/mp/v1/common/get_guide_info ", // 获取引导页信息

  getJobDetail: API_BASE_URL + "/api/mp/v1/job/detail", // 获取职位详情数据
  getJobFilteList: API_BASE_URL + "/api/mp/v1/job/get-job-filter-list", // 获取职位筛选列表

  getChoiceList: API_BASE_URL + "/api/mp/v1/home/<USER>", // 获取精选列表

  getArticleNoticeList: API_BASE_URL + "/api/mp/v1/article/article-notice-list", // 获取详情下官方动态
  getChildListForDetail:
    API_BASE_URL + "/api/mp/v1/article/child-list-for-detail", // 合集子公告列表-详情内弹窗形式

  setFollows: API_BASE_URL + "/api/mp/v1/user/follows", // 关注/取消关注

  getMajorList: API_BASE_URL + "/api/mp/v1/major/get_child_list", // 获取专业库三级联动
  getMajorByKeyWords: API_BASE_URL + "/api/mp/v1/major/get_list_by_keywords", // 获取专业库三级联动

  getCompareList: API_BASE_URL + "/api/mp/v1/job/get_list_by_ids", // 职位对比列表
  getCompareDetail: API_BASE_URL + "/api/mp/v1/job/compare", // 职位对比详情

  // 报考大数据/进面分数线
  getApplyDetail: API_BASE_URL + "/api/mp/v1/apply/detail", // 获取报名大数据某公告详情
  getProjectList: API_BASE_URL + "/api/mp/v1/common/get_project_list", // 获取考试项目列表
  getArticleList: API_BASE_URL + "/api/mp/v1/common/get_article_list", // 获取某考试项目下公告列表

  // 个人中心
  getUserIndex: API_BASE_URL + "/api/mp/v1/user/index", // 个人中心首页
  getFollowsList: API_BASE_URL + "/api/mp/v1/user/get_follows_list", // 我的关注
  getFootPrintList: API_BASE_URL + "/api/mp/v1/user/get_footprint_list", // 我的足迹
  setFollowTag: API_BASE_URL + "/api/mp/v1/user/follows_tag", // 添加职位标签

  getResumeInfo: API_BASE_URL + "/api/mp/v1/user/get_resume_info", // 获取简历详情
  saveResume: API_BASE_URL + "/api/mp/v1/user/save_resume", // 保存简历
  saveEducation: API_BASE_URL + "/api/mp/v1/user/save_education_record", // 保存教育经历
  deleteEducation: API_BASE_URL + "/api/mp/v1/user/delete_education_record", // 删除教育经历
  getEducationInfo: API_BASE_URL + "/api/mp/v1/user/get_education_record_info", // 获取教育经历详情
}
