// ===== 移动端适配变量 =====
// 设计稿宽度，通常为375px
$design-width: 375px;
// 根字体大小，1rem = 37.5px
$root-font-size: 37.5px;

// ===== 颜色变量 =====
// 主色调
$primary-color: #1989fa;
$primary-light: #66b1ff;
$primary-dark: #0d7377;

// 辅助色
$success-color: #07c160;
$warning-color: #ff976a;
$danger-color: #ee0a24;
$info-color: #909399;

// 中性色
$text-primary: #323233;
$text-regular: #646566;
$text-secondary: #969799;
$text-placeholder: #c8c9cc;

// 背景色
$bg-primary: #ffffff;
$bg-secondary: #f7f8fa;
$bg-tertiary: #ebedf0;

// 边框色
$border-light: #ebedf0;
$border-base: #dcdee0;
$border-dark: #c8c9cc;

// ===== 尺寸变量 =====
// 间距
$spacing-xs: 8px;
$spacing-sm: 12px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 圆角
$border-radius-sm: 2px;
$border-radius-md: 4px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// 字体大小（基于37.5px基准）
$font-size-xs: 20px;   // 0.533rem
$font-size-sm: 24px;   // 0.64rem
$font-size-md: 28px;   // 0.747rem
$font-size-lg: 32px;   // 0.853rem
$font-size-xl: 36px;   // 0.96rem

// 行高
$line-height-xs: 1.2;
$line-height-sm: 1.4;
$line-height-md: 1.5;
$line-height-lg: 1.6;

// ===== Z-index层级 =====
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// ===== 动画变量 =====
$transition-base: all 0.3s ease;
$transition-fast: all 0.15s ease;
$transition-slow: all 0.5s ease;

// ===== 阴影变量 =====
$box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
$box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
$box-shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.2);
