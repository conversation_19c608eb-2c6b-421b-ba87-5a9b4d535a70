apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${CI_PROJECT_NAME}
  namespace: ${NAMESPACE}
  labels:
    app: ${CI_PROJECT_NAME}
    serviceType: ${SERVICE_TYPE}
    version: stable
spec:
  replicas: ${REPLICAS}
  selector:
    matchLabels:
      app: ${CI_PROJECT_NAME}
      serviceType: ${SERVICE_TYPE}
      version: stable
  template:
    metadata:
      labels:
        app: ${CI_PROJECT_NAME}
        serviceType: ${SERVICE_TYPE}
        version: stable
      annotations:
        co.elastic.logs/enabled: "true"
    spec:
      containers:
      - name: ${CI_PROJECT_NAME}
        image: ccr.ccs.tencentyun.com/tiku/${CI_PROJECT_NAME}:${CI_COMMIT_SHORT_SHA}
        imagePullPolicy: IfNotPresent
        env:
          - name: APP_ENV
            value: ${APP_ENV}
        ports:
        - containerPort: ${SERVICE_PORT}
        resources:
          requests:
            cpu: ${REQUEST_CPU}
            memory: ${REQUEST_MEM}
          limits:
            cpu: ${LIMIT_CPU}
            memory: ${LIMIT_MEM}
        livenessProbe:
          httpGet:
            scheme: HTTP
            port: ${SERVICE_PORT}
            path: ${URI_PREFIX}/_dialtest
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 20
        readinessProbe:
          httpGet:
            scheme: HTTP
            port: ${SERVICE_PORT}
            path: ${URI_PREFIX}/_dialtest
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 10