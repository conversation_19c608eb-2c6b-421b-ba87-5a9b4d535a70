<view class="education-add">
  <view class="bg-area"></view>
  <view class="form-area">
    <view class="form-item" bindtap="onFormItemTap" data-field="degree">
      <view class="left">学历
        <text class="cred">*</text>
      </view>
      <view class="right">
        <view class="text {{formData.degree ? 'has-value' : ''}}">{{formData.degree || '待完善'}}</view>
        <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
      </view>
    </view>
    <view class="form-item" bindtap="onFormItemTap" data-field="major" wx:if="{{ showMajorField }}">
      <view class="left">专业
        <text class="cred">*</text>
      </view>
      <view class="right">
        <view class="text text-ellipsis-1 {{formData.major ? 'has-value' : ''}}">{{formData.major || '待完善'}}</view>
        <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
      </view>
    </view>
    <view class="form-item" bindtap="onFormItemTap" data-field="diploma">
      <view class="left">学位</view>
      <view class="right">
        <view class="text  {{educationId?'has-value':formData.diploma?'has-value':''}}">{{formData.diploma?formData.diploma:educationId ? "无学位" : "待完善"}}
        </view>
        <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_more.png" mode="" />
      </view>
    </view>
  </view>

  <!-- 学历选择器 -->
  <van-popup show="{{ degreeShow }}" round position="bottom" z-index="9999" bind:close="onDegreeClose">
    <van-picker id="degreePicker" show-toolbar title="请选择学历" columns="{{ degreeColumns }}" toolbar-class="top-area" column-class="column-area" active-class="active-item" bind:cancel="onDegreeClose" bind:confirm="onDegreeConfirm" />
  </van-popup>

  <!-- 专业选择器组件 -->
  <major-selector show="{{ majorShow }}" default-value="{{ formData.major }}" educationId="{{ educationIdForSelector }}" degreeName="{{ formData.degree }}" majorList="{{ majorList }}" selectedMajorIds="{{ selectedMajorIds }}" bind:close="onMajorClose" bind:confirm="onMajorConfirm" />
  <!-- <major-selector-pro show="{{ majorShow }}" default-value="{{ formData.major }}" bind:close="onMajorClose" bind:confirm="onMajorConfirm" /> -->

  <!-- 学位选择器 -->
  <van-popup show="{{ diplomaShow }}" round position="bottom" z-index="9999" bind:close="onDiplomaClose">
    <van-picker id="diplomaPicker" show-toolbar title="请选择学位" columns="{{ diplomaColumns }}" toolbar-class="top-area" column-class="column-area" active-class="active-item" bind:cancel="onDiplomaClose" bind:confirm="onDiplomaConfirm" />
  </van-popup>

  <tabbar-box>
    <view class="bottom-btn">
      <view class="btn {{ isFormComplete ? 'active' : '' }}" bindtap="onAddJobs">保存</view>
    </view>
  </tabbar-box>
</view>