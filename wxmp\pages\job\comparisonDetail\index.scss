/* 竖向滚动容器 */
.vertical-scroll-container {
  width: 100vw;
  height: 100vh;
  background: #ffffff;
}

/* 固定顶部头部样式 */
.fixed-top-header {
  .horizontal-scroll-area {
    overflow-x: auto;
    overflow-y: hidden;
    &::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }
  }
}

/* 顶部占位区域 */
.top-header-placeholder {
  background: transparent;
  flex-shrink: 0;
}

/* 左侧固定列区域 */
.fixed-columns-area {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 100;
  /* 单个表格的固定列部分 */
  .table-fixed-section {
    width: 100%;
  }
  /* 首列单元格 */
  .first-column-cell {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 24rpx;
    color: #666666;
    font-size: 24rpx;
    border-bottom: 1rpx solid #ebecf0;
    border-right: 1rpx solid #ebecf0;
    box-sizing: border-box;
    background: #fff;
  }
}

/* 右侧横向滚动区域 */
.horizontal-scroll-area {
  position: relative;
  background: #ffffff;
  flex: 1;
  height: 100%;
  z-index: 90;
  .horizontal-scroll-content {
    display: flex;
    flex-direction: column;
    min-height: 100%;
  }
  .table-scroll-section {
    width: 100%;
  }
  /* 数据行 */
  .data-row {
    display: flex;
    width: 100%;
    align-items: stretch;
    box-sizing: border-box;
  }

  /* 数据单元格 */
  .data-cell {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 32rpx 24rpx;
    color: #3c3d42;
    font-size: 24rpx;
    border-right: 1rpx solid #ebecf0;
    border-bottom: 1rpx solid #ebecf0;

    box-sizing: border-box;
    flex-shrink: 0;
    background: #ffffff;

    &:last-child {
      border-right: none;
    }
  }
  .header-placeholder {
    // position: sticky;
    // top: 264rpx;
    // background: #fff;
  }
}

/* ========================================
   顶部职位头部样式区域
   ======================================== */

.first-job-header-top {
  padding: 8rpx 5rpx 8rpx 10rpx !important;
  border-right: 1rpx solid transparent !important;
  background: #f7f8fa !important;
  .job-header-cell {
    padding: 68rpx 20rpx 20rpx 20rpx !important;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    background: #ffffff;
    box-shadow: 0rpx 0rpx 4rpx 2rpx rgba(0, 0, 0, 0.1);
    border-radius: 4rpx;

    .close-img {
      position: absolute;
      top: 10rpx;
      right: 8rpx;
      width: 24rpx;
      height: 24rpx;
    }

    .tips-box {
      position: absolute;
      left: 8rpx;
      top: 8rpx;
      display: flex;
      align-items: center;
      padding: 6rpx 16rpx 6rpx 8rpx;
      box-sizing: border-box;
      background: #f7f8fa;
      border-radius: 0 0 16rpx 0;
      &.active {
        background: rgba(230, 0, 3, 0.05);
        .tips-text {
          color: #e60003;
        }
      }
      .tips-icon {
        width: 24rpx;
        height: 24rpx;
        margin-right: 4rpx;
      }
      .tips-text {
        font-size: 20rpx;
        color: #8f97a8;
      }
    }

    .job-name {
      font-size: 28rpx;
      font-weight: bold;
      color: #3c3d42;
      margin-bottom: 8rpx;
      line-height: 1.3;
      vertical-align: middle;
      .job-arrow {
        display: inline-block;
        width: 24rpx;
        height: 24rpx;
        vertical-align: middle;
      }
    }
    .focus-btn {
      padding: 10rpx 24rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      border: 2rpx solid rgba(230, 0, 3, 0.5);
      border-radius: 8rpx;
      background: #ffffff;
      &.active {
        border: 1rpx solid #ebecf0;
        .focus-text {
          color: #919499;
        }
      }
      .focus-img {
        width: 24rpx;
        height: 24rpx;
        margin-right: 8rpx;
      }
      .focus-text {
        color: #e60003;
        font-size: 24rpx;
      }
    }

    .job-subtitle {
      font-size: 22rpx;
      color: #919499;
      text-align: center;
      line-height: 1.2;
    }

    /* 在这里可以添加其他顶部专用样式 */
  }
}

/* 顶部职位区域的筛选样式 */
.filter-scroll-section {
  .data-row {
    .data-cell {
      height: 264rpx;
    }
  }
}

.box {
  display: flex;
  &:first-of-type {
    border-top: 2rpx solid #ebecf0;
  }

  /* 固定定位的头部特殊样式 */
  &.fixed-top-header {
    border-top: 2rpx solid #ebecf0;

    .horizontal-scroll-area {
      flex: 1;
      width: calc(100vw - var(--first-column-width, 160rpx));
      max-width: calc(100vw - var(--first-column-width, 160rpx));
      margin-left: var(
        --first-column-width,
        160rpx
      ); /* 为左侧固定区域留出空间 */
    }
  }
}

.header-placeholder {
  height: 80rpx;
  .sticky-content {
    z-index: -1;
    position: absolute;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100%;
    background-color: #fff;
    box-sizing: border-box;
    border-bottom: 1rpx solid #e8eaed;
    display: flex;
    align-items: center;
    padding-left: 24rpx;
    .line {
      height: 24rpx;
      width: 6rpx;
      background: #e60003;
      border-radius: 4rpx;
      margin-right: 16rpx;
    }
    .text {
      color: #22242e;
      font-size: 28rpx;
      font-weight: bold;
    }
  }
}
/* 吸顶区域 */
.sticky-header {
  position: sticky;
  top: 264rpx;
  left: 0;
  z-index: 50;
}

.filter-fixed-section {
  position: fixed; /* 独立固定定位，不跟随页面滚动 */
  top: 0;
  left: 0;
  z-index: 1100;
  height: 264rpx;
  padding-left: 24rpx;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  border-right: 1rpx solid #ebecf0;
  border-bottom: 1rpx solid #ebecf0;
  box-sizing: border-box;
  .first-text {
    margin-top: 8rpx;
    font-size: 22rpx;
    color: #3c3d42;
  }
}

.bottom-area {
  padding: 40rpx 32rpx 32rpx 8rpx;
  .title {
    font-size: 24rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }
  .list-item-bottom {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16rpx;
    .num {
      width: 24rpx;
      height: 24rpx;
      background: #c2c5cc;
      border-radius: 12rpx;
      opacity: 0.6;
      font-size: 16rpx;
      font-weight: bold;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;
    }
    .text-desc {
      flex: 1;
      color: #919499;
      font-size: 24rpx;
    }
  }
}

/* 相同项和不同项的背景样式 */
.same-row-bg {
  .data-cell {
    background: #ffffff !important;
  }
}

.different-row-bg {
  .data-cell {
    background: #f7f8fa !important;
  }
}

/* 添加职位列样式 */
.add-job-cell {
  width: 100%;
  height: 100%;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 4rpx 2rpx rgba(0, 0, 0, 0.1);
  border-radius: 4rpx;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  .add-job-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .add-icon {
      width: 64rpx;
      height: 64rpx;
      margin-bottom: 16rpx;
    }

    .add-text {
      font-size: 24rpx;
      color: #e60003;
    }
  }
}

.add-job-data-cell {
  background: #f7f8fa !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #c2c5cc !important;
}
.cred {
  color: #e60003 !important;
}
