<view class="job-card">
  <view class="top">
    <view class="name">{{jobData.title}}</view>
    <view class="right-btn {{ jobData.isFocus?'':'red'}}" bindtap="onFocusClick">
      <image wx:if="{{jobData.isFocus}}" class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/focused.png" mode="" />
      <image wx:else class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_add_focus_red.png" mode="" />
      <view class="text">{{ jobData.isFocus?'已关注':'关注'}}</view>
    </view>
  </view>
  <view class="data-area">
    <view class="list-item">
      <view class="title">招录人数：</view>
      <view class="content"><text class="bold">1</text>人</view>
    </view>
    <view class="list-item">
      <view class="title">报考人数：</view>
      <view class="content"><text class="bold">402</text>人</view>
    </view>
    <view class="list-item">
      <view class="title">缴费人数：</view>
      <view class="content"><text class="bold">300</text>人</view>
    </view>
    <view class="list-item">
      <view class="title">竞争比：</view>
      <view class="red"><text class="bold">402:1</text></view>
    </view>
    <view class="list-item mb0">
      <view class="title">招录地区：</view>
      <view class="content">{{jobData.diqu}}</view>
    </view>
  </view>
</view>