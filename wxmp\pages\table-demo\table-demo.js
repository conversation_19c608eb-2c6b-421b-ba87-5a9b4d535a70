/**
 * 多表格双向滚动页面
 * 功能：支持横向和竖向双向滚动，固定表头和首列，支持吸顶
 * 滚动同步：使用view组件配合触摸事件（bindtouchstart、bindtouchmove、bindtouchend）实现实时横向滚动同步
 * 计算方式：采用增量计算方式，每次触摸移动时计算与上一次位置的差值，避免累积误差
 * 优势：相比scroll-view组件，触摸事件响应更快，无延迟，滚动更流畅，更精确
 * 作者：WeChat Mini Program Developer
 * 创建时间：2025-06-17
 * 更新时间：2025-06-18
 */

Page({
  /**
   * 触摸位置信息
   */
  lastTouchX: 0, // 上一次触摸位置，用于增量计算
  maxScrollLeft: 0,

  /**
   * 页面的初始数据
   */
  data: {
    // 表格数据列表
    tableList: [],
    // 首列宽度（rpx）
    firstColumnWidth: 160,
    // 其他列宽度（rpx）
    columnWidth: 240,
    // 行高（rpx）
    rowHeight: 80,
    // 可滚动区域宽度
    scrollableWidth: 0,
    // 首列高度二维数组（用于动态设置高度）[tableIndex][rowIndex]
    firstColumnHeights: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 初始化表格数据
    this.initTableData()
  },

  /**
   * 初始化表格数据
   */
  initTableData() {
    // 模拟数据
    const mockData = [
      {
        id: 1,
        title: "表格1",
        rows: [
          [
            "张三",
            "28",
            "男",
            "工程师工程师工程师工程师工程师工程师工程师工程师工程师工程师",
            "技术部",
            "2020-01-01",
          ],
          ["李四", "32", "男", "产品经理", "产品部", "2019-06-15"],
          ["王五", "25", "女", "设计师", "设计部", "2021-03-20"],
        ],
      },
      {
        id: 2,
        title: "表格2",
        rows: [
          ["赵六", "30", "男", "工程师", "技术部", "2018-12-01"],
          [
            "钱七",
            "27",
            "女",
            "产品经理产品经理产品经理产品经理产品经理产品经理产品经理",
            "产品部",
            "2020-08-15",
          ],
          ["孙八", "35", "男", "设计师", "设计部", "2019-09-20"],
        ],
      },
      {
        id: 1,
        title: "表格1",
        rows: [
          [
            "张三",
            "28",
            "男",
            "工程师工程师工程师工程师工程师工程师工程师工程师工程师工程师",
            "技术部",
            "2020-01-01",
          ],
          ["李四", "32", "男", "产品经理", "产品部", "2019-06-15"],
          ["王五", "25", "女", "设计师", "设计部", "2021-03-20"],
        ],
      },
      {
        id: 2,
        title: "表格2",
        rows: [
          ["赵六", "30", "男", "工程师", "技术部", "2018-12-01"],
          [
            "钱七",
            "27",
            "女",
            "产品经理产品经理产品经理产品经理产品经理产品经理产品经理",
            "产品部",
            "2020-08-15",
          ],
          ["孙八", "35", "男", "设计师", "设计部", "2019-09-20"],
        ],
      },
    ]

    this.setData({
      tableList: mockData,
    })

    // 计算可滚动区域宽度
    this.calculateScrollableWidth()
  },

  /**
   * 计算可滚动区域宽度和最大滚动距离
   */
  calculateScrollableWidth() {
    const { tableList, columnWidth, firstColumnWidth } = this.data

    if (!tableList || !Array.isArray(tableList) || tableList.length === 0) {
      return
    }

    // 找到列数最多的表格
    let maxColumns = 0
    tableList.forEach((table) => {
      if (table.headers && Array.isArray(table.headers)) {
        maxColumns = Math.max(maxColumns, table.headers.length)
      }
    })

    // 计算可滚动区域宽度（除首列外的所有列，rpx）
    const scrollableWidthRpx = Math.max(0, maxColumns - 1) * columnWidth

    // 获取屏幕信息
    const systemInfo = wx.getSystemInfoSync()
    const screenWidthPx = systemInfo.screenWidth

    // 将rpx转换为px进行计算
    const scrollableWidthPx = (scrollableWidthRpx * screenWidthPx) / 750
    const firstColumnWidthPx = (firstColumnWidth * screenWidthPx) / 750
    const paddingPx = (40 * screenWidthPx) / 750 // 40rpx边距转px

    // 计算可视区域宽度（像素）
    const visibleWidthPx = screenWidthPx - firstColumnWidthPx - paddingPx

    // 计算最大滚动距离（像素）
    this.maxScrollLeft = Math.max(0, scrollableWidthPx - visibleWidthPx)

    this.setData({
      scrollableWidth: scrollableWidthRpx, // 保持rpx用于样式
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 页面渲染完成后，同步第一列和第二列的高度
    this.syncCellHeights()
  },

  /**
   * 同步第一列和第二列单元格高度
   * 确保同行的第一个和第二个单元格高度保持一致
   */
  syncCellHeights() {
    // 使用 setTimeout 确保 DOM 完全渲染完成
    setTimeout(() => {
      this.performHeightSync()
    }, 100)
  },

  /**
   * 执行高度同步逻辑
   */
  performHeightSync() {
    const query = wx.createSelectorQuery().in(this)
    const { tableList } = this.data

    if (!tableList || tableList.length === 0) {
      return
    }

    // 计算总行数（所有表格的行数之和）
    let totalRows = 0
    tableList.forEach((table) => {
      totalRows += table.rows.length
    })

    // 获取所有第一列单元格的高度
    query.selectAll(".first-column-cell").boundingClientRect()

    // 获取所有第二列单元格的高度（可滚动区域的第一列）
    query.selectAll(".data-row .first-cell").boundingClientRect()

    query.exec((res) => {
      if (!res || res.length < 2) {
        console.warn("无法获取单元格高度信息")
        return
      }
      console.log(res)

      const firstColumnCells = res[0] || []
      const secondColumnCells = res[1] || []

      console.log("第一列单元格数量:", firstColumnCells.length)
      console.log("第二列单元格数量:", secondColumnCells.length)

      // 计算每个表格的行数，用于正确匹配单元格
      const tableRowCounts = tableList.map((table) => table.rows.length)
      let firstColumnIndex = 0
      let secondColumnIndex = 0
      const newHeights = [] // 二维数组 [tableIndex][rowIndex]

      // 遍历每个表格
      tableRowCounts.forEach((rowCount, tableIndex) => {
        const tableHeights = []

        // 遍历当前表格的每一行
        for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {
          const firstCellHeight =
            firstColumnCells[firstColumnIndex]?.height || 0
          const secondCellHeight =
            secondColumnCells[secondColumnIndex]?.height || 0

          // 取两个单元格中较高的高度
          const maxHeight = Math.max(firstCellHeight, secondCellHeight)
          tableHeights.push(maxHeight)

          console.log(
            `表格${tableIndex} 行${rowIndex}: 第一列高度=${firstCellHeight}, 第二列高度=${secondCellHeight}, 最大高度=${maxHeight}`
          )

          firstColumnIndex++
          secondColumnIndex++
        }

        newHeights.push(tableHeights)
      })

      // 更新数据，触发重新渲染
      this.setData({
        firstColumnHeights: newHeights,
      })

      console.log("更新后的高度二维数组:", newHeights)
    })
  },
})
