const APP = getApp()
const ROUTER = require("@/services/mpRouter")
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    imgurl: {
      type: String,
      value:
        "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbcxyzx/images/default/default_information.png",
    },
    item: {
      type: Object,
      value: null,
    },
  },
  options: {},

  /**
   * 组件的初始数据
   */
  data: {
    IMAGE_PREFIX: APP.globalData.CONFIG.IMAGE_PREFIX,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    goDetail(e) {
      const data = e.currentTarget.dataset.data
      ROUTER.navigateTo({
        path: "/package-goods/course/detail/index",
        query: {
          no: data.no,
        },
      })
    },
  },
})
