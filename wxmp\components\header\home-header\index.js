// components/home-header/index.js
const BASE_CACHE = require("@/utils/cache/baseCache")
const ROUTER = require("@/services/mpRouter")
Component({
  options: {
    addGlobalClass: true, //使用全局样式
    isLoad: false, // 页面是否加载完成
  },
  /**
   * 组件的属性列表
   */
  properties: {},

  /**
   * 组件的初始数据
   */
  data: {
    cacheStting: {},
    show: false,
    campusShow: false,
    provinceShow: false,
  },
  lifetimes: {
    attached() {
      this.setData({ isLoad: true })
      this.checkExam()
    },
    detached() {
      // 在组件实例被从页面节点树移除时执行
    },
  },
  pageLifetimes: {
    // 组件所在的页面被展示时执行
    show() {
      this.checkExam()
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 检查考试
    checkExam() {
      if (!this.data.isLoad) {
        return
      }
      this.setData({
        cacheStting: BASE_CACHE.getBaseCache(),
      })

      if (!this.data.cacheStting?.examDirection?.key) {
        this.toChangeExam()
      }
    },
    toChangeExam() {
      this.setData({
        show: true,
      })
      this.triggerEvent("open")
      // ROUTER.navigateTo({
      //   path: "/pages/entry/change-exam/index",
      // })
    },
    closeExam() {
      this.setData({
        show: false,
      })
      this.checkExam()
      this.triggerEvent("refresh")
    },
    // 副标题点击事件
    tapSubtitle() {
      console.log("点击进来没有")
      this.setData({
        provinceShow: true,
      })
    },
    tapButton() {
      ROUTER.navigateTo({
        path: "/pages/select/change/exam/index",
      })
    },
    closeProvince() {
      this.changeExam()
      this.setData({ provinceShow: false })
    },
    changeExam() {
      const examPopup = this.selectComponent("#examPopup")
      if (examPopup) {
        console.log("调用了没得")
        examPopup.comeIn()
      }
    },
    closeCampus() {
      this.changeExam()
      this.setData({ campusShow: false })
    },
    toSchool() {
      this.setData({
        campusShow: true,
      })
    },
  },
})
