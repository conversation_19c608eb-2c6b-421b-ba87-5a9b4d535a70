# 优先级 C 规则：推荐 {#priority-c-rules-recommended}

::: warning 注意
此 Vue.js 风格指南已过时，需要重新审查。如果您有任何问题或建议，请[提交 issue](https://github.com/vuejs/docs/issues/new)。
:::

当存在多个同样好的选项时，可以做出任意选择以确保一致性。在这些规则中，我们描述每个可接受的选项并建议一个默认选择。这意味着您可以在自己的代码库中自由做出不同的选择，只要您保持一致并有充分的理由。但请确实有充分的理由！通过适应社区标准，您将：

1. 训练您的大脑更容易解析您遇到的大多数社区代码
2. 能够复制和粘贴大多数社区代码示例而无需修改
3. 经常发现新员工已经习惯了您首选的编码风格，至少在 Vue 方面

## 组件/实例的选项的顺序 {#component-instance-options-order}

**组件/实例的选项应该有统一的顺序。**

这是我们推荐的组件选项默认顺序。它们被划分为几大类，所以你也能知道从插件里添加的新 property 应该放到哪里。

1. **全局感知**（要求组件以外的知识）

   - `name`

2. **模板编译器选项**（改变模板编译的方式）

   - `compilerOptions`

3. **模板依赖**（模板内使用的资源）

   - `components`
   - `directives`

4. **组合**（向选项里合并 property）

   - `extends`
   - `mixins`
   - `provide`/`inject`

5. **接口**（组件的接口）

   - `inheritAttrs`
   - `props`
   - `emits`

6. **组合式 API**（使用组合式 API 的入口点）

   - `setup`

7. **本地状态**（本地的响应式 property）

   - `data`
   - `computed`

8. **事件**（通过响应式事件触发的回调）

   - `watch`
   - 生命周期钩子（按照它们被调用的顺序）
     - `beforeCreate`
     - `created`
     - `beforeMount`
     - `mounted`
     - `beforeUpdate`
     - `updated`
     - `activated`
     - `deactivated`
     - `beforeUnmount`
     - `unmounted`
     - `errorCaptured`
     - `renderTracked`
     - `renderTriggered`

9. **非响应式的 property**（不依赖响应式系统的实例 property）

   - `methods`

10. **渲染**（组件输出的声明式描述）
    - `template`/`render`

## 元素 attribute 的顺序 {#element-attribute-order}

**元素（包括组件）的 attribute 应该有统一的顺序。**

这是我们为组件选项推荐的默认顺序。它们被划分为几大类，所以你也能知道新添加的自定义 attribute 和指令应该放到哪里。

1. **定义**（提供组件的选项）

   - `is`

2. **列表渲染**（创建多个变化的相同元素）

   - `v-for`

3. **条件渲染**（元素是否渲染/显示）

   - `v-if`
   - `v-else-if`
   - `v-else`
   - `v-show`
   - `v-cloak`

4. **渲染方式**（改变元素的渲染方式）

   - `v-pre`
   - `v-once`

5. **全局感知**（需要超越组件的知识）

   - `id`

6. **唯一的 attribute**（需要唯一值的 attribute）

   - `ref`
   - `key`

7. **双向绑定**（把绑定和事件结合起来）

   - `v-model`

8. **其它 attribute**（所有普通的绑定或未绑定的 attribute）

9. **事件**（组件事件监听器）

   - `v-on`

10. **内容**（覆写元素的内容）
    - `v-html`
    - `v-text`

## 组件/实例选项中的空行 {#empty-lines-in-component-instance-options}

**你可能想在多行 property 之间增加一个空行，特别是在这些选项一屏放不下，需要滚动才能都看到的时候。**

当你的组件开始觉得密集或难以阅读时，在多行 property 之间添加空行可以让其变得容易。在一些诸如 Vim 的编辑器里，这样格式化后的选项还能通过键盘被快速导航。

<div class="options-api">

<div class="style-example style-example-bad">
<h3>不好</h3>

```js
props: {
  value: {
    type: String,
    required: true
  },

  focused: {
    type: Boolean,
    default: false
  },

  label: String,
  icon: String
},

computed: {
  formattedValue() {
    // ...
  },

  inputClasses() {
    // ...
  }
}
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```js
// 没有空行也是可以的，只要组件
// 仍然容易阅读和导航。
props: {
  value: {
    type: String,
    required: true
  },
  focused: {
    type: Boolean,
    default: false
  },
  label: String,
  icon: String
},
computed: {
  formattedValue() {
    // ...
  },
  inputClasses() {
    // ...
  }
}
```

</div>

</div>

<div class="composition-api">

<div class="style-example style-example-bad">
<h3>不好</h3>

```js
defineProps({
  value: {
    type: String,
    required: true,
  },
  focused: {
    type: Boolean,
    default: false,
  },
  label: String,
  icon: String,
})
const formattedValue = computed(() => {
  // ...
})
const inputClasses = computed(() => {
  // ...
})
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```js
defineProps({
  value: {
    type: String,
    required: true,
  },

  focused: {
    type: Boolean,
    default: false,
  },

  label: String,
  icon: String,
})

const formattedValue = computed(() => {
  // ...
})

const inputClasses = computed(() => {
  // ...
})
```

</div>

</div>

## 单文件组件的顶级元素的顺序 {#single-file-component-top-level-element-order}

**[单文件组件](/guide/scaling-up/sfc)应该总是让 `<script>`、`<template>` 和 `<style>` 标签的顺序保持一致。且 `<style>` 要放在最后，因为另外两个标签至少要有一个。**

<div class="style-example style-example-bad">
<h3>不好</h3>

```vue-html [ComponentX.vue]
<style>/* ... */</style>
<script>/* ... */</script>
<template>...</template>
```

```vue-html [ComponentA.vue]
<script>/* ... */</script>
<template>...</template>
<style>/* ... */</style>
```

```vue-html [ComponentB.vue]
<template>...</template>
<script>/* ... */</script>
<style>/* ... */</style>
```

</div>

<div class="style-example style-example-good">
<h3>好</h3>

```vue-html [ComponentA.vue]
<script>/* ... */</script>
<template>...</template>
<style>/* ... */</style>
```

```vue-html [ComponentB.vue]
<script>/* ... */</script>
<template>...</template>
<style>/* ... */</style>
```

或者

```vue-html [ComponentA.vue]
<template>...</template>
<script>/* ... */</script>
<style>/* ... */</style>
```

```vue-html [ComponentB.vue]
<template>...</template>
<script>/* ... */</script>
<style>/* ... */</style>
```

</div>
