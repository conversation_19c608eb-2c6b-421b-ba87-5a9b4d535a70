import { getSelectedRegionsCache } from "../../utils/cache/regionCache"

Component({
  options: {
    addGlobalClass: true,
    multipleSlots: true,
  },

  /**
   * 组件的属性列表
   */
  properties: {
    // 菜单列表配置 - 统一为二维数组格式
    // 单行布局：[[menu1, menu2, menu3]] - 长度为1的二维数组
    // 分组布局：[[leftMenus], [rightMenus]] - 长度为2的二维数组
    menuList: {
      type: Array,
      value: [],
    },
    // 选中状态数据
    selectData: {
      type: Object,
      value: {},
    },
    // 当前展开的菜单key
    activeExpanded: {
      type: String,
      value: "",
    },
    // 外部样式类
    customClass: {
      type: String,
      value: "",
    },
    showPopupFilterMenu: {
      type: Boolean,
      value: false,
    },
    // Tab类型，用于区分不同Tab的地区缓存
    tabType: {
      type: String,
      value: "announcement", // 默认为公告Tab
    },
    // 是否隐藏地区列表
    hideRegionList: {
      type: Boolean,
      value: false,
    },
    // 是否显示筛选按钮
    isShowFilter: {
      type: Boolean,
      value: true,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 选中的地区列表
    selectedRegions: [],
  },

  /**
   * 组件的计算属性
   */
  computed: {
    // 是否为分组布局（二维数组长度为2）
    isGroupLayout() {
      const { menuList } = this.data
      return Array.isArray(menuList) && menuList.length === 2
    },

    // 左侧菜单列表（单行布局时为全部菜单，分组布局时为第一组）
    leftMenuList() {
      const { menuList } = this.data
      if (!Array.isArray(menuList) || menuList.length === 0) return []
      return menuList[0] || []
    },

    // 右侧菜单列表（仅分组布局时使用）
    rightMenuList() {
      const { menuList } = this.data
      if (!this.isGroupLayout()) return []
      return menuList[1] || []
    },
  },

  /**
   * 数据观察器
   */
  observers: {
    selectedRegions: function (selectedRegions) {
      // 当地区数据变化时，通知父组件更新菜单状态
      const hasSelectedRegions = selectedRegions && selectedRegions.length > 0
      this.triggerEvent("regionDataChange", {
        hasSelectedRegions: hasSelectedRegions,
        selectedRegions: selectedRegions,
      })
    },
    // 监听tabType变化，重新加载对应的地区数据
    tabType: function (newTabType, oldTabType) {
      if (newTabType && newTabType !== oldTabType) {
        console.log("TabType变化:", oldTabType, "->", newTabType)
        this.loadSelectedRegions()
      }
    },
  },

  /**
   * 组件生命周期
   */
  pageLifetimes: {
    // 组件所在的页面被展示时执行
    show() {
      console.log("组件showl")
      this.loadSelectedRegions()
    },
  },
  lifetimes: {
    attached() {
      // 组件初始化时从缓存加载地区数据
      this.loadSelectedRegions()
    },
    detached() {
      // 清理资源
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 从缓存加载选中的地区数据
     */
    loadSelectedRegions() {
      const { tabType } = this.data

      // 根据tabType映射到正确的缓存键
      let cacheKey = tabType
      if (tabType === "detail") {
        cacheKey = "detail" // 职位列表Tab
      } else if (tabType === "official") {
        cacheKey = "news_region" // 官方动态Tab
      }

      const cachedRegions = getSelectedRegionsCache(cacheKey) || []
      console.log(`加载${tabType}Tab的地区数据:`, cachedRegions)

      this.setData({
        selectedRegions: cachedRegions,
      })
    },

    /**
     * 刷新地区数据（供外部调用）
     */
    refreshRegions() {
      this.loadSelectedRegions()
    },

    /**
     * 处理菜单项点击事件
     * @param {Object} e 事件对象
     */
    handleMenuClick(e) {
      const { type, key } = e.currentTarget.dataset
      const { menuList } = this.data

      let currentItem = null

      // 从所有菜单组中查找菜单项
      if (Array.isArray(menuList)) {
        for (const menuGroup of menuList) {
          if (Array.isArray(menuGroup)) {
            currentItem = menuGroup.find((item) => item.key === key)
            if (currentItem) break
          }
        }
      }

      // 将点击事件抛出给父组件处理
      this.triggerEvent("menuClick", {
        type: type,
        key: key,
        currentItem: currentItem,
        menuList: menuList,
      })
    },

    /**
     * 处理地区项点击事件
     * @param {Object} e 事件对象
     */
    handleRegionClick(e) {
      const { index } = e.currentTarget.dataset
      const { selectedRegions } = this.data
      const clickedRegion = selectedRegions[index]

      // 将地区点击事件抛出给父组件处理
      this.triggerEvent("regionClick", {
        region: clickedRegion,
        index: index,
        selectedRegions: selectedRegions,
      })
    },

    /**
     * 处理添加地区按钮点击事件
     */
    handleAddRegionClick() {
      const { tabType } = this.data

      // 跳转到地区选择页面，传递Tab类型
      const ROUTER = require("../../services/mpRouter")
      ROUTER.navigateTo({
        path: "/pages/select/select-region/index",
        query: {
          tabType: tabType,
        },
      })
    },
  },
})
