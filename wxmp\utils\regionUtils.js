/**
 * 地区选择相关的工具函数
 */

/**
 * 解析地区key的公共方法
 * @param {string} key - 需要解析的key字符串，如 "110000-0-0"
 * @returns {object} 解析后的对象，包含 topId, parentId, level
 */
function parseRegionKey(key) {
  if (!key || typeof key !== "string") {
    return { topId: 0, parentId: 0, level: 1 }
  }

  const parts = key.split("-")
  if (parts.length >= 3) {
    return {
      topId: parseInt(parts[0]) || 0,
      parentId: parseInt(parts[1]) || 0,
      level: parseInt(parts[2]) || 1,
    }
  }

  // 如果key格式不正确，返回默认值
  return { topId: 0, parentId: 0, level: 1 }
}

/**
 * 格式化省份数据
 * @param {Array} data - 接口返回的原始数据
 * @returns {Array} 格式化后的省份数据
 */
function formatProvinceData(data) {
  if (!Array.isArray(data)) {
    return []
  }

  return data.map((item) => {
    item.count = 0
    item.selectedCount = 0

    return item
  })
}

/**
 * 格式化城市数据
 * @param {Array} data - 接口返回的原始数据
 * @returns {Array} 格式化后的城市数据
 */
function formatCityData(data) {
  if (!Array.isArray(data)) {
    return []
  }

  return data.map((item) => {
    return Object.assign({
      key: item.key || item.id,
      name: item.area_name || item.name,
      selected: false,
      selectedCount: 0,
      // 保存原始数据以便后续使用
      originalData: item,
    })
  })
}

/**
 * 格式化区县数据
 * @param {Array} data - 接口返回的原始数据
 * @returns {Array} 格式化后的区县数据（包含"全部"选项）
 */
function formatDistrictData(data) {
  if (!Array.isArray(data)) {
    return []
  }

  return data.map((item) => {
    return Object.assign({
      key: item.key || item.id,
      name: item.area_name || item.name,
      selected: false,
      // 保存原始数据以便后续使用
      originalData: item,
    })
  })
}

/**
 * 从地区对象中提取用于API调用的参数
 * @param {Object} region - 地区对象，包含originalData
 * @param {string} fallbackCode - 备用的code值
 * @returns {Object} 包含topId和parentId的对象
 */
function extractRegionParams(region, fallbackCode) {
  let topId = 0
  let parentId = 0

  if (region && region.originalData && region.originalData.key) {
    // 解析key获取参数
    const parsed = parseRegionKey(region.originalData.key)
    topId = parsed.topId
    parentId = parsed.topId // 对于下一级的查询，当前级别的topId作为下一级的parentId
  } else if (region && region.key) {
    // 如果没有key，使用code作为topId和parentId
    const key = parseInt(region.key) || 0
    topId = key
    parentId = key
  } else if (fallbackCode) {
    // 使用备用code
    const key = parseInt(fallbackCode) || 0
    topId = key
    parentId = key
  }

  return { topId, parentId }
}

module.exports = {
  parseRegionKey,
  formatProvinceData,
  formatCityData,
  formatDistrictData,
  extractRegionParams,
}
