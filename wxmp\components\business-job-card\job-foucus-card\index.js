const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Component({
  options: {
    addGlobalClass: true, //使用全局样式
    isLoad: false, // 页面是否加载完成
    multipleSlots: true,
  },
  /**
   * 组件的属性列表
   */
  properties: {
    isNewCard: {
      type: Boolean,
      value: false,
    },
    isSort: {
      type: Boolean,
      value: false,
    },
    // 单个职位数据
    jobData: {
      type: Object,
      value: {},
    },
    // 当前分组索引
    groupIndex: {
      type: Number,
      value: 0,
    },
    // 当前职位在分组中的索引
    jobIndex: {
      type: Number,
      value: 0,
    },
    // 是否可以向上排序
    topCanMove: {
      type: Boolean,
      value: false,
    },
    // 是否可以向下排序
    bottomCanMove: {
      type: Boolean,
      value: false,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    toJobDetail() {
      ROUTER.navigateTo({
        path: "/pages/job/detail/index",
        query: {
          id: this.data.jobData.id,
        },
      })
    },

    // 处理三个点点击事件
    onOptionsClick(e) {
      const jobData = this.data.jobData
      // 向父组件传递事件
      this.triggerEvent("optionsClick", {
        jobId: jobData.id,
        job_name: jobData.job_name,
        follows_id: jobData.follows_id,
        follows_tag: jobData.follows_tag,
      })
    },

    // 向上排序
    moveUp() {
      if (!this.data.topCanMove) {
        return
      }
      this.triggerEvent("moveUp", {
        groupIndex: this.data.groupIndex,
        jobIndex: this.data.jobIndex,
      })
    },

    // 向下排序
    moveDown() {
      if (!this.data.bottomCanMove) {
        return
      }
      this.triggerEvent("moveDown", {
        groupIndex: this.data.groupIndex,
        jobIndex: this.data.jobIndex,
      })
    },
  },
})
