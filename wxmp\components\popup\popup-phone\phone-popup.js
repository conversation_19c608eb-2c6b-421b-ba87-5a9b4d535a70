// components/popup/popup-wechat/index.js
const APP = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    weichatShow: {
      type: Boolean,
      value: false,
    },
    title: {
      type: String,
      value: "",
    },
    btnText: {
      type: String,
      value: "",
    },
    dataList: {
      type: Array,
      value: [],
      observer(newV) {
        let arr = newV.map((item, index) => {
          return {
            num: item,
            checked: index == 0,
          }
        })
        this.setData({
          dataArr: arr,
        })
      },
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    IMAGE_PREFIX: APP.globalData.CONFIG.IMAGE_PREFIX,
    dataArr: [],
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onClickHide() {
      this.triggerEvent("onClickHide", false)
    },
    selectItem(e) {
      let activeIndex = e.currentTarget.dataset.index
      let arr = this.data.dataArr
      arr.forEach((item, index) => {
        item.checked = activeIndex == index
      })
      this.setData({
        dataArr: arr,
      })
    },
    onCall() {
      let num = this.data.dataArr.filter((item) => item.checked)[0].num
      wx.makePhoneCall({
        phoneNumber: num,
        success: function (res) {
          console.log("成功调起拨打电话", res)
        },
        fail: function (err) {
          console.error("调起拨打电话失败", err)
        },
      })
    },
  },
})
