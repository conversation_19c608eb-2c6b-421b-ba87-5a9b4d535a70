apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: ${INGRESS_CLASS}
    nginx.ingress.kubernetes.io/server-snippet: |
      rewrite ^/$ /wa/ permanent;
      rewrite ^/login(.*)$ /wa/login$1 permanent;
  name: ${CI_PROJECT_NAME}
  namespace: ${NAMESPACE}
spec:
  rules:
  - host: ${DOMAIN}
    http:
      paths:
      - backend:
          serviceName: ${CI_PROJECT_NAME}
          servicePort: ${SERVICE_PORT}
        path: /${URI_PREFIX_S}