<view class="position-list-item {{ isSort?'pr120':''}}" catch:tap="toJobDetail">
  <image wx:if="{{jobData.follows_tag}}" class="label-img r64" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/{{jobData.follows_tag == 'enter'?'job_announcement':'job_position'}}.png"></image>
  <!-- <view wx:if="{{isSort}}" class="sort-area">
    <view class="sort-btn" catch:tap="moveUp">
      <image class="sort-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/{{topCanMove?'top_arrow':'gray_top_arrow'}}.png" mode="" />
    </view>
    <view class="sort-btn" catch:tap="moveDown">
      <image class="sort-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/{{bottomCanMove?'bottom_arrow':'gray_bottom_arrow'}}.png" mode="" />
    </view>
  </view> -->
  <view class="title-area">
    <view class="title text-ellipsis">{{jobData.job_name || '职位名称'}}</view>
    <view class="options-area" wx:if="{{!isSort}}">
      <image class="options-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/options_icon.png" mode="" catch:tap="onOptionsClick" />
    </view>
  </view>
  <view class="label-item">
    <text class="status" style="color: {{jobData.apply_status.color}};">{{jobData.apply_status.text}}</text>
    <text class="num text-ellipsis">{{ '共招' + jobData.need_num || '共招24'}}人</text>
  </view>
  <view class="label-item">
    招录单位<text class="num text-ellipsis">{{jobData.work_unit || '重庆市长寿区凤城街道办事处'}}</text>
  </view>
  <view class="label-item">
    来自公告<text class="num text-ellipsis">{{jobData.article_name || '2025年陕西省事业单位联考公告汇总联考公…'}}</text>
  </view>
  <view class="bottom-item">
    <text>{{jobData.region_province_name || '重庆'}}·{{jobData.exam_type_name || '事业单位'}}</text>
    <text wx:if="{{!isSort}}">{{jobData.release_time || '15分钟前'}}</text>
  </view>
</view>