import { getCookie, setCookie, delCookie } from "@/utils/cookies/core"

/**
 * 用户相关 Cookie 操作模块
 * 提供用户认证、考试职位、游客信息、学习记录等 Cookie 管理功能
 */

// ==================== 用户认证 Token ====================

/** 用户认证 Token 的 Cookie 键名 */
const USER_TOKEN = "userToken"

/**
 * 设置用户认证 Token
 * @param {string} token - 用户认证令牌
 * @param {Object} options - Cookie 选项
 * @param {number} options.expires - 过期时间（天数），默认7天
 * @returns {string|undefined} 设置成功返回 cookie 字符串
 */
export function setUserTokenCookie(token, options = {}) {
  if (!token || typeof token !== "string") {
    console.warn("设置用户 Token 失败：Token 无效")
    return undefined
  }

  return setCookie(USER_TOKEN, token, { expires: 7, ...options })
}

/**
 * 获取用户认证 Token
 * @returns {string} 用户认证令牌，不存在返回空字符串
 */
export function getUserTokenCookie() {
  return getCookie(USER_TOKEN) || ""
}

/**
 * 删除用户认证 Token
 * @returns {boolean} 删除成功返回 true
 */
export function delUserTokenCookie() {
  return delCookie(USER_TOKEN)
}
