/**
 * 判断当前浏览器是否为 iOS 浏览器
 *
 * @returns {boolean} 如果当前浏览器为 iOS 浏览器，则返回 true，否则返回 false
 */
export function isIOSBrowser() {
  return !!navigator.userAgent.match(/iPhone|iPad|iPod|Mac/i)
}

/**
 * 判断当前浏览器是否为 Android 浏览器
 *
 * @returns {boolean} 如果当前浏览器为 Android 浏览器，则返回 true，否则返回 false
 */
export function isAndroidBrowser() {
  return /Android/i.test(navigator.userAgent)
}

/**
 * 执行iOS平台的原生方法
 *
 * @param {string} funName - 原生方法的名称
 * @param {any} [param=null] - 传递给原生方法的参数
 */
function execIosFunction(funName, param = null) {
  return window && window.webkit && window.webkit.messageHandlers[funName].postMessage(param)
}

/**
 * 执行Android平台的原生方法
 *
 * @param {string} funName - 原生方法的名称
 * @param {any} [param=null] - 传递给原生方法的参数
 */
function execAndroidFunction(funName, param = null) {
  try {
    return window.tikuWeb && window.tikuWeb[funName](param)
  }catch (error) {
    console.log(error,'error')
  }
}

function androidFunction(funName) {
  return window.tikuWeb && window.tikuWeb[funName]()
}
/**
 * 获取设备版本号的数字部分
 * 从版本字符串中提取数字版本号，用于版本比较
 *
 * @param {string} version - 版本字符串，通常以字母开头后跟数字，如 "v1.2.3"
 * @returns {number} 返回版本号的数字部分，如果解析失败则返回0
 *
 * @description 该函数会去除版本字符串的第一个字符（通常是'v'），然后将剩余部分转换为数字
 * 主要用于App版本号的比较和判断
 *
 * @example
 * getDeviceVersionNumber("v1.2.3") // 返回 1.23
 */
export function getDeviceVersionNumber(version) {
  if (!version) {
    return 0
  }
  let number = Number("".slice.call(version, 1))
  return number > 0 ? number : 0
}

/**
 * 更新App的meta信息
 * 根据当前平台（iOS或Android）调用相应的meta更新函数
 *
 * @description 这是一个统一的入口函数，会自动检测当前平台并调用相应的更新函数
 * @example
 * // 在页面加载完成后调用
 * updateAppMeta()
 *
 */
export function updateAppMeta() {
  if (isAndroidBrowser()) {
    // 更新Android平台的meta信息
    execAndroidFunction("setShareHtml", {
      html: "<head>" + document.getElementsByTagName("html")[0].innerHTML + "</head>",
    })
  }

  // 更新iOS平台的meta信息
  if (isIOSBrowser()) {
    execIosFunction("canLoadShare", null)
  }
}

// 设置 meta
function setMeta(attributes) {
  console.log(attributes)

  // 确保至少有 property 或 name 以及 content 属性被提供
  if ((!attributes.property && !attributes.name) || attributes.content === undefined) {
    console.warn('setMeta至少需要一个 "property" or "name" 和一个 "content" 属性.')
    return
  }

  // 获取 head 部分
  let head = document.head || document.getElementsByTagName("head")[0]

  // 构建选择器字符串用于查找已存在的 meta 标签
  let selector = attributes.property
    ? `meta[property="${attributes.property}"]`
    : `meta[name="${attributes.name}"]`

  // 查找是否已经存在相同 property 或 name 的 meta 标签
  let existingMeta = head.querySelector(selector)

  if (existingMeta) {
    // 如果存在，则更新 content 属性
    existingMeta.setAttribute("content", attributes.content)
  } else {
    // 如果不存在，则创建一个新的 meta 元素
    let meta = document.createElement("meta")

    // 设置 meta 的属性
    if (attributes.property) {
      meta.setAttribute("property", attributes.property)
    } else if (attributes.name) {
      meta.setAttribute("name", attributes.name)
    }

    meta.setAttribute("content", attributes.content)

    // 将新的 meta 添加到 head 中
    head.appendChild(meta)
  }
}

// 设置 app meta 标签
export function setAppMeta(param) {
  setMeta({ property: "tiku:share_title", content: param.title })
  setMeta({ property: "tiku:share_description", content: param.desc })
  setMeta({ property: "tiku:share_image", content: param.imgUrl })
  setMeta({ property: "tiku:share_url", content: param.link })
  setMeta({ property: "tiku:share", content: true })
  setMeta({ property: "tiku:share_mp", content: param.mpPram })
  console.log("开启app分享")

  //app分享时，需要调用此方法 延长调用，否则app拿不到数据
  setTimeout(() => {
    updateAppMeta()
  }, 1000)
}

/**
 * 设置App内容高度
 *
 * @param {number} boxHeight - 内容高度
 * @description 该函数用于设置App内容高度，通常在页面加载完成后调用
 * @example
 * setAppContentHeight(500) // 设置内容高度为500px
 */
export function setAppContentHeight(boxHeight) {
  execIosFunction("acceptParameters", { contentHeight: boxHeight })
}

/**
 * 打开APP分享弹窗
 */
export function openAppSharePopup() {
  if (isIOSBrowser()) {
    execIosFunction("shareAction", null)
  } else {
    const param = {
      type: "openWidget",
      option: {
        widgetKey: "setShare",
        param: {},
      },
    }
    openUrlAction(param)
  }
}

/**
 * 关闭APP webview
 */
export function closeAppWebview() {
  if (isIOSBrowser()) {
    execIosFunction("closeAction", null)
  } else {
    androidFunction("finish")
  }
}

/**
 * 获取App状态栏高度
 */
export function getAppStatusHeight(data) {
  if (isIOSBrowser()) {
    return execIosFunction("getNavigationBarHeight2px", data)
  } else {
    return androidFunction("getStatusHeight")
  }
}

export function openAppFullscreen() {
  if (isIOSBrowser()) {
    return execIosFunction("fullscreenAction", 1)
  } else {
    return execAndroidFunction("fullscreenAction", 1)
  }
}

/**
 * 打开URL Action跳转
 * 通过自定义URL Scheme跳转到App的指定页面或执行特定操作
 *
 * @param {Object} option - 跳转配置对象
 * @param {string} option.unitKey - 目标单元的标识符，如 "Web"、"Native" 等
 * @param {Object} option.param - 传递给目标单元的参数对象
 * @param {string} [option.param.url] - 当unitKey为"Web"时，指定要打开的网页URL
 *
 * @description 该函数构造一个特殊的URL Scheme，用于在App内部进行页面跳转或功能调用。
 * URL格式：urlaction://openUnit/schema?params={"type":"openUnit","option":option}
 *
 * @example
 * // 打开网页
 * openUrlAction({
 *   unitKey: "Web",
 *   param: {
 *     url: "https://www.baidu.com"
 *   }
 * })
 *
 */
export function openUrlAction(data) {
  const url = `urlaction://openUnit/schema?params=${encodeURIComponent(JSON.stringify(data))}`

  window.location.href = url
}
