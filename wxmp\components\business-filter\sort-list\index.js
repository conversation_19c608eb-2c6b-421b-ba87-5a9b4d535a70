const {
  handleSingleSelect,
  handleMultiSelect,
} = require("@/services/selectionService")

Component({
  /**
   * 组件属性
   */
  properties: {
    // 考试列表数据
    sortList: {
      type: Array,
      value: [],
    },
    // 选中状态数据
    selected: {
      type: Array,
      value: [],
    },
    // 唯一键名，用于wx:key
    uniqueKey: {
      type: String,
      value: "value",
    },
    filterKey: {
      type: String,
      value: "",
    },
    isMultipleChoice: {
      type: Boolean,
      value: false,
    },
    show: {
      type: Boolean,
      value: false,
    },
  },

  /**
   * 组件数据
   */
  data: {
    tempSelected: [],
  },
  observers: {
    selected: function (newVal) {
      this.setData({
        tempSelected: newVal,
      })
    },
    show: function (newVal) {
      this.setData({
        tempSelected: this.data.selected,
      })
    },
  },
  /**
   * 组件方法
   */
  methods: {
    /**
     * 处理考试选择
     * @param {Object} e - 事件对象
     */
    handleExamSelect(e) {
      const { value } = e.currentTarget.dataset
      const data = this.data.tempSelected
      const tempSelected = this.data.isMultipleChoice
        ? handleMultiSelect(data, value)
        : handleSingleSelect(data, value)
      this.setData({
        tempSelected,
      })
      this.handleConfirm()
      console.log(tempSelected)
    },
    handleReset() {
      this.setData({
        tempSelected: [],
      })
      // 只触发事件，不处理选择逻辑
      this.triggerEvent("reset", { filterKey: this.data.filterKey })
    },
    handleConfirm() {
      // 只触发事件，不处理选择逻辑
      this.triggerEvent("confirm", {
        filterKey: this.data.filterKey,
        tempSelected: this.data.tempSelected,
      })
    },
  },
})
