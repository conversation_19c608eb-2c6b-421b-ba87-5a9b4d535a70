// pages/course/components/list/course-card-price-text/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 卷后价
    couponPrice: {
      type: String,
      value: "",
    },
    // 当前价格
    price: {
      type: String,
      value: "",
    },
    // 原价
    oldPrice: {
      type: String,
      value: "",
    },
    // 商品类型
    usability: {
      type: String,
      value: "",
    },
    // 描述
    desc: {
      type: String,
      value: " ",
    },
    // 前置标题
    pricePrefixText: {
      type: String,
      value: "",
    },
    // 拥有资格
    isQualification: {
      type: Boolean,
      value: false,
    },
    // 隐藏描述文本（首页需要）
    isHideDescText: {
      type: Boolean,
      value: false,
    },
    // 是否关联拼团
    isHasGroupon: {
      type: Boolean,
      value: false,
    },
    // 是否参与拼团
    isJoinGroupon: {
      type: Boolean,
      value: false,
    },
    // 文字左对齐
    isInHome: {
      type: Boolean,
      value: false,
    },
    // 课程类型
    courseType: {
      type: String,
      value: "course",
    },
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {},
})
