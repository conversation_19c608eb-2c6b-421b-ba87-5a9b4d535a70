// pages/special/components/job-card/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    jobData: {
      type: Object,
      value: {},
    },
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 关注按钮点击事件
     */
    onFocusClick() {
      const { jobData } = this.properties
      const isFocus = jobData.isFocus

      // 更新关注状态
      jobData.isFocus = !isFocus

      // 更新组件数据
      this.setData({
        jobData: jobData,
      })

      // 显示提示
      wx.showToast({
        title: isFocus ? "已取消关注" : "关注成功",
        icon: "none",
        duration: 1500,
      })
    },
  },
})
