/**
 * 文件类型与 MIME 类型映射
 */
const FILE_EXTENSIONS = {
  pdf: "application/pdf",
  jpg: "image/jpeg",
  jpeg: "image/jpeg",
  png: "image/png",
  gif: "image/gif",
  mp4: "video/mp4",
  m4v: "video/x-m4v",
  doc: "application/msword",
  docx:
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  xls: "application/vnd.ms-excel",
  xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ppt: "application/vnd.ms-powerpoint",
  pptx:
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
}

/**
 * 自定义错误类
 */
class FileError extends Error {
  constructor(message, code) {
    super(message)
    this.name = "FileError"
    this.code = code
  }
}

/**
 * 文件路径缓存管理
 */
const filePathCache = {
  cache: {},

  set(url, localPath, fileName) {
    this.cache[url] = { localPath, fileName }
  },

  get(url) {
    return this.cache[url]
  },

  async checkFileExists(filePath) {
    try {
      await wx.getFileInfo({ filePath })
      return true
    } catch (error) {
      return false
    }
  },

  clear() {
    this.cache = {}
  },
}

/**
 * 从URL中提取文件名
 * @param {string} url - 文件URL
 * @returns {string} 文件名
 */
function getFileNameFromUrl(url) {
  if (!url) return ""
  const pathWithoutQuery = url.split("?")[0]
  const fileName = pathWithoutQuery.split("/").pop()
  return decodeURIComponent(fileName)
}

/**
 * 获取文件扩展名
 * @param {string} url - 文件URL
 * @returns {string} 扩展名
 */
function getFileExtension(url) {
  const pathOnly = url.split("?")[0]
  const extMatch = /\.([^.\\/]+)$/.exec(pathOnly)
  return extMatch ? extMatch[1].toLowerCase() : ""
}

/**
 * 构建完整的文件路径
 * @param {string} basePath - 基础路径
 * @param {string} fileName - 文件名
 * @returns {string} 完整路径
 */
function buildFilePath(basePath, fileName) {
  return `${basePath}${fileName}`
}

/**
 * 下载文件（包含缓存处理）
 * @param {string} url - 文件URL
 * @param {string} fileName - 文件名
 * @returns {Promise<string>} 下载后的文件路径
 */
async function downloadFile(url, fileName) {
  // 检查缓存
  const cachedFile = filePathCache.get(url)
  if (cachedFile) {
    const fileExists = await filePathCache.checkFileExists(cachedFile.localPath)
    if (fileExists) {
      return cachedFile.localPath
    }
  }

  // 构建文件路径
  const basePath = wx.env.USER_DATA_PATH + "/"
  const filePath = buildFilePath(
    basePath,
    fileName || getFileInfo(url).fileName
  )
  // 下载文件
  return new Promise((resolve, reject) => {
    wx.downloadFile({
      url,
      filePath,
      success: (res) => {
        if (res.statusCode === 200) {
          // 更新缓存
          filePathCache.set(url, res.filePath, fileName)
          resolve(res.filePath)
        } else {
          reject(
            new FileError(
              `下载失败，状态码：${res.statusCode}`,
              "DOWNLOAD_ERROR"
            )
          )
        }
      },
      fail: (error) => {
        reject(
          new FileError(
            `下载失败：${error.errMsg || "未知错误"}`,
            "DOWNLOAD_ERROR"
          )
        )
      },
    })
  })
}

/**
 * 预览图片
 * @param {string} current - 当前图片URL
 * @returns {Promise<void>}
 */
function previewImage(current) {
  return new Promise((resolve, reject) => {
    wx.previewImage({
      current,
      urls: [current],
      success: resolve,
      fail: (error) => reject(new FileError("图片预览失败", "PREVIEW_ERROR")),
    })
  })
}

/**
 * 预览视频
 * @param {string} url - 视频URL
 * @returns {Promise<void>}
 */
function previewVideo(url) {
  return new Promise((resolve, reject) => {
    wx.previewMedia({
      sources: [{ url, type: "video" }],
      success: resolve,
      fail: (error) => reject(new FileError("视频预览失败", "PREVIEW_ERROR")),
    })
  })
}

/**
 * 打开文档
 * @param {string} filePath - 文件路径
 * @returns {Promise<void>}
 */
function openDocument(filePath) {
  return new Promise((resolve, reject) => {
    wx.openDocument({
      filePath,
      showMenu: true,
      success: resolve,
      fail: (error) => reject(new FileError("文档打开失败", "OPEN_ERROR")),
    })
  })
}

/**
 * 获取文件信息
 * @param {string} url - 文件URL
 * @returns {Object} 文件信息
 */
function getFileInfo(url) {
  if (!url || typeof url !== "string") {
    throw new FileError("无效的URL", "INVALID_URL")
  }

  const ext = getFileExtension(url)
  const mimeType = FILE_EXTENSIONS[ext]

  return {
    fileName: getFileNameFromUrl(url),
    mimeType,
    extension: ext,
  }
}

/**
 * 预览文件
 * @param {string} url - 文件URL
 * @param {Object} options - 配置选项
 * @returns {Promise<Object>} 文件信息
 */
async function previewFile(url, options = {}) {
  try {
    const fileInfo = getFileInfo(url)
    if (options.fileName) {
      fileInfo.fileName = `${options.fileName}.${fileInfo.extension}`
    }

    const extension = fileInfo.extension

    // 处理图片
    if (["jpg", "jpeg", "png", "gif"].includes(extension)) {
      await previewImage(url)
      return { fileInfo, type: "image" }
    }

    // 处理视频
    if (["mp4", "m4v"].includes(extension)) {
      await previewVideo(url)
      return { fileInfo, type: "video" }
    }

    // 处理文档
    const downloadedPath = await downloadFile(url, fileInfo.fileName)
    await openDocument(downloadedPath)
    return { fileInfo, type: "document" }
  } catch (error) {
    throw error instanceof FileError
      ? error
      : new FileError(error.message, "UNKNOWN_ERROR")
  }
}
/**
 * 解析 URL，返回基础 URL 和查询参数对象
 * @param {string} urlString - 完整的 URL
 * @returns {Object} 包含基础 URL 和查询参数的对象
 * @throws {FileError} 当URL格式无效时抛出错误
 */
function parseUrl(urlString) {
  if (!urlString || typeof urlString !== "string") {
    throw new FileError("无效的URL", "INVALID_URL")
  }

  try {
    const queryStart = urlString.indexOf("?")

    if (queryStart === -1) {
      return { baseUrl: removeProtocol(urlString), queryParams: {} }
    }

    const baseUrlWithProtocol = urlString.slice(0, queryStart)
    const queryString = urlString.slice(queryStart + 1)
    const baseUrl = removeProtocol(baseUrlWithProtocol)
    const queryParams = parseQueryString(queryString)

    return { baseUrl, queryParams }
  } catch (error) {
    throw new FileError("URL解析失败", "PARSE_ERROR")
  }
}
/**
 * 移除 URL 中的协议部分
 * @param {string} url - 完整的 URL
 * @returns {string} 移除协议后的 URL
 */
function removeProtocol(url) {
  const protocolEndIndex = url.indexOf("//") + 2
  return protocolEndIndex > 1 ? url.slice(protocolEndIndex) : url
}

/**
 * 解析查询字符串
 * @param {string} queryString - 查询字符串
 * @returns {Object} 解析后的查询参数对象
 */
function parseQueryString(queryString) {
  const queryParams = {}
  queryString.split("&").forEach((pair) => {
    if (pair) {
      const [key, value] = pair.split("=", 2)
      queryParams[decodeURIComponent(key)] = decodeURIComponent(value || "")
    }
  })
  return queryParams
}
module.exports = {
  previewFile,
  downloadFile,
  filePathCache,
  FileError,
  parseUrl,
}
