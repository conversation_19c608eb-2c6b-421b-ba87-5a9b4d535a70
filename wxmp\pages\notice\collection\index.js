const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()
const PopupMenuFilterMixin = require("@/components/popup/popup-menu-filter/mixin")
const { processMenuList } = require("@/services/menuServices")
const { handleMultiSelect } = require("@/services/selectionService")
const {
  setCollectionSelectForTemplateCache,
  getCollectionSelectForTemplateCache,
  setJobDetailSelectForTemplateCache,
  getJobDetailSelectForTemplateCache,
  getJobListForTemplateCache,
  setJobDetailPopuSelectForTemplateCache,
  getJobDetailPopuSelectForTemplateCache,
} = require("@/utils/cache/filterCache")
let PAGE_OPTIONS = {} // 页面进入时参数
/**
 * 专题合辑页面
 * 集成筛选菜单功能
 * @mixes PopupMenuFilterMixin 弹窗筛选混合方法
 */

const pageConfig = Object.assign({}, PopupMenuFilterMixin, {
  data: {
    show_white: false,
    isCollect: false,

    // 筛选菜单相关数据
    showPopupFilterMenu: false,
    headerHeight: 100,
    menuSticky: false,
    menuOffsetTop: 0,

    // 页面滚动控制
    pageScrollDisabled: false,

    savedScrollTop: 0,
    overlayTop: 0,
    menuList: [],
    collectionSelectForTemplate: {},
    collectionMenuData: {},
    activeExpanded: "",
    page: 1,
    // 分页相关状态
    hasMore: true, // 是否还有更多数据
    isLoading: false, // 是否正在加载
    articleList: [], // 文章列表

    // 静态tab配置（所有可能的tab）
    allTabsConfig: [
      { key: "detail", title: "公告详情", alwaysShow: true },
      { key: "position", title: "职位列表", conditionField: "job_num" },
      { key: "official", title: "官方动态", conditionField: "notice_num" },
    ],
    // 动态生成的tab列表
    tabList: ["公告详情"],
    // 当前激活的tab索引
    activeIndex: 0,
    // tab键值到索引的映射
    tabKeyToIndex: { detail: 0 },
    // 索引到tab键值的映射
    indexToTabKey: { 0: "detail" },
    isExpanded: false,
    showToggle: false,
    maxHeight: 800, // 默认收起状态的最大高度(px)
    actualHeight: 0, // 内容实际高度
    show: false,
    show_white: false,
    isCollect: false,
    jobDetailSelectForTemplate: {},
    detailMenuData: {},
    activeExpanded: "",
    hasPageIcon: false,
    page: 1,
    pageType: "",
  },

  async onLoad(options) {
    PAGE_OPTIONS = options
    // 初始化弹窗管理功能
    this.initPopupMenuFilterMixin()
    await this.getNoticeDetail()
    // 页面渲染完成后获取导航栏高度并设置sticky的top值
    this.setMenuStickyTop()
    // 从缓存恢复筛选条件
    if (this.data.pageType == "collection") {
      await this.restoreFilterFromCache()
      this.applyFilter()
    } else {
      await this.updateJobSelectForTemplateFromCache()
    }
  },
  onShow() {
    // 初始化胶囊按钮颜色
    if (this.data.show_white) {
      // 白色背景时，胶囊按钮显示黑色
      wx.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff",
      })
    } else {
      // 深色背景时，胶囊按钮显示白色
      wx.setNavigationBarColor({
        frontColor: "#ffffff",
        backgroundColor: "#000000",
      })
    }
  },

  async getArticleChildList(param, isLoadMore = false) {
    // 如果正在加载或没有更多数据，直接返回
    if (
      this.data.isLoading ||
      (!isLoadMore && !this.data.hasMore && this.data.page > 1)
    ) {
      return
    }

    const params = {
      page: this.data.page,
      article_id: PAGE_OPTIONS.id,
      ...param,
    }

    this.setData({
      isLoading: true,
    })

    try {
      const res = await UTIL.request(API.getArticleChildList, params)
      if (res && res.error && res.error.code === 0 && res.data) {
        const newList = res?.data?.list || []

        // 根据是否为加载更多来决定如何处理数据
        let updatedArticleList
        if (isLoadMore) {
          // 分页加载：追加到现有数据
          updatedArticleList = [...this.data.articleList, ...newList]
        } else {
          // 首次加载或筛选：直接使用新数据
          updatedArticleList = newList
        }

        this.setData({
          articleList: updatedArticleList,
          hasMore: newList.length > 0, // 如果返回的数据为空，说明没有更多数据
          isLoading: false,
        })

        console.log(res, "文章列表数据")
        console.log(
          this.data.collectionMenuData,
          this.data.menuList,
          this.data.collectionSelectForTemplate,
          "--------------------------------------222"
        )
      } else {
        this.setData({
          isLoading: false,
          hasMore: false,
        })
      }
    } catch (error) {
      console.error("获取文章列表失败:", error)
      this.setData({
        isLoading: false,
      })
    }
  },
  async getNoticeDetail() {
    try {
      const res = await UTIL.request(API.getArticleDetail, {
        id: PAGE_OPTIONS?.id,
      })
      if (res && res.error && res.error.code === 0 && res.data) {
        console.log(res, "公告详情")
        let resData = res.data
        let pageType = ""
        if (res.data.type == "article_list" && res.data.show_type == "list") {
          pageType = "collection"
        } else {
          pageType = "detail"
        }
        this.setData({
          isRequest: true,
          noticeData: res.data,
          pageType,
          isPageLoadComplete: true,
          isShowResume:
            resData.detail?.complete_progress?.is_tips === 1 &&
            !APP.globalData.hasResume,
        })
        if (res.data?.detail?.filter_menu?.length) {
          this.initDynamicMenu(res.data.detail.filter_menu)
        }
        if ((pageType = "detail")) {
          // 根据动态数据生成Tab列表
          this.generateDynamicTabs()
          // 重新检测内容高度（因为数据已更新）
          this.checkContentHeight()
        }
      } else {
        this.setData({
          isRequest: true,
        })
      }
    } catch (error) {
      console.error("获取公告详情失败:", error)
    }
  },
  // 详情tab初始化
  generateDynamicTabs() {
    const { noticeData } = this.data
    if (!noticeData || !noticeData.detail) {
      console.log("公告数据未加载，使用默认tab配置")
      return
    }

    const detail = noticeData.detail
    const dynamicTabs = []
    const tabKeyToIndex = {}
    const indexToTabKey = {}

    this.data.allTabsConfig.forEach((tabConfig) => {
      const { key, title, alwaysShow, conditionField } = tabConfig

      // 总是显示的tab或满足条件的tab
      if (alwaysShow || (conditionField && detail[conditionField] > 0)) {
        const index = dynamicTabs.length
        dynamicTabs.push(title)
        tabKeyToIndex[key] = index
        indexToTabKey[index] = key
      }
    })

    console.log("动态生成的tabs:", {
      tabs: dynamicTabs,
      keyToIndex: tabKeyToIndex,
      indexToKey: indexToTabKey,
      conditions: {
        job_num: detail.job_num,
        notice_num: detail.notice_num,
      },
    })

    this.setData({
      tabList: dynamicTabs,
      tabKeyToIndex,
      indexToTabKey,
    })
  },
  checkContentHeight() {
    console.log("进来没得")
    // 延迟执行，确保rich-text内容已经渲染
    let retryCount = 0
    const maxRetries = 3
    this.checkHeight(retryCount, maxRetries)
  },
  checkHeight(retryCount, maxRetries) {
    const query = wx.createSelectorQuery().in(this)
    query.select("#richTextContent").boundingClientRect()
    query.exec((res) => {
      console.log("进来没得1")
      if (res && res[0] && res[0].height > 0) {
        const contentHeight = res[0].height
        const needToggle = contentHeight > this.data.maxHeight

        console.log("Rich-text content height:", contentHeight)
        console.log("Max height:", this.data.maxHeight)
        console.log("Need toggle:", needToggle)

        this.setData({
          actualHeight: contentHeight,
          showToggle: needToggle,
          // 如果内容高度小于等于maxHeight，默认展开
          isExpanded: !needToggle,
        })
      } else if (retryCount < maxRetries) {
        // 如果高度获取失败，进行重试
        retryCount++
        console.log(
          `Rich-text高度检测失败，正在重试 ${retryCount}/${maxRetries}`
        )
        setTimeout(checkHeight, 300)
      } else {
        // 重试失败，设置默认状态
        console.warn("Rich-text高度检测失败，使用默认状态")
        this.setData({
          showToggle: false,
          isExpanded: true,
        })
      }
    })
  },
  updateJobSelectForTemplateFromCache() {
    const jobDetailSelectForTemplate = this.data.jobDetailSelectForTemplate
    const cacheJobSelectForTemplate = getJobDetailSelectForTemplateCache()
    const cachePositionSelectForTemplate = getJobListForTemplateCache()
    for (const key in jobDetailSelectForTemplate) {
      if (cacheJobSelectForTemplate[key]) {
        jobDetailSelectForTemplate[key] = cacheJobSelectForTemplate[key]
      }
    }
    // 处理职位筛选缓存 - 赋值给 filter_list
    if (
      cachePositionSelectForTemplate &&
      Object.keys(cachePositionSelectForTemplate).length > 0
    ) {
      jobDetailSelectForTemplate.filter_list = cachePositionSelectForTemplate
    }
    this.setData({
      jobDetailSelectForTemplate,
    })
  },
  handleJobMenuFilterConfirm(e) {
    const { filterKey, tempSelected } = e.detail
    // 清空展开状态
    this.setData({
      [`jobDetailSelectForTemplate.${filterKey}`]: tempSelected,
      activeExpanded: "",
    })
    setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
    this.hidePopupMenuFilter()
    this.closeSelectBoxPopup()
    this.applyFilter()
  },
  /**
   * 初始化动态菜单
   * @param {Array} serverMenuList 服务器返回的菜单列表
   */
  initDynamicMenu(serverMenuList) {
    if (serverMenuList && serverMenuList.length) {
      // 使用提取后的纯函数处理菜单
      const menuList = processMenuList(serverMenuList)
      if (this.data.pageType == "collection") {
        const collectionMenuData = {}
        serverMenuList.forEach((item) => {
          collectionMenuData[item.filter_key] = item
        })
        this.setData({
          collectionMenuData,
          menuList,
          collectionSelectForTemplate: this.getNoticeSelectFromMenuData(
            serverMenuList
          ),
        })
      } else {
        const hasPageIcon = menuList.some((menuGroup) =>
          menuGroup.some((item) => item.type === "page_icon")
        )
        const detailMenuData = {}
        serverMenuList.forEach((item) => {
          detailMenuData[item.filter_key] = item
        })

        this.setData({
          detailMenuData,
          menuList,
          jobDetailSelectForTemplate: this.getNoticeSelectFromMenuData(
            serverMenuList
          ),
          hasPageIcon,
        })
      }
    }
  },
  /**
   * 从菜单数据初始化collectionSelectForTemplate对象
   * 数据结构：{ filter_key: Array<value> } 或 { filter_key: { nested_key: Array<value> } }
   * @param {Array} serverMenuList 原始菜单数据 (article_filter_menu)
   */
  getNoticeSelectFromMenuData(serverMenuList) {
    const result = {}
    serverMenuList.forEach((menu) => {
      const filterKey = menu.filter_key

      if (filterKey === "filter_list") {
        result[filterKey] = {}
        menu.data.forEach((filterGroup) => {
          const groupFilterKey = filterGroup.filter_key
          if (groupFilterKey) {
            result[filterKey][groupFilterKey] = []
          }
        })
      } else if (filterKey) {
        result[filterKey] = []
      }
      if (
        this.data?.noticeData?.detail?.child_article_list.length > 0 &&
        this.data.pageType == "detail"
      ) {
        result["article_list"] = [
          this.data.noticeData.detail.child_article_list[0],
        ]
      }
    })
    return result
  },

  handleNoticeMenuFilterConfirm(e) {
    const { filterKey, tempSelected } = e.detail
    // 清空展开状态
    this.setData({
      [`collectionSelectForTemplate.${filterKey}`]: tempSelected,
      activeExpanded: "",
    })
    setCollectionSelectForTemplateCache(this.data.collectionSelectForTemplate)
    this.hidePopupMenuFilter()
    this.applyFilter()
  },
  /**
   * 应用筛选条件 - 统一处理两个Tab
   */
  async applyFilter() {
    // 重置分页状态，但不清空articleList，避免缺省图闪现
    this.setData({
      page: 1,
      hasMore: true,
      // articleList: [] // 注释掉，避免缺省图闪现
    })
    if (this.data.pageType == "collection") {
      const apiParams = this.buildApiParams(
        this.data.collectionSelectForTemplate
      )
      console.log("拿到的参数", apiParams)
      await this.getArticleChildList(apiParams, false)
    } else {
      const apiParams = this.buildApiParams(
        this.data.jobDetailSelectForTemplate
      )
      console.log("拿到的参数", apiParams)
      await this.getJobList(apiParams, false)
    }
  },

  /**
   * 构造API请求参数 - 直接从 noticeSelectForTemplate 构建
   * @returns {Object} API参数对象
   */
  buildApiParams(selectedData) {
    if (this.data.pageType == "collection") {
      const apiParams = {}

      Object.keys(selectedData).forEach((keyName) => {
        const data =
          selectedData[keyName] || (keyName !== "filter_list" ? [] : {})

        if (keyName === "fit_me" || keyName === "has_tenure") {
          apiParams[keyName] = data[0] || null
        }
        if (keyName === "exam_type") {
          apiParams[keyName] = data
        }

        if (keyName === "sort_list") {
          apiParams[keyName] = data
        }

        if (keyName === "region") {
          const regionData = data
            .map((region) => {
              // 如果已经是字符串格式，直接使用
              return region.key
            })
            .filter((regionCode) => regionCode) // 过滤掉空值
          console.log(regionData, "---------------")
          apiParams["region"] = regionData
        }

        if (keyName === "filter_list") {
          Object.keys(data).forEach((cKeyName) => {
            const cData = data[cKeyName] || []

            if (cKeyName === "need_num") {
              const numValue = Number(cData[0])
              const val = !isNaN(numValue) ? numValue : null
              apiParams[cKeyName] = val
            }

            if (cKeyName === "apply_status") {
              apiParams[cKeyName] = cData
            }
          })
        }
      })

      return UTIL.convertArraysToString(apiParams)
    } else {
      const apiParams = {}

      Object.keys(selectedData).forEach((keyName) => {
        const data =
          selectedData[keyName] || (keyName !== "filter_list" ? [] : {})

        if (keyName === "fit_me" || keyName === "has_tenure") {
          apiParams[keyName] = data[0] || null
        }
        if (keyName === "my_major") {
          apiParams[keyName] = data
        }

        if (keyName === "sort_list") {
          apiParams[keyName] = data
        }

        if (keyName === "my_education") {
          apiParams[keyName] = data
        }

        if (keyName === "exam_type") {
          apiParams[keyName] = data
        }

        if (keyName === "paper_type") {
          apiParams[keyName] = data
        }

        if (keyName === "apply_region") {
          const regionData = data
            .map((region) => {
              // 如果已经是字符串格式，直接使用
              return region.key
            })
            .filter((regionCode) => regionCode) // 过滤掉空值
          console.log(regionData, "---------------")
          apiParams["region"] = regionData
        }
        if (keyName === "article_list") {
          const regionData = data.map((region) => {
            // 如果已经是字符串格式，直接使用
            return region.id
          })
          console.log(regionData, "---------------")
          apiParams["article_list"] = regionData
        }
        if (keyName === "filter_list") {
          Object.keys(data).forEach((filterKey) => {
            const filterValue = data[filterKey] || []

            // 招录人数 - 转换为数字
            if (filterKey === "need_num") {
              const numValue = Number(filterValue[0])
              const val = !isNaN(numValue) ? numValue : null
              apiParams[filterKey] = val
            }
            // 学历要求
            else if (filterKey === "education") {
              apiParams[filterKey] = filterValue
            }
            // 专业要求
            else if (filterKey === "major") {
              apiParams[filterKey] = filterValue
            }
            // 政治面貌
            else if (filterKey === "politics_face") {
              apiParams[filterKey] = filterValue
            }
            // 应届毕业生
            else if (filterKey === "fresh_graduate") {
              apiParams[filterKey] = filterValue
            }
            // 报名状态
            else if (filterKey === "apply_status") {
              apiParams[filterKey] = filterValue
            }
            // 其他字段的默认处理
            else {
              apiParams[filterKey] = filterValue
            }
          })
        }
        if (keyName === "apply_time") {
          apiParams[keyName] = data
        }
        if (keyName === "apply_status") {
          apiParams[keyName] = data
        }
      })
      return UTIL.convertArraysToString(apiParams)
    }
  },
  async getJobList(filterConditions = {}, isLoadMore = false) {
    // 如果正在加载或没有更多数据，直接返回
    if (
      this.data.isLoading ||
      (!isLoadMore && !this.data.hasMore && this.data.page > 1)
    ) {
      return
    }

    try {
      // 确保传递的参数不为空，至少是一个空对象
      const requestParams = {
        page: this.data.page,
        ...filterConditions,
        id: PAGE_OPTIONS.id,
      }

      this.setData({
        isLoading: true,
      })

      const res = await UTIL.request(API.getJobList, requestParams)

      if (res && res.error && res.error.code === 0 && res.data) {
        console.log("获取职位列表成功:", res.data)

        // 处理职位列表数据
        const newList = res.data.list || []

        // 根据是否为加载更多来决定如何处理数据
        let updatedJobList
        if (isLoadMore) {
          // 分页加载：追加到现有数据
          updatedJobList = [...this.data.jobList, ...newList]
        } else {
          // 首次加载或筛选：直接使用新数据
          updatedJobList = newList
        }

        // 更新页面数据
        this.setData({
          jobList: updatedJobList,
          hasMore: newList.length > 0, // 如果返回的数据为空，说明没有更多数据
          isLoading: false,
          isRequest: true,
        })

        return res.data
      } else {
        console.error("获取职位列表失败:", res)
        this.setData({
          isLoading: false,
          hasMore: false,
        })
        return null
      }
    } catch (error) {
      console.error("请求职位列表异常:", error)
      this.setData({
        isLoading: false,
      })
      return null
    }
  },
  /**
   * 处理菜单项点击事件
   * @param {Object} e 事件对象
   */
  handleMenuClick(e) {
    const { type, currentItem } = e.detail || e.currentTarget.dataset
    const { showPopupFilterMenu, collectionSelectForTemplate } = this.data
    const filterKey = currentItem.filter_key

    const currentMenuSelected = collectionSelectForTemplate[filterKey]

    if (type !== "apply_region") {
      this.setData({
        showRegionList: false,
      })
    }

    if (type === "check" || type === "apply_region") {
      this.hidePopupMenuFilter()
    } else if (
      showPopupFilterMenu === true &&
      this.data.activeExpanded === filterKey
    ) {
      this.hidePopupMenuFilter()
    } else {
      this.showPopupMenuFilter()
    }

    this.setData({
      activeExpanded: this.data.activeExpanded === filterKey ? "" : filterKey, // 设置当前key为展开状态
    })

    // 处理 filter_key 为 apply_region 的情况：走公告现在的逻辑
    if (filterKey === "apply_region") {
      // 切换地区列表显示状态
      this.setData({
        showRegionList: !this.data.showRegionList,
      })
      return
    }

    // 处理check类型：单选，不打开弹窗，点击选中，再次点击取消
    if (type === "check") {
      this.setData({
        [`collectionSelectForTemplate.${filterKey}`]: handleMultiSelect(
          currentMenuSelected || [],
          1
        ),
      })
      setCollectionSelectForTemplateCache(this.data.collectionSelectForTemplate)
      this.applyFilter()
      return
    }
  },

  handleRegionConfirmSelection(e) {
    const { filterKey, tempSelected } = e.detail
    console.log(filterKey, tempSelected)
    if (this.data.pageType == "collection") {
      this.setData({
        [`collectionSelectForTemplate.${filterKey}`]: tempSelected,
        activeExpanded: "",
      })
      setCollectionSelectForTemplateCache(this.data.collectionSelectForTemplate)
    } else {
      this.setData({
        [`jobDetailSelectForTemplate.${filterKey}`]: tempSelected,
        activeExpanded: "",
      })
      setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
    }
    this.applyFilter()
    this.hidePopupMenuFilter()
  },
  /**
   * 从缓存恢复筛选条件
   */
  async restoreFilterFromCache() {
    await this.updateNoticeSelectForTemplateFromCache()
  },
  /**
   * 从缓存恢复公告选中状态 (noticeSelectForTemplate)
   */
  updateNoticeSelectForTemplateFromCache() {
    const collectionSelectForTemplate = this.data.collectionSelectForTemplate
    const cacheCollectionSelectForTemplate = getCollectionSelectForTemplateCache()
    for (const key in collectionSelectForTemplate) {
      if (cacheCollectionSelectForTemplate[key]) {
        collectionSelectForTemplate[key] = cacheCollectionSelectForTemplate[key]
      }
    }
    this.setData({
      collectionSelectForTemplate,
    })
  },
  handlePopupClose() {
    console.log("弹窗关闭事件")
    // 清空展开状态
    this.setData({
      activeExpanded: "",
    })
    // 调用hidePopupMenuFilter真正关闭弹窗（包含恢复备份的逻辑）
    this.hidePopupMenuFilter()
  },

  // 设置菜单sticky的top值
  setMenuStickyTop() {
    const query = wx.createSelectorQuery()
    query
      .select("#commonHeader")
      .boundingClientRect((headerRect) => {
        let headerHeight = 100 // 默认高度
        console.log("获取到的header信息:", headerRect)

        if (headerRect) {
          headerHeight = headerRect.height
          console.log("成功获取导航栏高度:", headerHeight)
        } else {
          console.log("无法获取导航栏高度，使用降级方案")
          // 降级方案：通过系统信息计算
          const systemInfo = wx.getSystemInfoSync()
          const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
          headerHeight = menuButtonInfo.bottom
          console.log("降级方案计算高度:", headerHeight)
        }

        // 获取菜单容器的位置信息
        const menuQuery = wx.createSelectorQuery()
        menuQuery.select(".main-top").boundingClientRect()
        menuQuery.selectViewport().scrollOffset()
        menuQuery.exec((res) => {
          const menuRect = res[0]
          const scrollOffset = res[1]

          let menuOffsetTop = 0
          if (menuRect) {
            // 计算菜单距离页面顶部的距离
            menuOffsetTop = menuRect.top + scrollOffset.scrollTop
          }

          // 设置数据
          this.setData({
            headerHeight: headerHeight - 15,
            menuOffsetTop: menuOffsetTop - 15,
          })

          console.log("导航栏高度:", headerHeight)
          console.log("菜单初始位置:", menuOffsetTop)
        })
      })
      .exec()
  },

  // 去搜索页
  goSearch() {
    ROUTER.navigateTo({
      path: "/pages/search/index",
      query: {
        type: 1,
        placeholder: "搜索职位职位代码为关键词",
      },
    })
  },
  // 展开收起
  toggleText() {
    this.setData({
      isExpanded: !this.data.isExpanded,
    })
  },

  // 考试列表弹窗相关逻辑
  async openExam() {
    await this.updateJobPopuSelectForTemplateFromCache()
    this.applyPopuFilter()
    this.setData(
      {
        show: true,
      },
      () => {
        // 在setData回调中执行，确保DOM已更新
        // 获取 popu-menu 的位置信息
        this.calculateExamPopupMenuPosition()
      }
    )
  },
  // 获取考试列表弹窗相关逻辑
  async getChildListForDetail(filterConditions = {}) {
    const requestParams = {
      ...filterConditions,
      article_id: PAGE_OPTIONS.id,
    }
    const res = await UTIL.request(API.getChildListForDetail, requestParams)
    if (res && res.error && res.error.code === 0 && res.data) {
      this.setData({
        examData: res.data,
      })
      if (res.data.filter_menu.length) {
        this.initexamPopuMenu(res.data.filter_menu)
      }
    }
  },
  // 考试列表弹窗筛选相关逻辑
  initexamPopuMenu(serverMenuList) {
    if (serverMenuList && serverMenuList.length) {
      // 使用提取后的纯函数处理菜单
      const menuList = processMenuList(serverMenuList)
      const examMenuData = {}
      serverMenuList.forEach((item) => {
        examMenuData[item.filter_key] = item
      })

      this.setData({
        examMenuData,
        examList: menuList,
        examPopuSelectForTemplate:
          this.data?.examPopuSelectForTemplate?.apply_status?.length > 0
            ? this.data.examPopuSelectForTemplate
            : this.getExamPopuSelectFromMenuData(serverMenuList),
      })
    }
  },
  // 初始化考试列表弹窗筛选状态
  getExamPopuSelectFromMenuData(serverMenuList) {
    const result = {}
    serverMenuList.forEach((menu) => {
      const filterKey = menu.filter_key
      console.log(filterKey, "---------------------------------------------")
      if (filterKey === "filter_list") {
        result[filterKey] = {}

        menu.data.forEach((category) => {
          if (category.list) {
            category.list.forEach((filterGroup) => {
              const groupFilterKey = filterGroup.filter_key
              if (groupFilterKey) {
                // 直接将所有筛选项提取到 filter_list 下，去掉中间层级
                result[filterKey][groupFilterKey] = []
              }
            })
          }
        })
      } else if (filterKey) {
        result[filterKey] = []
      }
      if (filterKey == "apply_status") {
        result[filterKey] = [2]
      }
    })
    return result
  },
  // 考试列表弹窗筛选项点击
  handleExamMenuClick(e) {
    const { type, currentItem } = e.detail || e.currentTarget.dataset
    const { showExamRegionPopup, examPopuSelectForTemplate } = this.data
    const filterKey = currentItem.filter_key
    console.log(currentItem, "----------------------------")
    const currentMenuSelected = examPopuSelectForTemplate[filterKey]

    if (
      type === "apply_region" ||
      type == "apply_status" ||
      type == "sort_time"
    ) {
      this.hidePopupMenu()
    } else if (
      showExamRegionPopup === true &&
      this.data.activeExamExpanded === filterKey
    ) {
      this.hidePopupMenu()
    } else {
      console.log("进的这里？")
      this.setData({
        showExamRegionPopup: true,
      })
    }
    this.setData({
      activeExamExpanded:
        this.data.activeExamExpanded == filterKey ? "" : filterKey, // 设置当前key为展开状态
    })

    // 处理 filter_key 为 apply_region 的情况：走公告现在的逻辑
    if (filterKey === "apply_status") {
      // 切换地区列表显示状态
      this.setData({
        showApplyStatus: !this.data.showApplyStatus,
      })
      return
    }

    // 处理sort_time类型：单选，不打开弹窗，点击选中，再次点击取消
    if (type === "sort_time") {
      const currentValue = currentMenuSelected
      let newValue
      if (!currentValue || currentValue.length === 0) {
        // 没有值，设置为1
        newValue = [1]
      } else if (currentValue[0] == 1) {
        // 值为1，设置为2
        newValue = [2]
      } else if (currentValue[0] == 2) {
        // 值为2，设置为1
        newValue = [1]
      } else {
        // 其他情况，默认设置为1
        newValue = []
      }
      console.log(newValue, "拿到的")

      this.setData({
        [`examPopuSelectForTemplate.${filterKey}`]: newValue,
      })
      setJobDetailPopuSelectForTemplateCache(
        this.data.examPopuSelectForTemplate
      )
      this.applyPopuFilter()
      return
    }
  },
  // 关闭考试列表里面筛选项的弹窗
  hidePopupMenu() {
    this.setData({
      showExamRegionPopup: false,
    })
  },
  // 选项确认公用逻辑
  handlePopuMenuFilterConfirm() {
    const { filterKey, tempSelected } = e.detail
    // 清空展开状态
    this.setData({
      [`examPopuSelectForTemplate.${filterKey}`]: tempSelected,
      activeExamExpanded: "",
    })
    setJobDetailSelectForTemplateCache(this.data.examPopuSelectForTemplate)
    this.hidePopupMenu()
    this.applyPopuFilter()
  },
  // 考试列表弹窗应用筛选请求
  async applyPopuFilter() {
    const apiParams = this.buildApiParams(this.data.examPopuSelectForTemplate)
    console.log("拿到的参数", apiParams)
    await this.getChildListForDetail(apiParams)
  },
  // 获取考试列表弹窗筛选缓存
  updateJobPopuSelectForTemplateFromCache() {
    const examPopuSelectForTemplate = getJobDetailPopuSelectForTemplateCache()
    console.log(examPopuSelectForTemplate)
    this.setData({
      examPopuSelectForTemplate,
    })
  },
  // 考试列表弹窗地区筛选确认弹窗
  handlePopuRegionSelection(e) {
    const { filterKey, tempSelected } = e.detail
    console.log(filterKey, tempSelected)
    this.setData({
      [`examPopuSelectForTemplate.${filterKey}`]: tempSelected,
      activeExamExpanded: "",
    })
    setJobDetailPopuSelectForTemplateCache(this.data.examPopuSelectForTemplate)
    this.applyPopuFilter()
    this.hidePopupMenu()
  },

  // 点击附件
  tapAttachment(e) {
    const val = e.currentTarget.dataset.item
    const url = val.url
    const name = val.name
    wx.showLoading({
      title: "",
      mask: true,
    })
    APP.openFile(url, { fileName: name })
      .then((res) => {
        console.log("文件打开成功", url)
        wx.hideLoading()
      })
      .catch((res) => {
        console.log(res)
        wx.hideLoading()
        // wx.navigateTo({
        //   url: `/pages/file-preview/index?filePath=${res.filePath}&fileName=${res.fileName}`,
        // })
      })
  },
  tapCustomerService(e) {
    const { cmd_json } = e.currentTarget.dataset.item
    console.log(e, cmd_json)
    APP.toCmdUnitKey(cmd_json)
  },
  // 复制链接
  copyUrl(e) {
    const link = e.currentTarget.dataset.item

    // 调用微信API复制链接到剪贴板
    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showToast({
          title: "复制成功",
          duration: 2000,
          icon: "none",
        })
      },
    })
  },
  // 关闭popu弹窗
  onClose() {
    this.setData({
      show: false,
      showExamRegionPopup: false,
    })
  },
  // 打开公告弹窗
  openSelectBoxPopup() {
    console.log("打开选择公告弹窗")

    // 如果弹窗已经打开，则关闭弹窗
    if (this.data.showSelectBoxPopup) {
      this.closeSelectBoxPopup()
      return
    }

    // 如果弹窗未打开，则打开弹窗
    this.calculateSelectBoxPopupPosition()
    this.setData({
      showSelectBoxPopup: true,
      showPopupFilterMenu: false,
    })
    // 禁用页面滚动
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    })
    this.setData({
      pageScrollDisabled: true,
    })
  },
  // 关闭公告弹窗
  closeSelectBoxPopup() {
    console.log("关闭选择公告弹窗")
    this.setData({
      showSelectBoxPopup: false,
      pageScrollDisabled: false, // 恢复页面滚动
    })
  },
  // 选择公告项
  handleSelectBoxItemClick(e) {
    const { id, title } = e.currentTarget.dataset
    console.log("选择公告项:", { id, title })

    // 更新选中状态
    const updatedList = this.data.selectBoxList.map((item) => ({
      ...item,
      selected: item.id === id,
    }))

    // 更新当前选中的标题
    this.setData({
      selectBoxList: updatedList,
      currentSelectBoxTitle: title,
      showSelectBoxPopup: false,
      pageScrollDisabled: false, // 恢复页面滚动
    })
  },
  // 计算公告弹窗筛选弹窗弹出位置
  calculateSelectBoxPopupPosition() {
    const query = this.createSelectorQuery()
    query
      .select(".select-box")
      .boundingClientRect((rect) => {
        if (rect) {
          // 计算弹窗应该出现的位置
          const selectBoxPopupTop = rect.bottom + 10 // 在 select-box 下方 10px 处
          this.setData({
            selectBoxPopupTop,
          })
        }
      })
      .exec()
  },
  // 计算考试列表弹窗筛选弹窗弹出位置
  calculateExamPopupMenuPosition() {
    setTimeout(() => {
      console.log("开始计算exam popup位置...")
      const query = this.createSelectorQuery()

      // 获取popu-box和popu-menu .top的位置信息
      query.select(".popu-box").boundingClientRect()
      query.select(".popu-box .popu-menu .top").boundingClientRect()

      query.exec((res) => {
        const popuBoxRect = res[0]
        const topRect = res[1]

        console.log("popu-box元素信息:", popuBoxRect)
        console.log("popu-menu内部top元素信息:", topRect)

        if (popuBoxRect && topRect) {
          // 获取屏幕高度，计算20vh的像素值
          const systemInfo = wx.getSystemInfoSync()
          const screenHeight = systemInfo.windowHeight
          const vh20 = (screenHeight * 20) / 100 // 20vh转换为像素

          // 计算top元素相对于popu-box的位置
          const topRelativeToPopuBox = topRect.top - popuBoxRect.top

          // 地区选择弹窗应该出现的位置：top元素在popu-box内的位置 + top元素高度 + 20vh + 10px间距
          const examRegionPopupTop =
            topRelativeToPopuBox + topRect.height + vh20 + 10

          console.log("位置计算信息:", {
            popuBoxTop: popuBoxRect.top,
            topElementTop: topRect.top,
            topRelativeToPopuBox: topRelativeToPopuBox,
            topHeight: topRect.height,
            screenHeight: screenHeight,
            vh20: vh20,
            calculatedTop: examRegionPopupTop,
          })

          this.setData({
            examRegionPopupTop,
          })
        } else {
          console.log("未找到.popu-box或.popu-box .popu-menu .top元素")
          console.log("popuBoxRect:", popuBoxRect)
          console.log("topRect:", topRect)

          // 降级方案：使用相对安全的默认值
          const systemInfo = wx.getSystemInfoSync()
          const screenHeight = systemInfo.windowHeight
          const vh20 = (screenHeight * 20) / 100
          const fallbackTop = 100 + vh20 + 10 // 估算的相对位置

          console.log("使用降级方案，设置默认位置:", fallbackTop)
          this.setData({
            examRegionPopupTop: fallbackTop,
          })
        }
      })
    }, 300) // 确保van-popup已完全显示并渲染
  },
  // 关闭考试列表popu
  closeExamRegionPopup() {
    this.setData({
      showExamRegionPopup: false,
      activeExamExpanded: "",
    })
  },

  // 考试列表全部职位点击
  goPosition(e) {
    this.setData({
      show: false,
    })
    const { id } = e.currentTarget.dataset.item
    console.log(id, PAGE_OPTIONS.id)
    if (id == PAGE_OPTIONS.id) {
      this.setActiveTab("position")
    } else {
      ROUTER.navigateTo({
        path: "/pages/notice/detail/index",
        query: {
          id,
        },
      })
    }
  },

  // 切换tab
  changeTab(e) {
    console.log(e.currentTarget.dataset.index, e, "-----------------")
    const { index, item } = e.currentTarget.dataset
    // 如果有弹窗打开，先关闭弹窗
    if (this.data.showPopupFilterMenu) {
      this.hidePopupMenuFilter()
    }
    this.setData({
      activeIndex: index,
    })
    if (this.data.indexToTabKey[this.data.activeIndex] == "position") {
      this.applyFilter()
    } else if (this.data.indexToTabKey[this.data.activeIndex] == "official") {
      this.getArticleNoticeList()
    }
  },
  async getArticleNoticeList() {
    let requestParams = {
      id: PAGE_OPTIONS.id,
    }
    const res = await UTIL.request(API.getArticleNoticeList, requestParams)
    if (res && res.error && res.error.code === 0 && res.data) {
      const resData = res.data
      this.setData({
        officialList: resData.list,
      })
      console.log(resData, "------------")
    }
  },

  changeCollect() {
    this.setFollows()
  },

  async setFollows() {
    const is_follow = this.data?.noticeData?.is_follow
    let params = {
      item_type: "article",
      item_no: [PAGE_OPTIONS.id],
      type: is_follow == 0 ? "follow" : "unfollow",
    }
    const res = await UTIL.request(API.setFollows, params)
    if (res.error.code === 0) {
      wx.showToast({
        title: is_follow == 0 ? "关注成功" : "已取消关注",
        icon: "none",
        duration: 2000,
      })
      this.setData({
        ["noticeData.is_follow"]: is_follow == 0 ? 1 : 0,
      })
    }
  },

  backPage() {
    if (getCurrentPages().length <= 1) {
      wx.reLaunch({
        url: "/pages/home/<USER>/index",
      })
      console.log("回首页")
      return false
    }
    console.log("触发返回")
    wx.navigateBack({
      delta: 1,
    })
  },

  onPageScroll(e) {
    // 如果页面滚动被禁用，则不处理滚动事件
    if (this.isPageScrollDisabled && this.isPageScrollDisabled()) {
      return
    }

    const scrollTop = e.scrollTop
    const { headerHeight } = this.data

    // 处理头部背景色变化
    if (scrollTop > 0) {
      //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色设置为白色
      if (!this.data.show_white) {
        this.setData({ show_white: true })
        // 设置胶囊按钮为黑色（适用于白色背景）
        wx.setNavigationBarColor({
          frontColor: "#000000",
          backgroundColor: "#ffffff",
        })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
        // 设置胶囊按钮为白色（适用于深色背景）
        wx.setNavigationBarColor({
          frontColor: "#ffffff",
          backgroundColor: "#000000",
        })
      }
    }

    // 检测菜单是否吸顶
    // 当滚动距离大于等于菜单到顶部的距离减去导航栏高度时，菜单开始吸顶
    const { menuOffsetTop } = this.data
    const isMenuSticky =
      menuOffsetTop > 0 && scrollTop >= menuOffsetTop - headerHeight

    if (isMenuSticky !== this.data.menuSticky) {
      this.setData({
        menuSticky: isMenuSticky,
      })
    }
  },

  onReachBottom() {
    console.log("触底了")
    if (this.data.pageType == "collection") {
      if (this.data.hasMore && !this.data.isLoading) {
        // 页码+1
        const nextPage = this.data.page + 1
        this.setData({
          page: nextPage,
        })

        // 加载下一页数据
        const apiParams = this.buildApiParams(
          this.data.collectionSelectForTemplate
        )
        this.getArticleChildList(apiParams, true)
      }
    } else {
      if (this.data.indexToTabKey[this.data.activeIndex] == "position") {
        // 检查是否还有更多数据且当前不在加载中
        if (this.data.hasMore && !this.data.isLoading) {
          // 页码+1
          const nextPage = this.data.page + 1
          this.setData({
            page: nextPage,
          })

          // 加载下一页数据
          const apiParams = this.buildApiParams(
            this.data.jobDetailSelectForTemplate
          )
          this.getJobList(apiParams, true)
        }
      }
    }
  },
  onShareAppMessage() {},
})

Page(pageConfig)
