apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${DEPLOYMENT_NAME}
  namespace: ${NAMESPACE}
  labels:
    app: ${DEPLOYMENT_NAME}
    serviceType: ${SERVICE_TYPE}
    version: stable
spec:
  replicas: ${REPLICAS}
  selector:
    matchLabels:
      app: ${DEPLOYMENT_NAME}
      serviceType: ${SERVICE_TYPE}
      version: stable
  template:
    metadata:
      labels:
        app: ${DEPLOYMENT_NAME}
        serviceType: ${SERVICE_TYPE}
        version: stable
    spec:
      containers:
      - name: ${DEPLOYMENT_NAME}
        image: ccr.ccs.tencentyun.com/tiku/${DEPLOYMENT_NAME}:${CI_COMMIT_SHORT_SHA}
        imagePullPolicy: IfNotPresent
        env:
          - name: APP_ENV
            value: ${APP_ENV}
        ports:
        - containerPort: ${SERVICE_PORT}
        resources:
          requests:
            cpu: ${REQUEST_CPU}
            memory: ${REQUEST_MEM}
          limits:
            cpu: ${LIMIT_CPU}
            memory: ${LIMIT_MEM}
        livenessProbe:
          tcpSocket:
            port: ${SERVICE_PORT}
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
        readinessProbe:
          tcpSocket:
            port: ${SERVICE_PORT}
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3