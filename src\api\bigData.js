import request from "@/utils/request"

// 基础配置相关
export function getConfigRequest(data = {}) {
  return request({
    url: "/retrievemp/common/get_config",
    method: "post",
    data: data,
  })
}

// 地区数据相关
export function getRegionChildListRequest(data = {}) {
  return request({
    url: "/retrievemp/common/get_region_child_list",
    method: "post",
    data: data,
  })
}

export function getRegionTreeRequest(data = {}) {
  return request({
    url: "/retrievemp/common/get_region_tree",
    method: "post",
    data: data,
  })
}

export function getRegionListByArticleIdRequest(data = {}) {
  return request({
    url: "/retrievemp/common/get_region_list_by_article_id",
    method: "post",
    data: data,
  })
}

// 项目和文章相关
export function getProjectListRequest(data = {}) {
  return request({
    url: "/retrievemp/common/get_project_list",
    method: "post",
    data: data,
  })
}

export function getArticleListRequest(data = {}) {
  return request({
    url: "/retrievemp/common/get_article_list",
    method: "post",
    data: data,
  })
}

// 用人单位相关
export function getWorkUnitListRequest(data = {}) {
  return request({
    url: "/retrievemp/common/get_work_unit_list",
    method: "post",
    data: data,
  })
}

// 报名大数据相关
export function getApplyDetailRequest(data = {}) {
  return request({
    url: "/retrievemp/apply/detail",
    method: "post",
    data: data,
  })
}

export function getStatisticsListRequest(data = {}) {
  return request({
    url: "/retrievemp/apply/get_statistics_list",
    method: "post",
    data: data,
  })
}

export function getJobCompetitiveRateListRequest(data = {}) {
  return request({
    url: "/retrievemp/apply/get_job_competitive_rate_list",
    method: "post",
    data: data,
  })
}

// 进面分数线相关
export function getEnterDetailRequest(data = {}) {
  return request({
    url: "/retrievemp/enter/detail",
    method: "post",
    data: data,
  })
}

// 面试分数页面专用接口
export function getEnterDetailForInterviewRequest(data = {}) {
  return request({
    url: "/retrievemp/enter/detail",
    method: "post",
    data: data,
  })
}

export function getJobEntryListForInterviewRequest(data = {}) {
  return request({
    url: "/retrievemp/enter/get_job_entry_list",
    method: "post",
    data: data,
  })
}

export function setFocus(data = {}) {
  return request({
    url: "/retrievemp/user/follows",
    method: "post",
    data: data,
  })
}

export function getJobRankList(data = {}) {
  return request({
    url: "/retrievemp/enter/get_job_rank_list",
    method: "post",
    data: data,
  })
}

export function getOverViewRequest(data = {}) {
  return request({
    url: "/retrievemp/apply/overview",
    method: "post",
    data: data,
  })
}

export function getInterViewRequest(data = {}) {
  return request({
    url: "/retrievemp/enter/overview",
    method: "post",
    data: data,
  })
}
