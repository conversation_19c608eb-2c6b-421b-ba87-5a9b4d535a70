.card-type-advclass {
  position: relative;
  width: 100%;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-sizing: border-box;
  box-shadow: 0rpx 2rpx 6rpx 2rpx rgba(0, 0, 0, 0.04);
  .desk-list {
    padding-top: 60rpx;
    display: flex;
    flex-wrap: wrap;
    padding-left: 100rpx;
    height: 170rpx;
    box-sizing: border-box;
  }
  .advclass-status {
    position: absolute;
    background: rgba(214, 40, 40, 0.6);
    padding: 6rpx 12rpx;
    border-radius: 16rpx 0rpx 16rpx 0rpx;
    font-size: 24rpx;
    color: #fff;
    display: flex;
    align-items: center;
    .image {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 8rpx;
    }
  }
  .fonts {
    background: linear-gradient(to bottom, #ffffff 0%, #fff9ee 100%);
    box-shadow: 0rpx 2rpx 20rpx 2rpx rgba(245, 138, 74, 0.06);
    padding: 16rpx;
    .flex-1 {
      flex: 1;
      min-width: 0;
      padding-right: 30rpx;
    }

    border-radius: 16rpx;
    display: flex;
    align-items: center;
    .title {
      font-size: 28rpx;
      font-weight: 500;
      flex: 1;
      min-width: 0;
      padding-right: 30rpx;
      color: #2c2725;
    }
    .image {
      width: 10rpx;
      height: 18rpx;
    }
  }
  .fonts-box {
    padding: 0 20rpx;
    padding-bottom: 20rpx;
  }
  &.big {
    min-height: 370rpx;
    .fonts {
      padding: 30rpx;
    }
    .desk-list {
      height: auto !important;
      padding-top: 80rpx;
    }
    .fonts-box {
      margin-top: 20rpx;
    }
  }
  &.min {
    min-height: 254rpx;
  }
}

.student-label {
  font-size: 20rpx;
  color: #ff6a4d;
  border: 0.5rpx solid rgba(255, 106, 77, 0.3);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}

.intro-box {
  margin-top: 10rpx;
  display: flex;
  .texts {
    font-size: 24rpx;
    color: #706c6a;
    flex: 1;
  }
}

.right-btn-s {
  display: flex;
  align-items: center;

  .text {
    font-size: 26rpx;
    color: #d62828;
    margin-right: 4rpx;
  }
}
