page {
  background: #f7f8fa;
}
.comparison-list {
  min-height: 100vh;
  position: relative;
  padding: 32rpx 32rpx 0 32rpx;
  box-sizing: border-box;
  .top-card {
    width: 100%;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 16rpx;
    .icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
    .text {
      font-size: 26rpx;
      color: #3c3d42;
    }
  }
  .card-area {
    margin-top: 32rpx;
  }
  .bottom-area {
    width: 100%;
    box-sizing: border-box;
    background: #ffffff;
    padding: 14rpx 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      display: flex;
      align-items: center;
      .icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
      }
      .text {
        font-size: 26rpx;
        color: #666666;
      }
    }
    .btn {
      width: 400rpx;
      height: 84rpx;
      background: #ec3e33;
      border-radius: 16rpx;
      opacity: 0.5;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 500;
      transition: opacity 0.3s ease;

      &.active {
        opacity: 1;
      }
    }
  }
}
