import axios from "axios"
import {
  getAppTokenCookie,
  getDeviceAppBrandCookie,
  getDeviceAppVersionCookie,
  getRequestAppTypeCookie, isDeviceAppClientCookie,
} from "@/utils/cookies/modules/appDevice"

// import { getUserTokenCookie } from "./cookies/modules/user"
// import { useUserInfoStore } from "@/stores/useUserStore"
const baseURL = process.env.NODE_ENV === "development" ? "/proxy/wap/api/" : "/wap/api/"
const service = axios.create({
  baseURL: baseURL, // url = base url + request url
  timeout: 60000 * 5,
})
// 记录请求前的token
// const beforeToken = getUserTokenCookie()

// request interceptor
service.interceptors.request.use(
  (config) => {
    // 如果cookie内存有当前缓存类型字段，则更改请求header中app的值
    const headerApp = getRequestAppTypeCookie() || getDeviceAppVersionCookie() || ""
    if (headerApp) {
      config.headers["app"] = headerApp
    }

    // app内打开的品牌
    if (getDeviceAppBrandCookie()) {
      config.headers["brand"] = getDeviceAppBrandCookie()
    }

    if(getAppTokenCookie()) {
      const token = getAppTokenCookie()
      config.headers.Authorization = "Bearer " + token
    }
    if (isDeviceAppClientCookie()) {
      config.headers["app"] = "app"
    }
    const province = new URLSearchParams(window.location.search).get("province")
    if(province) {
      config.headers["province"] = province
    }

    // const token =
    //   sessionStorage.getItem("token") ||
    //   "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1Y2lkIjoxNzYxMTE2LCJicmFuZCI6ImNhbXBzIiwiYXBwbGljYXRpb24iOiJ3ZWIiLCJ0ZXN0ZXIiOjEsImlzX3RvdXJpc3QiOjAsImlhdCI6MTcxOTMxMTIzOSwiaXNzIjoidWNlbnRlciJ9.8odV88E6Z2pJx8Dn7MKEa9wzkv45w_decQLUTTOzpsw"
    // const userInfoStore = useUserInfoStore()
    // const token = getUserTokenCookie()
    // console.log(beforeToken, token)

    // token不一致时刷新页面
    // if (beforeToken && beforeToken !== token) {
    //   window.location.href = window.location.href
    // }

    // if (token) {
    //   config.headers.Authorization = "Bearer " + token
    // }
    // config.headers.brand = "skb"
    // config.headers.province = "chongqing"

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  (response) => {
    const res = response.data
    // if (res.error?.code !== 0) {
    //   // message.error(res.msg || "接口返回Error")
    //   return Promise.reject(res)
    // } else {
    //   if (res?.data?.token) {
    //     sessionStorage.setItem("token", res.data.token)
    //   }
    // }
    return res
  },
  (error) => {
    console.error(error)
    if (error.response) {
      // if (error.response.status === 401) {
      //   sessionStorage.removeItem("token")
      //   location.reload()
      // }
      // message.error(error.message || error.response.data || "接口返回Error")
      return Promise.reject(error.response.data)
    }
  }
)

export default service
