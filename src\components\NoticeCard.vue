<template>
  <div class="notice-list">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="notice-list-item"
      :class="{ over: item.is_over }"
      @click="goDetail(item)"
    >
      <div class="title">{{ item.title }}</div>
      <div class="bottom">
        <div class="flex-v">
          <template v-if="item.apply_status?.text">
            <span class="status blue" :style="{ color: item.apply_status.color }">
              {{ item.apply_status.text }}
            </span>
            <div v-if="item.need_num || item.job_num" class="line">｜</div>
          </template>
          <div v-if="item.need_num" class="item-text mr16">
            共招
            <span class="num">{{ item.need_num }}</span>
            人
          </div>
          <div v-if="item.job_num" class="item-text">
            <span class="num">{{ item.job_num }}</span>
            个职位
          </div>
        </div>
        <div v-if="item.suit_job_num" class="item-text">
          适合职位
          <span class="num">{{ item.suit_job_num }}</span>
          个
        </div>
        <div v-else class="time">{{ item.release_time }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue"

// 定义props
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})

// 定义emits
const emit = defineEmits(["goDetail"])

// 跳转详情
const goDetail = (item) => {
  emit("goDetail", item)
  console.log("跳转详情:", item)
}
</script>

<style lang="scss" scoped>
.notice-list {
  &-item {
    background: #ffffff;
    padding: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
    border-radius: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
    margin-bottom: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
    position: relative;
    cursor: pointer;

    &.over {
      .title {
        color: rgba(145, 148, 153, 1);
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    .title {
      font-size: 0.427rem; // 32rpx × 0.5 = 16px, 16px ÷ 37.5 = 0.427rem
      color: rgba(34, 36, 46, 1);
      line-height: 0.64rem; // 48rpx × 0.5 = 24px, 24px ÷ 37.5 = 0.64rem
      font-weight: bold;
    }

    .bottom {
      display: flex;
      align-items: center;
      margin-top: 0.533rem; // 40rpx × 0.5 = 20px, 20px ÷ 37.5 = 0.533rem

      .status {
        font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        color: rgba(19, 191, 128, 0.8);

        &.blue {
          color: rgba(68, 138, 255, 0.8);
        }

        &.over {
          color: rgba(255, 106, 77, 0.8);
        }

        &.end {
          color: rgba(145, 148, 153, 1);
        }
      }

      .line {
        font-size: 0.267rem; // 20rpx × 0.5 = 10px, 10px ÷ 37.5 = 0.267rem
        color: rgba(194, 197, 204, 1);
        margin: 0 0.107rem; // 8rpx × 0.5 = 4px, 4px ÷ 37.5 = 0.107rem
      }

      .item-text {
        font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
        color: rgba(145, 148, 153, 1);

        .num {
          color: rgba(60, 61, 66, 1);
          margin: 0 0.05rem;
        }
      }

      .mr16 {
        margin-right: 0.213rem; // 16rpx × 0.5 = 8px, 8px ÷ 37.5 = 0.213rem
      }

      .flex-v {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
      }
    }

    .time {
      font-size: 0.32rem; // 24rpx × 0.5 = 12px, 12px ÷ 37.5 = 0.32rem
      color: rgba(194, 197, 204, 1);
    }
  }
}
</style>
