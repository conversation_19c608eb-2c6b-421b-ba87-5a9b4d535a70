// 品牌 Schema 配置
export const BRAND_SCHEMAS = {
  skb: {
    protocal: "skb",
    failUrl: "cn.net.tiku.shikaobang.syn",
  },
  jbcgk: {
    protocal: "jbcgk",
    failUrl: "cn.net.tiku.jbcgk.syn",
  },
  gpjs: {
    protocal: "gpjs",
    failUrl: "cn.net.tiku.gpjiaoshi.syn",
  },
  xuandiao: {
    protocal: "xuandiao",
    failUrl: "cn.net.tiku.gongkao.xuandiao",
  },
  jbczsb: {
    protocal: "jbczsb",
    failUrl: "cn.tiku.android.zsb",
  },
  jbcyk: {
    protocal: "jbcyk",
    failUrl: "com.tiku.android.yian",
  },
  jbcshangan: {
    protocal: "jbcshangan",
    failUrl: "com.tiku.android.shangan",
  },
}

export default BRAND_SCHEMAS
