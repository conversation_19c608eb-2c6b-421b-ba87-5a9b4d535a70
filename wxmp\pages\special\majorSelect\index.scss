.major-select {
  height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  .back-btn {
    position: absolute;
    top: 112rpx;
    left: 40rpx;
    width: 40rpx;
    height: 40rpx;
    z-index: 1000; // 提高层级，确保在所有内容之上
    pointer-events: auto; // 确保可以接收点击事件
  }
  .top-bg {
    width: 100%;
    height: 520rpx;
    position: relative;
    z-index: -1;
    padding: 224rpx 150rpx 0 40rpx;
    box-sizing: border-box;
    .top-title {
      font-weight: bold;
      font-size: 40rpx;
      color: #ffffff;
      line-height: 36rpx;
      margin-bottom: 32rpx;
    }
    .sub-title {
      color: #ffffff;
      font-size: 28rpx;
      line-height: 36rpx;
      opacity: 0.8;
    }
    .bg-img {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
    }
  }
  .main-content {
    width: 100%;
    margin-top: -100rpx;
    background: #ffffff;
    flex: 1;
    border-radius: 24rpx 24rpx 0 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    // Tab导航区域样式
    .tab-container {
      position: relative;
      width: 100%;
      background: #ffffff;

      .tab-scroll {
        width: 100%;
        white-space: nowrap;

        .tab-list {
          display: inline-flex;
          align-items: center;

          .tab-item {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 34rpx 40rpx 22rpx 40rpx;
            font-size: 28rpx;
            color: #666666;
            position: relative;
            white-space: nowrap;
            flex-shrink: 0;

            &.active {
              color: #22242e;
              font-weight: bold;
              font-size: 32rpx;

              &::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 40rpx;
                height: 6rpx;
                background: #e60003;
              }
            }
          }
        }
      }
    }
    .major-selector-container {
      flex: 1;
      min-height: 0;
    }
  }
}
