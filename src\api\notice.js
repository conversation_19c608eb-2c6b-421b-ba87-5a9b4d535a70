import request from "@/utils/request"

/**
 * 获取公告详情
 * @param {Object} data 请求参数
 */
export function getArticleDetailRequest(data) {
  return request({
    url: `retrievemp/article/detail`,
    method: "post",
    data: data,
  })
}

/**
 * 获取合辑列表
 * @param {Object} data 请求参数
 */
export function getArticleChildListRequest(data) {
  return request({
    url: `retrievemp/article/child-list`,
    method: "post",
    data: data,
  })
}

/**
 * 获取考试动态详情
 * @param {Object} data 请求参数
 */
export function getJobDetail(data) {
  return request({
    url: `retrievemp/article/note-detail`,
    method: "post",
    data: data,
  })
}
