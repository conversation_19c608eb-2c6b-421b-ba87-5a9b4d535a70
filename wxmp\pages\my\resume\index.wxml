<view class="resume">
  <view class="top-card">
    <view class="title-area">
      <view class="text">完善你的简历信息</view>
      <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_card_sign.png" mode="" />
    </view>
    <view class="checked-item">
      <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_checked.png" mode="" />
      <view class="text">优势考试优先推送</view>
    </view>
    <view class="checked-item mt16">
      <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/my/resume_checked.png" mode="" />
      <view class="text">适配岗位精准推荐</view>
    </view>
    <view class="circle-box">
      <van-circle color="#FF6A4D" layer-color="#FFE9E4" line-cap="butt" value="{{detailData.complete_progress.progress}}" type="2d" stroke-width="18" size="106" />
      <view class="circle-text">
        <text class="num">{{detailData.complete_progress.progress}}</text>%
      </view>
    </view>
  </view>
  <!-- 新增：全局待完善项数 -->
  <view class="all-incomplete-tips" wx:if="{{allIncompleteCount > 0}}">{{allIncompleteCount}}项待完善</view>
  <base-info-card id="base-info-card" baseInfo="{{ detailData.info }}" bind:dataChange="onBaseInfoDataChange"></base-info-card>
  <education-card id="education-card" bind:dataChange="onEducationDataChange" educationData="{{educationList}}" isGraduate="{{ detailData.info.fresh_graduate.value}}"></education-card>
  <work-info-card id="work-info-card" workInfo="{{detailData.info}}" bind:dataChange="onWorkInfoDataChange"></work-info-card>
  <view class="bottom-text">
    用户简历信息仅用于产品功能使用，请放心填写，点击查看<text class="blue-text">《隐私条例》</text>
  </view>

  <tabbar-box>
    <view class="bottom-btn">
      <view class="btn {{ isFormComplete ? 'active' : '' }}" bindtap="onAddJobs">保存</view>
    </view>
  </tabbar-box>
</view>