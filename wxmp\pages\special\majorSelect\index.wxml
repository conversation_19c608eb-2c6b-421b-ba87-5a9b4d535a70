<view class="major-select">
  <image class="back-btn" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data-top_back.png" mode="" catch:tap="onBackClick" />
  <view class="top-bg">
    <image class="bg-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/major_bg.png" mode="" />
    <view class="top-title">国家教育部专业指导目录</view>
    <view class="sub-title">该目录来源于国家教育部专业目录，由金标尺整理发布，仅供参考。</view>
  </view>

  <view class="main-content">
    <!-- Tab导航区域 -->
    <view class="tab-container">
      <scroll-view class="tab-scroll" scroll-x="true" scroll-with-animation="true" scroll-left="{{scrollLeft}}" show-scrollbar="{{false}}" enhanced="{{true}}">
        <view class="tab-list">
          <view wx:for="{{tabList}}" wx:key="index" class="tab-item {{currentTab == item.education_id ? 'active' : ''}}" data-data="{{item}}" bindtap="onTabClick" id="tab-{{index}}">
            {{item.name}}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 专业选择器内容区域 -->
    <view class="major-selector-container">
      <major-selector-inline education-id="{{currentTab}}" bind:confirm="onMajorConfirm" />
    </view>
  </view>
</view>