<navigation-status-bar></navigation-status-bar>
<view class="container top-header-nav">
  <view class="header-item-left">
    <view catch:tap="tapButton">
      <view class="exam-name" bind:tap="toChangeExam"><text>{{cacheStting.examDirection.name}} ({{cacheStting.province.name}})</text>
        <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/common/header_exam_down_arrow.png" mode="widthFix" class="icon" />
      </view>
    </view>
  </view>
  <view class="header-item-right">
    <slot></slot>
  </view>
</view>
<exam-popup show="{{show}}" id="examPopup" bind:examClose="closeExam" bind:toSchool="toSchool" bind:tapSubtitle="tapSubtitle"></exam-popup>
<province-popup show="{{provinceShow}}" bind:closeProvince="closeProvince"></province-popup>
<campus-popup show="{{campusShow}}" bind:closeCampus="closeCampus"></campus-popup>