/**
 * 筛选选项相关的 WXS 函数
 * 数据结构：{ filter_key: Array<value> } 或 { filter_key: { nested_key: Array<value> } }
 */

/**
 * 判断筛选选项是否被选中
 * @param {string} filterKey 菜单的filter_key
 * @param {string} optionValue 选项的value
 * @param {Object} selectObject 选中状态对象 (noticeSelectForTemplate)
 * @returns {boolean} 是否选中
 */
function isFilterOptionSelected(filterKey, optionValue, selectObject) {
  if (!selectObject || !selectObject[filterKey]) {
    return false
  }

  var selectedValues = selectObject[filterKey]

  // WXS 中处理数组格式的数据
  if (selectedValues && selectedValues.length !== undefined) {
    for (var i = 0; i < selectedValues.length; i++) {
      if (selectedValues[i] == optionValue) {
        return true
      }
    }
  }

  return false
}

function isFilterItemOptionSelected(optionValue, selectedValues) {
  // WXS 中处理数组格式的数据
  if (selectedValues && selectedValues.length !== undefined) {
    for (var i = 0; i < selectedValues.length; i++) {
      if (selectedValues[i] == optionValue) {
        return true
      }
    }
  }

  return false
}

function isSelectOptionSelected(optionValue, selectedValues) {
  // WXS 中处理数组格式的数据
  if (selectedValues && selectedValues.length !== undefined) {
    for (var i = 0; i < selectedValues.length; i++) {
      if (selectedValues[i].id == optionValue) {
        return true
      }
    }
  }

  return false
}

/**
 * 判断菜单项是否被选中（基于 noticeSelectForTemplate）
 * @param {string} filterKey 菜单的filter_key
 * @param {Object} selectObject 选中状态对象 (noticeSelectForTemplate)
 * @returns {boolean} 是否选中
 */
function isMenuItemSelected(filterKey, selectObject) {
  if (!selectObject || !filterKey) {
    return false
  }

  if (filterKey === "apply_time") {
    return false
  }

  // 根据不同的 filter_key 判断是否有选中值
  if (filterKey === "fit_me" || filterKey === "has_tenure") {
    // 单选类型：检查数组是否有值
    return selectObject[filterKey] && selectObject[filterKey].length > 0
  } else if (filterKey === "apply_region") {
    // 地区类型：检查 apply_region 数组是否有值
    return selectObject.apply_region && selectObject.apply_region.length > 0
  } else if (filterKey === "region") {
    // 考试动态地区类型：检查 region 数组是否有值
    return selectObject.region && selectObject.region.length > 0
  } else if (filterKey === "exam_type") {
    // 考试类型：检查 exam_type 数组是否有值
    return selectObject.exam_type && selectObject.exam_type.length > 0
  } else if (filterKey === "filter_list") {
    if (!selectObject || !selectObject.filter_list) {
      return false
    }

    var filterList = selectObject.filter_list

    // 手动列出你关心的字段（根据你的业务逻辑修改）
    var keys = [
      "education", // 地区
      "major", // 考试类型
      "politics_face", // 招聘人数
      "fresh_graduate", // 报名状态
      "need_num",
      "apply_status",
    ]

    for (var i = 0; i < keys.length; i++) {
      var key = keys[i]
      var value = filterList[key]

      if (value && value.length > 0) {
        return true
      }
    }

    return false
  } else {
    // 其他类型：检查对应字段是否有值
    return selectObject[filterKey] && selectObject[filterKey].length > 0
  }
}
var parseRegion = function (str) {
  if (typeof str !== "string" || str === "") {
    return []
  }

  var arr = str.split("-")
  var result = []

  for (var i = 0; i < arr.length; i++) {
    var num = parseInt(arr[i], 10)
    if (!isNaN(num)) {
      result.push(num)
    }
  }

  return result
}

var includesKey = function (arr, key) {
  if (arr.length == 0) {
    return false
  }

  for (var i = 0; i < arr.length; i++) {
    if (arr[i].key === key) {
      return true
    }
  }

  return false
}

var countMatchingByLevel = function (list, key, level) {
  var targetId = parseInt(key, 10)
  if (isNaN(targetId)) {
    return 0
  }

  var count = 0

  for (var i = 0; i < list.length; i++) {
    var item = list[i]
    if (!item || !item.key) continue

    var parts = item.key.split("-")
    var itemId = parseInt(parts[level - 1], 10)

    if (!isNaN(itemId) && itemId === targetId) {
      count++
    }
  }
  if (count > 0) {
    return count
  }
}

module.exports = {
  isFilterOptionSelected: isFilterOptionSelected,
  isMenuItemSelected: isMenuItemSelected,
  isFilterItemOptionSelected: isFilterItemOptionSelected,
  parseRegion: parseRegion,
  includesKey: includesKey,
  countMatchingByLevel: countMatchingByLevel,
  isSelectOptionSelected: isSelectOptionSelected,
}
