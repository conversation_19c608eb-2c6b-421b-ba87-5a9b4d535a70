// ===== 移动端适配混入 =====

// 1px边框解决方案（解决移动端1px边框问题）
@mixin border-1px($color: $border-base, $position: bottom) {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    background-color: $color;
    
    @if $position == top {
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      transform: scaleY(0.5);
    } @else if $position == bottom {
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      transform: scaleY(0.5);
    } @else if $position == left {
      top: 0;
      left: 0;
      bottom: 0;
      width: 1px;
      transform: scaleX(0.5);
    } @else if $position == right {
      top: 0;
      right: 0;
      bottom: 0;
      width: 1px;
      transform: scaleX(0.5);
    } @else if $position == all {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 1px solid $color;
      transform: scale(0.5);
      transform-origin: 0 0;
    }
  }
}

// 文本省略
@mixin ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lines;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 居中对齐
@mixin center($type: both) {
  position: absolute;
  
  @if $type == both {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  } @else if $type == horizontal {
    left: 50%;
    transform: translateX(-50%);
  } @else if $type == vertical {
    top: 50%;
    transform: translateY(-50%);
  }
}

// Flex布局快捷方式
@mixin flex($direction: row, $justify: center, $align: center, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == phone {
    @media (max-width: 767px) { @content; }
  }
  @if $breakpoint == tablet {
    @media (min-width: 768px) and (max-width: 1023px) { @content; }
  }
  @if $breakpoint == desktop {
    @media (min-width: 1024px) { @content; }
  }
}

// 按钮样式混入
@mixin button-variant($bg-color, $text-color: #fff, $border-color: $bg-color) {
  background-color: $bg-color;
  color: $text-color;
  border: 1px solid $border-color;
  
  &:hover {
    background-color: lighten($bg-color, 10%);
    border-color: lighten($border-color, 10%);
  }
  
  &:active {
    background-color: darken($bg-color, 5%);
    border-color: darken($border-color, 5%);
  }
  
  &:disabled {
    background-color: lighten($bg-color, 30%);
    border-color: lighten($border-color, 30%);
    cursor: not-allowed;
  }
}

// 卡片样式
@mixin card($padding: $spacing-md, $radius: $border-radius-lg) {
  background-color: $bg-primary;
  border-radius: $radius;
  padding: $padding;
  box-shadow: $box-shadow-light;
}

// 安全区域适配（适配iPhone X等刘海屏）
@mixin safe-area-insets($property: padding, $position: all) {
  @if $position == all {
    #{$property}-top: constant(safe-area-inset-top);
    #{$property}-right: constant(safe-area-inset-right);
    #{$property}-bottom: constant(safe-area-inset-bottom);
    #{$property}-left: constant(safe-area-inset-left);
    
    #{$property}-top: env(safe-area-inset-top);
    #{$property}-right: env(safe-area-inset-right);
    #{$property}-bottom: env(safe-area-inset-bottom);
    #{$property}-left: env(safe-area-inset-left);
  } @else {
    #{$property}-#{$position}: constant(safe-area-inset-#{$position});
    #{$property}-#{$position}: env(safe-area-inset-#{$position});
  }
}
