FROM node:20.13-alpine as build-stage
RUN mkdir /src
WORKDIR /src
COPY . .
RUN node -v
RUN npm config set registry https://registry.npmmirror.com
RUN npm install --global pnpm && pnpm install && pnpm run build


FROM ccr.ccs.tencentyun.com/tiku/nginx
ARG uri_prefix
COPY --from=build-stage /src/dist/ /usr/share/nginx/html/
RUN sed -i "9a\try_files \$uri \$uri/ ${uri_prefix}/index.html;" /etc/nginx/conf.d/default.conf


