// 判断是否是在 App 内打开的品牌标识，一般只有 App 才会设置这个字段
import { getCookie, setCookie } from "../core"

// 定义 Cookie 中用于标识 App 品牌的字段名
const APP_BRAND = "App-Brand"

/**
 * 获取当前设备中存储的 App 品牌标识（App-Brand）
 * @returns {string|undefined} 返回 App 品牌标识的值，如果没有设置则返回 undefined
 */
export function getDeviceAppBrandCookie() {
  return getCookie(APP_BRAND)
}

// 定义 Cookie 中用于标识设备类型（App 客户端）的字段名
const APP_CLIENT = "App-Client"

/**
 * 获取当前设备中存储的客户端类型（App-Client）
 * 通常用于判断当前页面是否在 App 内打开
 * @returns {string|undefined} 返回客户端类型，如 "app" 或 "pad"，如果没有设置则返回 undefined
 */
export function getDeviceAppClientCookie() {
  return getCookie(APP_CLIENT)
}

// 定义 Cookie 中用于标识 App 版本号的字段名
const APP_VERSION = "App-Version"

/**
 * 获取当前设备中存储的 App 版本号
 * @returns {string|undefined} 返回版本号字符串，如果没有设置则返回 undefined
 */
export function getDeviceAppVersionCookie() {
  return getCookie(APP_VERSION)
}

// 定义 Cookie 中用于存储用户身份 Token 的字段名
const TokenKey = "App-Token"

/**
 * 获取当前设备中存储的用户身份 Token
 * @returns {string|undefined} 返回 Token 字符串，如果没有设置则返回 undefined
 */
export function getAppTokenCookie() {
  return getCookie(TokenKey)
}

/**
 * 设置用户的 Token 到 Cookie 中
 * @param {string} token - 要设置的 Token 字符串
 * @returns {void}
 */
export function setAppTokenCookie(token) {
  return setCookie(TokenKey, token)
}

// 定义 Cookie 中用于标识请求来源类型的字段名
const REQUEST_APP_TYPE = "RequestAppType"

/**
 * 获取当前请求的 App 类型标识（RequestAppType）
 * 用于后端识别请求来源或做不同处理
 * @returns {string|undefined} 返回请求类型字符串，如果没有设置则返回 undefined
 */
export function getRequestAppTypeCookie() {
  return getCookie(REQUEST_APP_TYPE)
}

/**
 * 判断当前是否是在 App 或 Pad 客户端内打开的页面
 * @returns {boolean} 如果是 App 或 Pad 客户端则返回 true，否则返回 false
 */
export function isDeviceAppClientCookie() {
  return getDeviceAppClientCookie() === "app" || getDeviceAppClientCookie() === "pad"
}
