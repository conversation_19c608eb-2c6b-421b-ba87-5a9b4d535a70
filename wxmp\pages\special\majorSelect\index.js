const { getMajorList } = require("@/config/api")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    tabList: [
      { name: "普通专科", education_id: "4" },
      { name: "普通本科", education_id: "6" },
      { name: "硕士研究生", education_id: "8" },
      { name: "博士研究生", education_id: "10" },
    ],
    currentTab: 6,
    scrollLeft: 0,
    selectedMajor: null, // 选择的专业信息
    selectedMajorText: "", // 选择的专业文本
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 组件会自动初始化数据，这里不需要手动调用
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  /**
   * 返回按钮点击
   */
  onBackClick() {
    wx.navigateBack()
  },

  /**
   * Tab点击事件
   */
  onTabClick(e) {
    const item = e.currentTarget.dataset.data
    this.setData({
      currentTab: item.education_id,
    })
  },

  /**
   * 专业选择确认
   */
  onMajorConfirm(e) {
    const { major, text } = e.detail
    console.log("选择的专业:", major, text)

    this.setData({
      selectedMajor: major,
      selectedMajorText: text,
    })

    // 显示选择结果提示
    wx.showToast({
      title: `已选择: ${text}`,
      icon: "none",
      duration: 2000,
    })

    // 可以在这里添加其他逻辑，比如保存到服务器或跳转到其他页面
  },
})
