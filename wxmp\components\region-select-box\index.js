const UTIL = require("@/utils/util")
const API = require("@/config/api")
const {
  formatProvinceData,
  formatCityData,
  formatDistrictData,
  extractRegionParams,
} = require("@/utils/regionUtils")
const RegionSelectUtils = require("@/services/regionSelectUtils")
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 已选择的地区列表
    selectedRegions: {
      type: Array,
      value: [],
    },

    // 是否在弹窗中使用（影响高度和定位）
    inPopup: {
      type: Boolean,
      value: false,
    },
    // 弹窗中的高度限制
    popupHeight: {
      type: String,
      value: "80vh",
    },
    // 控制颜色
    isShowBg: {
      type: Boolean,
      value: true,
    },
    noPadding: {
      type: Boolean,
      value: false,
    },
    show: {
      type: Boolean,
      value: false,
    },
    filterKey: {
      type: String,
      value: "",
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 省份列表
    provinceList: [],
    // 城市列表
    cityList: [],
    // 区县列表
    districtList: [],

    // 当前选中的省份
    currentProvince: {},
    // 当前选中的城市
    currentCity: {},

    // activeList: { province: [], city: [], district: [] },

    tempSelected: [],

    currentDistrict: [],

    // 加载状态
    loading: {
      province: false,
      city: false,
      district: false,
    },
  },
  observers: {
    show: function (newVal) {
      this.setData({
        cityList: [],
        districtList: [],
        currentProvince: {},
        currentCity: {},
        currentDistrict: [],
        tempSelected: this.data.selectedRegions,
      })
    },
  },
  lifetimes: {
    attached() {
      console.log("进来")
      this.loadNewsProvinceList()
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    handleProvinceClick(e) {
      const { index } = e.currentTarget.dataset
      const province = this.data.provinceList[index]

      // 判断是否是当前已选中的省份（根据 id 判断）
      if (
        this.data.currentProvince &&
        this.data.currentProvince.id === province.id
      ) {
        // 已选中的是同一个省份，不需要处理
        return
      }

      // 否则更新 currentProvince，并清空 districtList
      this.setData({
        currentProvince: province,
        districtList: [], // 只有切换省份时才清空
      })

      this.loadNewsCityList(province)
    },
    /**
     * 处理城市点击事件
     */
    handleCityClick(e) {
      const { index } = e.currentTarget.dataset
      const city = this.data.cityList[index]
      console.log("xianzai", this.data.tempSelected)

      this.setData({ currentCity: city })
      this.loadNewsDistrictList(city)
    },

    /**
     * 处理区县点击事件
     */
    handleDistrictClick(e) {
      const { index } = e.currentTarget.dataset
      const district = this.data.districtList[index] // 假设 district 有 id 字段
      const currentDistrict = [...this.data.currentDistrict]

      const matchIndex = currentDistrict.findIndex(
        (item) => item.id === district.id
      )

      if (matchIndex > -1) {
        // 已存在，取消选中
        const removedId = currentDistrict[matchIndex].id
        // 🚀 调用删除 tempSelected 中的方法
        this.removeFromTempSelectedById(removedId)
        currentDistrict.splice(matchIndex, 1)
      } else {
        // 不存在，添加
        currentDistrict.push(district)
        this.handleItemChange(district)
      }
      this.setData({ currentDistrict })
      console.log(this.data.currentDistrict, "数据")
    },

    handleItemChange(selectedItem) {
      const tempSelected = [...this.data.tempSelected] // 创建副本避免直接修改 data

      // 根据 id 判断是否已存在
      const exists = tempSelected.some((item) => item.id === selectedItem.id)

      if (!exists) {
        tempSelected.push(selectedItem)

        this.setData({
          tempSelected,
          // activeList: this.parseRegionKeys(tempSelected),
        })
      }

      console.log(tempSelected, "数据呢")
    },

    removeFromTempSelectedById(id) {
      const tempSelected = [...this.data.tempSelected]
      const matchIndex = tempSelected.findIndex((item) => item.id === id)

      if (matchIndex > -1) {
        tempSelected.splice(matchIndex, 1)
      }

      this.setData({ tempSelected })
    },

    parseRegionKeys(regionArray) {
      const result = {
        province: [],
        city: [],
        district: [],
      }

      if (!Array.isArray(regionArray)) return result

      const allIds = regionArray
        .filter(
          (item) => item && typeof item.key === "string" && item.key.trim()
        )
        .flatMap((item) => item.key.split("-"))

      const uniqueIds = [...new Set(allIds)]

      uniqueIds.forEach((id) => {
        const numId = Number(id)
        if (isNaN(numId)) return

        // 简单规则判断层级
        if (id.endsWith("0000")) {
          result.province.push(numId)
        } else if (id.endsWith("00")) {
          result.city.push(numId)
        } else {
          result.district.push(numId)
        }
      })

      return result
    },

    /**
     * 移除已选择的地区
     */
    removeSelectedRegion(e) {
      const { index } = e.currentTarget.dataset
      const tempSelected = [...this.data.tempSelected]
      if (index > -1) {
        tempSelected.splice(index, 1)
      }
      this.setData({
        tempSelected,
      })
    },

    /**
     * 清空所有选择
     */
    clearAllSelected() {
      // this.triggerEvent("clearAllSelected")
      this.setData({
        tempSelected: [],
        currentDistrict: [],
        currentCity: {},
        currentProvince: {},
        cityList: [],
        districtList: [],
      })
    },

    /**
     * 完成选择
     */
    confirmSelection() {
      this.triggerEvent("confirmSelection", {
        tempSelected: this.data.tempSelected,
        filterKey: this.data.filterKey,
      })
    },

    /**
     * 加载考试动态城市列表
     */
    async loadNewsCityList(province) {
      // this.setData({
      //   "newsRegionPopupData.loading.city": true,
      // })
      try {
        // 使用工具函数提取参数
        const { topId, parentId } = extractRegionParams(
          province,
          province.cokeyde
        )

        // 调用接口获取城市列表 - level=2表示城市
        const cityData = await this.getNewsRegionList(topId, parentId, 2)
        // 使用工具函数格式化数据
        const formattedCityList = cityData

        this.setData({
          cityList: formattedCityList,
          "newsRegionPopupData.loading.city": false,
        })
        console.log(this.data.cityList)
        // 同步已选择地区的状态
        // this.syncNewsSelectedRegionsWithLists()
      } catch (error) {
        console.error("考试动态加载城市列表失败:", error)
        this.setData({
          "newsRegionPopupData.loading.city": false,
        })
        wx.showToast({
          title: "加载城市失败",
          icon: "none",
        })
      }
    },
    /**
     * 加载考试动态区县列表
     */
    async loadNewsDistrictList(city) {
      this.setData({
        "newsRegionPopupData.loading.district": true,
      })

      try {
        // 使用工具函数提取参数
        const topId = city.area_parent_id
        const parentId = city.id
        // 调用接口获取区县列表 - level=3表示区县
        const districtData = await this.getNewsRegionList(topId, parentId, 3)

        // 使用工具函数格式化数据
        const formattedDistrictList = formatProvinceData(districtData)

        this.setData({
          districtList: formattedDistrictList,
          "newsRegionPopupData.loading.district": false,
        })
        if (this.data.currentDistrict.length == 0) {
          let currentDistrict = [...this.data.currentDistrict] // 创建副本

          if (this.data.tempSelected.length > 0) {
            // 使用展开运算符将 tempSelected 中的元素添加到 currentDistrict
            currentDistrict = [
              ...this.data.tempSelected, // 展开数组元素
              ...this.data.currentDistrict,
            ]
          }

          console.log(currentDistrict, "修正后的currentDistrict")
          this.setData({
            currentDistrict,
          })
        }

        console.log("考试动态区县列表加载完成:", formattedDistrictList)

        // 同步已选择地区的状态
        // this.syncNewsSelectedRegionsWithLists()
      } catch (error) {
        console.error("考试动态加载区县列表失败:", error)
        this.setData({
          "newsRegionPopupData.loading.district": false,
        })
        wx.showToast({
          title: "加载区县失败",
          icon: "none",
        })
      }
    },

    /**
     * 调用地区接口获取数据（考试动态专用）
     * @param {number} topId - 省级id
     * @param {number} parentId - 父级id
     * @param {number} level - 第几级（1:省份, 2:城市, 3:区县）
     */
    async getNewsRegionList(topId = 0, parentId = 0, level = 1) {
      const param = {
        top_id: topId,
        parent_id: parentId,
        level: level,
      }

      try {
        const res = await UTIL.request(API.getRegionChildList, param)
        console.log(
          `考试动态获取地区数据 - level:${level}, top_id:${topId}, parent_id:${parentId}`,
          res
        )

        if (res && res.error && res.error.code === 0 && res.data) {
          return res.data
        } else {
          console.error("考试动态获取地区数据失败:", res)
          return []
        }
      } catch (error) {
        console.error("考试动态获取地区数据时出错", error)
        return []
      }
    },

    /**
     * 加载考试动态省份列表
     */
    async loadNewsProvinceList() {
      try {
        // 调用接口获取省份列表 - level=1表示省份
        const provinceData = await this.getNewsRegionList(0, 0, 1)

        // 使用工具函数格式化数据
        const formattedProvinceList = formatProvinceData(provinceData)
        this.setData({ provinceList: formattedProvinceList })
      } catch (error) {
        wx.showToast({
          title: "加载省份失败",
          icon: "none",
        })
      }
    },
  },
})
