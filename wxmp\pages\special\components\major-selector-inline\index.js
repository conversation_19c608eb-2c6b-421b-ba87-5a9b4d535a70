const API = require("@/config/api")
const UTIL = require("@/utils/util")

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    defaultValue: {
      type: String,
      value: "",
    },
    educationId: {
      type: String,
      value: "6", // 默认为普通本科
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    searchKeyword: "",
    selectedText: "",
    selectedFirstIndex: -1,
    selectedSecondIndex: -1,
    selectedThirdIndex: -1,
    selectedMajor: null,
    firstLevelList: [],
    secondLevelList: [],
    thirdLevelList: [],
    searchResults: [], // 搜索结果
    allMajorData: [], // 存储所有专业数据，用于搜索
  },

  /**
   * 监听属性变化
   */
  observers: {
    educationId(newVal, oldVal) {
      if (newVal !== oldVal && newVal) {
        this.initData()
      }
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化数据 - 获取第一级专业数据
     */
    async initData() {
      try {
        this.setData({
          firstLevelList: [],
          secondLevelList: [],
          thirdLevelList: [],
          selectedFirstIndex: -1,
          selectedSecondIndex: -1,
          selectedThirdIndex: -1,
          selectedMajor: null,
          selectedText: "",
        })

        const param = {
          pid: 0,
          education_id: this.data.educationId,
        }

        const res = await UTIL.request(API.getMajorList, param)
        console.log("第一级专业数据:", res)

        if (res && res.data && res.data.length > 0) {
          this.setData({
            firstLevelList: res.data,
          })
        }
      } catch (error) {
        console.error("获取第一级专业数据失败:", error)
        wx.showToast({
          title: "获取专业数据失败",
          icon: "none",
        })
      }
    },

    /**
     * 选择一级分类
     */
    async onFirstLevelSelect(e) {
      const index = e.currentTarget.dataset.index
      const selectedItem = this.data.firstLevelList[index]

      try {
        this.setData({
          selectedFirstIndex: index,
          selectedSecondIndex: -1,
          selectedThirdIndex: -1,
          selectedMajor: null,
          selectedText: "",
          secondLevelList: [],
          thirdLevelList: [],
        })

        const param = {
          pid: selectedItem.id,
          education_id: this.data.educationId,
        }

        const res = await UTIL.request(API.getMajorList, param)
        console.log("第二级专业数据:", res)

        if (res && res.data && res.data.length > 0) {
          this.setData({
            secondLevelList: res.data,
          })
        }
      } catch (error) {
        console.error("获取第二级专业数据失败:", error)
        wx.showToast({
          title: "获取专业数据失败",
          icon: "none",
        })
      }
    },

    /**
     * 选择二级分类
     */
    async onSecondLevelSelect(e) {
      const index = e.currentTarget.dataset.index
      const selectedItem = this.data.secondLevelList[index]

      try {
        this.setData({
          selectedSecondIndex: index,
          selectedThirdIndex: -1,
          selectedMajor: null,
          selectedText: "",
          thirdLevelList: [],
        })

        const param = {
          pid: selectedItem.id,
          education_id: this.data.educationId,
        }

        const res = await UTIL.request(API.getMajorList, param)
        console.log("第三级专业数据:", res)

        if (res && res.data && res.data.length > 0) {
          this.setData({
            thirdLevelList: res.data,
          })
        }
      } catch (error) {
        console.error("获取第三级专业数据失败:", error)
        wx.showToast({
          title: "获取专业数据失败",
          icon: "none",
        })
      }
    },

    /**
     * 选择三级分类（具体专业）
     */
    onThirdLevelSelect(e) {
      const index = e.currentTarget.dataset.index
      const selectedItem = this.data.thirdLevelList[index]
      const firstLevel = this.data.firstLevelList[this.data.selectedFirstIndex]
      const secondLevel =
        this.data.secondLevelList[this.data.selectedSecondIndex]

      const selectedText = `${firstLevel.name}-${secondLevel.name}-${selectedItem.name}`

      this.setData({
        selectedThirdIndex: index,
        selectedMajor: selectedItem,
        selectedText: selectedText,
      })

      // 触发确认事件，直接选择
      this.triggerEvent("confirm", {
        major: selectedItem,
        text: selectedText,
      })
    },

    /**
     * 搜索输入事件
     */
    onSearchInput(e) {
      const keyword = e.detail.value.trim()
      this.setData({
        searchKeyword: keyword,
      })

      if (keyword) {
        this.performSearch(keyword)
      } else {
        this.setData({
          searchResults: [],
        })
      }
    },

    /**
     * 执行搜索
     */
    async performSearch(keyword) {
      try {
        const param = {
          keywords: keyword,
          education_id: this.data.educationId,
        }

        const res = await UTIL.request(API.getMajorByKeyWords, param)
        console.log("搜索结果:", res)

        if (res && res.data && res.data.length > 0) {
          // 将搜索结果转换为组件所需的格式
          const searchResults = res.data.map((item) => ({
            thirdLevel: item,
            fullPath: item.name, // 如果接口返回的数据中有完整路径，可以使用对应字段
          }))

          this.setData({
            searchResults: searchResults,
          })
        } else {
          this.setData({
            searchResults: [],
          })
        }
      } catch (error) {
        console.error("搜索失败:", error)
        this.setData({
          searchResults: [],
        })
        wx.showToast({
          title: "搜索失败，请稍后重试",
          icon: "none",
        })
      }
    },

    /**
     * 选择搜索结果
     */
    onSelectSearchResult(e) {
      const item = e.currentTarget.dataset.item

      this.setData({
        selectedMajor: item.thirdLevel,
        selectedText: item.fullPath,
        searchKeyword: "", // 清空搜索关键词
        searchResults: [], // 清空搜索结果
      })

      // 触发确认事件
      this.triggerEvent("confirm", {
        major: item.thirdLevel,
        text: item.fullPath,
      })
    },
  },
})
