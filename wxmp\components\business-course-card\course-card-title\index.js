// pages/course/components/list/course-card-title/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    title: {
      type: String,
      value: "",
    },
    subTitle: {
      type: String,
      value: "",
    },
    showLabel: {
      type: Boolean,
      value: true,
    },

    titlePrefix: {
      type: Object,
      value: {},
    },
    titleEllipsis: {
      type: Number,
      value: 2,
    },
    isSearch: {
      type: Boolean,
      value: true,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    textArr: [], // 存储解析后的文本数组
  },

  lifetimes: {
    attached() {
      if (this.data.isSearch && this.data.title) {
        const textArr = this.parseHtmlToTextArr(this.data.title)

        // 更新数据
        this.setData({
          textArr: textArr,
        })
      }
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    parseHtmlToTextArr(html) {
      const textArr = []
      let remaining = html

      while (remaining.length > 0) {
        const startTagMatch = remaining.match(/<text class="([^"]+)">/)
        if (startTagMatch) {
          // 提取开始标签前的普通文本
          const beforeText = remaining.slice(0, startTagMatch.index)
          if (beforeText) {
            textArr.push({ text: beforeText, isRedText: false })
          }

          // 提取 <text class="red-text"> 内部的内容
          const endTagMatch = remaining.match(/<\/text>/)
          if (endTagMatch) {
            const innerText = remaining.slice(
              startTagMatch.index + startTagMatch[0].length,
              endTagMatch.index
            )
            textArr.push({ text: innerText, isRedText: true })

            // 剩余部分从 </text> 后继续处理
            remaining = remaining.slice(
              endTagMatch.index + endTagMatch[0].length
            )
          } else {
            throw new Error("Missing closing </text> tag")
          }
        } else {
          // 没有更多 <text> 标签，直接将剩余内容作为普通文本
          textArr.push({ text: remaining, isRedText: false })
          remaining = ""
        }
      }

      return textArr
    },
  },
})
