// pages/course/components/list/advclass-other-card/index.js
const ROUTER = require("@/services/mpRouter")
const APP = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    item: {
      type: Object,
      value: null,
    },
  },
  options: {},
  /**
   * 组件的初始数据
   */
  data: {
    IMAGE_PREFIX: APP.globalData.CONFIG.IMAGE_PREFIX,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    goDetail(e) {
      const data = e.currentTarget.dataset.item
      ROUTER.navigateTo({
        path: "/package-goods/advclass/advclass-detail/index",
        query: {
          no: data.project_no,
        },
      })
    },
  },
})
