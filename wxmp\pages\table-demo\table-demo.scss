/* 竖向滚动容器 */
.vertical-scroll-container {
  width: 100vw;
  height: 100vh;
  background: #ffffff;
}

/* 左侧固定列区域 */
.fixed-columns-area {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 100;
  /* 单个表格的固定列部分 */
  .table-fixed-section {
    width: 100%;
  }
  /* 首列单元格 */
  .first-column-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16rpx;
    color: #495057;
    font-size: 26rpx;
    border-bottom: 1rpx solid #f0f2f5;
    border-right: 1rpx solid #f0f2f5;
    box-sizing: border-box;
    background: #fff;
  }
}

/* 右侧横向滚动区域 */
.horizontal-scroll-area {
  position: relative;
  background: #ffffff;
  flex: 1;
  height: 100%;
  z-index: 90;
  .horizontal-scroll-content {
    display: flex;
    flex-direction: column;
    min-height: 100%;
  }
  .table-scroll-section {
    width: 100%;
  }
  /* 数据行 */
  .data-row {
    display: flex;
    width: 100%;
    align-items: stretch;
    box-sizing: border-box;

    &:last-child {
      border-bottom: none;
    }
  }

  /* 数据单元格 */
  .data-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16rpx;
    color: #6c757d;
    font-size: 26rpx;
    border-right: 1rpx solid #f0f2f5;
    border-bottom: 1rpx solid #f0f2f5;

    box-sizing: border-box;
    flex-shrink: 0;
    background: #ffffff;

    &:last-child {
      border-right: none;
    }
  }
  .header-placeholder {
    // position: sticky;
    // top: 264rpx;
    // background: #fff;
  }
}

.box {
  display: flex;
}

.header-placeholder {
  height: 80rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid #e8eaed;
  .sticky-content {
    z-index: -1;
    position: absolute;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100%;
    background-color: #fff;
    border-bottom: 1rpx solid #e8eaed;
    display: flex;
    align-items: center;
    padding-left: 20rpx;
  }
}
/* 吸顶区域 */
.sticky-header {
  position: sticky;
  top: 264rpx;
  left: 0;
  z-index: 50;
}

.filter-fixed-section {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 1100;
  height: 264rpx;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border-right: 1rpx solid #f0f2f5;
  border-bottom: 1rpx solid #f0f2f5;
  box-sizing: border-box;
}

.filter-scroll-section {
  .data-row {
    .data-cell {
      height: 264rpx;
    }
  }
}
