.select-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 26rpx 24rpx;
  background: rgba(247, 248, 250, 1);
  border-radius: 12rpx;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .item-title {
    font-size: 28rpx;
    color: rgba(102, 102, 102, 1);
    flex: 1;
    min-width: 0;
    padding-right: 30rpx;
  }

  .check-icon {
    width: 32rpx;
    height: 32rpx;
    margin-left: 16rpx;
  }

  &.selected {
    .item-title {
      color: rgba(60, 61, 66, 1);
      font-weight: 500;
    }
  }

  &:active {
    background-color: #f5f5f5;
  }
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
}
