---
outline: deep
---

# 风格指南 {#style-guide}

::: warning 注意
此 Vue.js 风格指南已过时，需要重新审查。如果您有任何问题或建议，请[提交 issue](https://github.com/vuejs/docs/issues/new)。
:::

这是 Vue 专用代码的官方风格指南。如果您在项目中使用 Vue，这是避免错误、无意义争论和反模式的绝佳参考。但是，我们不认为任何风格指南都适用于所有团队或项目，因此鼓励基于过往经验、周围技术栈和个人价值观进行有意识的偏离。

在大多数情况下，我们也避免对 JavaScript 或 HTML 的一般性建议。我们不介意您是否使用分号或尾随逗号。我们不介意您的 HTML 是否对属性值使用单引号或双引号。但是，在某些情况下会有例外，我们发现特定模式在 Vue 的上下文中很有帮助。

最后，我们将规则分为四个类别：

## 规则分类 {#rule-categories}

### 优先级 A：必要的（错误预防） {#priority-a-essential-error-prevention}

这些规则有助于防止错误，因此请不惜一切代价学习并遵守它们。可能存在例外情况，但应该非常罕见，并且只能由同时具备 JavaScript 和 Vue 专业知识的人员制定。

- [查看所有优先级 A 规则](./rules-essential)

### 优先级 B：强烈推荐 {#priority-b-strongly-recommended}

在大多数项目中，这些规则已被发现可以提高可读性和/或开发者体验。如果您违反了它们，您的代码仍然可以运行，但违反应该很少见并且有充分的理由。

- [查看所有优先级 B 规则](./rules-strongly-recommended)

### 优先级 C：推荐 {#priority-c-recommended}

当存在多个同样好的选项时，可以做出任意选择以确保一致性。在这些规则中，我们描述每个可接受的选项并建议一个默认选择。这意味着您可以在自己的代码库中自由做出不同的选择，只要您保持一致并有充分的理由。但请确实有充分的理由！通过适应社区标准，您将：

1. 训练您的大脑更容易解析您遇到的大多数社区代码
2. 能够复制和粘贴大多数社区代码示例而无需修改
3. 经常发现新员工已经习惯了您首选的编码风格，至少在 Vue 方面

- [查看所有优先级 C 规则](./rules-recommended)

### 优先级 D：谨慎使用 {#priority-d-use-with-caution}

Vue 的某些功能存在是为了适应罕见的边缘情况或从遗留代码库更平滑地迁移。但是，当过度使用时，它们可能会使您的代码更难维护，甚至成为错误的来源。这些规则揭示了潜在的风险功能，描述了何时以及为什么应该避免它们。

- [查看所有优先级 D 规则](./rules-use-with-caution)
