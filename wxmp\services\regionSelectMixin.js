/**
 * 地区选择 Mixin
 * 提供通用的地区选择功能，包括省市区三级联动、缓存管理等
 * 使用方式：在页面的 JS 文件中通过 Object.assign 混入
 */

const UTIL = require("../utils/util")
const API = require("../config/api")
const { getSelectedRegionsCache, setSelectedRegionsCache } = require("../utils/cache/regionCache")
const { formatProvinceData, formatCityData, formatDistrictData } = require("../utils/regionUtils")

const RegionSelectMixin = {
  /**
   * 地区选择相关的数据
   */
  regionData: {
    // 地区弹窗数据
    regionPopupData: {
      selectedRegions: [], // 已选择的地区列表
      provinceList: [], // 省份列表
      cityList: [], // 城市列表
      districtList: [], // 区县列表
      currentProvince: null, // 当前选中的省份
      currentCity: null, // 当前选中的城市
      loading: {
        province: false, // 省份加载状态
        city: false, // 城市加载状态
        district: false, // 区县加载状态
      },
    },

    // 考试动态地区弹窗数据
    newsRegionPopupData: {
      selectedRegions: [], // 已选择的地区列表
      provinceList: [], // 省份列表
      cityList: [], // 城市列表
      districtList: [], // 区县列表
      currentProvince: null, // 当前选中的省份
      currentCity: null, // 当前选中的城市
      loading: {
        province: false, // 省份加载状态
        city: false, // 城市加载状态
        district: false, // 区县加载状态
      },
    },
  },

  /**
   * 地区选择相关的方法
   */
  regionMethods: {
    /**
     * 初始化地区数据
     * @param {string} cacheKey 缓存键名，如 'announcement', 'news', 'job', 'collection'
     * @param {string} dataKey 数据键名，如 'regionPopupData', 'newsRegionPopupData'
     */
    initRegionData(cacheKey = 'announcement', dataKey = 'regionPopupData') {
      console.log(`初始化地区数据，缓存键：${cacheKey}，数据键：${dataKey}`)
      
      // 从缓存加载已选择的地区
      const cachedRegions = getSelectedRegionsCache(cacheKey) || []

      this.setData({
        [`${dataKey}.selectedRegions`]: cachedRegions,
      })

      // 加载省份列表
      this.loadProvinceList(dataKey)
    },

    /**
     * 加载省份列表
     * @param {string} dataKey 数据键名
     */
    loadProvinceList(dataKey = 'regionPopupData') {
      this.setData({
        [`${dataKey}.loading.province`]: true,
      })

      UTIL.request(API.getRegionChildList, {
        level: 1,
        parent_id: 0,
        top_id: 0,
      })
        .then((res) => {
          if (res && res.error && res.error.code === 0 && res.data) {
            // 使用格式化函数处理数据
            const formattedProvinceList = formatProvinceData(res.data)
            this.setData({
              [`${dataKey}.provinceList`]: formattedProvinceList,
              [`${dataKey}.loading.province`]: false,
            })
            console.log(`省份列表加载成功，数据键：${dataKey}`, formattedProvinceList)
          } else {
            console.error(`省份列表加载失败，数据键：${dataKey}`, res)
            this.setData({
              [`${dataKey}.loading.province`]: false,
            })
          }
        })
        .catch((error) => {
          console.error(`省份列表请求异常，数据键：${dataKey}`, error)
          this.setData({
            [`${dataKey}.loading.province`]: false,
          })
        })
    },

    /**
     * 加载城市列表
     * @param {number} provinceId 省份ID
     * @param {string} dataKey 数据键名
     */
    loadCityList(provinceId, dataKey = 'regionPopupData') {
      this.setData({
        [`${dataKey}.loading.city`]: true,
        [`${dataKey}.cityList`]: [],
        [`${dataKey}.districtList`]: [],
      })

      UTIL.request(API.getRegionChildList, {
        level: 2,
        parent_id: provinceId,
        top_id: provinceId,
      })
        .then((res) => {
          if (res && res.error && res.error.code === 0 && res.data) {
            // 使用格式化函数处理数据
            const formattedCityList = formatCityData(res.data)
            this.setData({
              [`${dataKey}.cityList`]: formattedCityList,
              [`${dataKey}.loading.city`]: false,
            })
            console.log(`城市列表加载成功，数据键：${dataKey}`, formattedCityList)
          } else {
            console.error(`城市列表加载失败，数据键：${dataKey}`, res)
            this.setData({
              [`${dataKey}.loading.city`]: false,
            })
          }
        })
        .catch((error) => {
          console.error(`城市列表请求异常，数据键：${dataKey}`, error)
          this.setData({
            [`${dataKey}.loading.city`]: false,
          })
        })
    },

    /**
     * 加载区县列表
     * @param {number} cityId 城市ID
     * @param {string} dataKey 数据键名
     */
    loadDistrictList(cityId, provinceId, dataKey = 'regionPopupData') {
      this.setData({
        [`${dataKey}.loading.district`]: true,
        [`${dataKey}.districtList`]: [],
      })

      UTIL.request(API.getRegionChildList, {
        level: 3,
        parent_id: cityId,
        top_id: provinceId,
      })
        .then((res) => {
          if (res && res.error && res.error.code === 0 && res.data) {
            // 使用格式化函数处理数据
            const formattedDistrictList = formatDistrictData(res.data)
            this.setData({
              [`${dataKey}.districtList`]: formattedDistrictList,
              [`${dataKey}.loading.district`]: false,
            })
            console.log(`区县列表加载成功，数据键：${dataKey}`, formattedDistrictList)
          } else {
            console.error(`区县列表加载失败，数据键：${dataKey}`, res)
            this.setData({
              [`${dataKey}.loading.district`]: false,
            })
          }
        })
        .catch((error) => {
          console.error(`区县列表请求异常，数据键：${dataKey}`, error)
          this.setData({
            [`${dataKey}.loading.district`]: false,
          })
        })
    },

    /**
     * 处理省份点击
     * @param {Object} e 事件对象
     * @param {string} dataKey 数据键名
     */
    handleProvinceClick(e, dataKey = 'regionPopupData') {
      const { province } = e.detail
      console.log(`省份点击，数据键：${dataKey}`, province)

      this.setData({
        [`${dataKey}.currentProvince`]: province,
        [`${dataKey}.currentCity`]: null,
      })

      // 加载该省份下的城市列表
      this.loadCityList(province.code || province.id, dataKey)
    },

    /**
     * 处理城市点击
     * @param {Object} e 事件对象
     * @param {string} dataKey 数据键名
     */
    handleCityClick(e, dataKey = 'regionPopupData') {
      const { city } = e.detail
      const currentData = this.data[dataKey]
      console.log(`城市点击，数据键：${dataKey}`, city)

      this.setData({
        [`${dataKey}.currentCity`]: city,
      })

      // 加载该城市下的区县列表，需要传递省份ID
      const provinceId = currentData.currentProvince ? (currentData.currentProvince.code || currentData.currentProvince.id) : 0
      this.loadDistrictList(city.code || city.id, provinceId, dataKey)
    },

    /**
     * 处理区县点击
     * @param {Object} e 事件对象
     * @param {string} dataKey 数据键名
     * @param {string} cacheKey 缓存键名
     */
    handleDistrictClick(e, dataKey = 'regionPopupData', cacheKey = 'announcement') {
      const { district } = e.detail
      const currentData = this.data[dataKey]
      
      console.log(`区县点击，数据键：${dataKey}，缓存键：${cacheKey}`, district)

      // 检查是否已经选择了该区县
      const isAlreadySelected = currentData.selectedRegions.some(
        (region) => region.districtId === district.id
      )

      if (isAlreadySelected) {
        console.log("该区县已经选择，忽略重复选择")
        return
      }

      // 构造地区对象
      const regionObject = {
        provinceId: currentData.currentProvince.id,
        provinceName: currentData.currentProvince.name,
        cityId: currentData.currentCity.id,
        cityName: currentData.currentCity.name,
        districtId: district.id,
        districtName: district.name,
        displayName: `${currentData.currentProvince.name}-${currentData.currentCity.name}-${district.name}`,
      }

      // 添加到已选择列表
      const updatedSelectedRegions = [...currentData.selectedRegions, regionObject]

      this.setData({
        [`${dataKey}.selectedRegions`]: updatedSelectedRegions,
      })

      // 保存到缓存
      setSelectedRegionsCache(updatedSelectedRegions, cacheKey)
      console.log(`地区选择已保存到缓存，缓存键：${cacheKey}`, updatedSelectedRegions)
    },

    /**
     * 处理区县长按（删除选择）
     * @param {Object} e 事件对象
     * @param {string} dataKey 数据键名
     * @param {string} cacheKey 缓存键名
     */
    handleDistrictLongPress(e, dataKey = 'regionPopupData', cacheKey = 'announcement') {
      const { district } = e.detail
      const currentData = this.data[dataKey]
      
      console.log(`区县长按删除，数据键：${dataKey}，缓存键：${cacheKey}`, district)

      // 从已选择列表中移除
      const updatedSelectedRegions = currentData.selectedRegions.filter(
        (region) => region.districtId !== district.id
      )

      this.setData({
        [`${dataKey}.selectedRegions`]: updatedSelectedRegions,
      })

      // 更新缓存
      setSelectedRegionsCache(updatedSelectedRegions, cacheKey)
      console.log(`地区选择已更新缓存，缓存键：${cacheKey}`, updatedSelectedRegions)
    },

    /**
     * 移除选中的地区
     * @param {Object} e 事件对象
     * @param {string} dataKey 数据键名
     * @param {string} cacheKey 缓存键名
     */
    handleRemoveSelectedRegion(e, dataKey = 'regionPopupData', cacheKey = 'announcement') {
      const { region } = e.detail
      const currentData = this.data[dataKey]
      
      console.log(`移除选中地区，数据键：${dataKey}，缓存键：${cacheKey}`, region)

      // 从已选择列表中移除
      const updatedSelectedRegions = currentData.selectedRegions.filter(
        (selectedRegion) => selectedRegion.districtId !== region.districtId
      )

      this.setData({
        [`${dataKey}.selectedRegions`]: updatedSelectedRegions,
      })

      // 更新缓存
      setSelectedRegionsCache(updatedSelectedRegions, cacheKey)
      console.log(`地区选择已更新缓存，缓存键：${cacheKey}`, updatedSelectedRegions)
    },

    /**
     * 清空所有选中的地区
     * @param {Object} e 事件对象
     * @param {string} dataKey 数据键名
     * @param {string} cacheKey 缓存键名
     */
    handleClearAllSelected(e, dataKey = 'regionPopupData', cacheKey = 'announcement') {
      console.log(`清空所有选中地区，数据键：${dataKey}，缓存键：${cacheKey}`)

      this.setData({
        [`${dataKey}.selectedRegions`]: [],
      })

      // 清空缓存
      setSelectedRegionsCache([], cacheKey)
      console.log(`地区选择缓存已清空，缓存键：${cacheKey}`)
    },

    /**
     * 确认地区选择
     * @param {Object} e 事件对象
     * @param {string} dataKey 数据键名
     * @param {string} cacheKey 缓存键名
     * @param {Function} callback 确认后的回调函数
     */
    handleConfirmSelection(e, dataKey = 'regionPopupData', cacheKey = 'announcement', callback) {
      const currentData = this.data[dataKey]

      console.log(`确认地区选择，数据键：${dataKey}，缓存键：${cacheKey}`, currentData.selectedRegions)

      // 保存到缓存
      setSelectedRegionsCache(currentData.selectedRegions, cacheKey)

      // 执行回调函数
      if (typeof callback === 'function') {
        callback(currentData.selectedRegions)
      }

      console.log(`地区选择确认完成，缓存键：${cacheKey}`)
    },

    /**
     * 获取地区选择的API参数
     * @param {Array} selectedRegions 选中的地区列表
     * @returns {Array} API参数格式的地区ID数组
     */
    getRegionApiParams(selectedRegions) {
      if (!selectedRegions || selectedRegions.length === 0) {
        return []
      }

      return selectedRegions.map(region => region.districtId)
    },

    /**
     * 更新选中状态到对应的状态对象
     * @param {Array} selectedRegions 选中的地区列表
     * @param {string} stateKey 状态对象键名，如 'noticeSelectForTemplate', 'examSelectForTemplate'
     * @param {string} filterKey 筛选键名，如 'apply_region'
     */
    updateRegionSelectState(selectedRegions, stateKey = 'noticeSelectForTemplate', filterKey = 'apply_region') {
      const currentState = this.data[stateKey] || {}
      const regionIds = this.getRegionApiParams(selectedRegions)

      const updatedState = {
        ...currentState,
        [filterKey]: regionIds,
      }

      this.setData({
        [stateKey]: updatedState,
      })

      console.log(`地区选择状态已更新，状态键：${stateKey}，筛选键：${filterKey}`, regionIds)
    },

    /**
     * 创建地区选择事件处理器
     * 返回一个对象，包含所有地区选择相关的事件处理方法
     * @param {string} dataKey 数据键名
     * @param {string} cacheKey 缓存键名
     * @param {Function} confirmCallback 确认回调函数
     */
    createRegionHandlers(dataKey = 'regionPopupData', cacheKey = 'announcement', confirmCallback) {
      return {
        handleProvinceClick: (e) => this.handleProvinceClick(e, dataKey),
        handleCityClick: (e) => this.handleCityClick(e, dataKey),
        handleDistrictClick: (e) => this.handleDistrictClick(e, dataKey, cacheKey),
        handleDistrictLongPress: (e) => this.handleDistrictLongPress(e, dataKey, cacheKey),
        handleRemoveSelectedRegion: (e) => this.handleRemoveSelectedRegion(e, dataKey, cacheKey),
        handleClearAllSelected: (e) => this.handleClearAllSelected(e, dataKey, cacheKey),
        handleConfirmSelection: (e) => this.handleConfirmSelection(e, dataKey, cacheKey, confirmCallback),
      }
    },
  },

  /**
   * 初始化 Mixin
   * 在页面的 onLoad 或组件的 attached 中调用
   */
  initRegionSelectMixin() {
    // 将地区数据合并到页面数据中
    const currentData = this.data || {}
    this.setData({
      ...currentData,
      ...this.regionData,
    })

    // 将地区方法合并到页面方法中
    Object.assign(this, RegionSelectMixin.regionMethods)

    console.log("RegionSelectMixin 初始化完成")
  },

  /**
   * 使用示例：
   *
   * 1. 在页面中导入并混入 mixin：
   *    const RegionSelectMixin = require("@/services/regionSelectMixin")
   *    const pageConfig = Object.assign({}, RegionSelectMixin, {
   *      // 页面配置
   *    })
   *
   * 2. 在 onLoad 中初始化：
   *    this.initRegionSelectMixin()
   *
   * 3. 初始化地区数据：
   *    this.initRegionData('announcement', 'regionPopupData') // 公告Tab
   *    this.initRegionData('news', 'newsRegionPopupData')     // 考试动态Tab
   *
   * 4. 创建事件处理器：
   *    const handlers = this.createRegionHandlers('regionPopupData', 'announcement', (regions) => {
   *      // 确认回调
   *      this.updateRegionSelectState(regions, 'noticeSelectForTemplate', 'apply_region')
   *    })
   *
   * 5. 在页面方法中使用：
   *    handleRegionProvinceClick(e) {
   *      this.handleProvinceClick(e, 'regionPopupData')
   *    }
   */
}

module.exports = RegionSelectMixin
