// 导入变量和混入
@import "./variables.scss";
@import "./mixins.scss";
@import "../fonts/DINBold.css";

// ===== 移动端基础样式重置 =====
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

html {
  // 设置根字体大小，1rem = 37.5px（基于375px设计稿）
  font-size: $root-font-size;
  // 禁止用户缩放
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  // 平滑滚动
  scroll-behavior: smooth;
  // 禁用双击缩放
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

body {
  margin: 0;
  padding: 0;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    "Helvetica Neue",
    Helvetica,
    Segoe UI,
    Arial,
    Roboto,
    "PingFang SC",
    "miui",
    "Hiragino Sans GB",
    "Microsoft Yahei",
    sans-serif;
  // 禁用双击缩放
  touch-action: manipulation;
  -webkit-touch-callout: none;
}

// ===== 移动端应用容器 =====
#app {
  width: 100%;
  min-height: 100vh;
  background-color: $bg-secondary;
}

// ===== 通用样式类 =====
// 容器
.container {
  width: 100%;
  max-width: 750px; // 基于375px设计稿的2倍
  margin: 0 auto;
  padding: 0 $spacing-md;
}

// 页面容器
.page {
  min-height: 100vh;
  background-color: $bg-secondary;

  &__header {
    background-color: $bg-primary;
    @include safe-area-insets(padding, top);
  }

  &__content {
    flex: 1;
    padding: $spacing-md;
  }

  &__footer {
    background-color: $bg-primary;
    @include safe-area-insets(padding, bottom);
  }
}

// 卡片
.card {
  @include card();
  margin-bottom: $spacing-md;

  &__header {
    padding-bottom: $spacing-sm;
    @include border-1px($border-light, bottom);
    margin-bottom: $spacing-sm;
  }

  &__title {
    font-size: $font-size-lg;
    font-weight: 600;
    color: $text-primary;
  }

  &__content {
    color: $text-regular;
  }
}

// 按钮
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border: none;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  cursor: pointer;
  transition: $transition-base;
  text-decoration: none;

  &--primary {
    @include button-variant($primary-color);
  }

  &--success {
    @include button-variant($success-color);
  }

  &--warning {
    @include button-variant($warning-color);
  }

  &--danger {
    @include button-variant($danger-color);
  }

  &--block {
    width: 100%;
  }

  &--large {
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-lg;
  }

  &--small {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
  }
}

// 文本样式
.text {
  &--primary {
    color: $text-primary;
  }
  &--regular {
    color: $text-regular;
  }
  &--secondary {
    color: $text-secondary;
  }
  &--placeholder {
    color: $text-placeholder;
  }

  &--xs {
    font-size: $font-size-xs;
  }
  &--sm {
    font-size: $font-size-sm;
  }
  &--md {
    font-size: $font-size-md;
  }
  &--lg {
    font-size: $font-size-lg;
  }
  &--xl {
    font-size: $font-size-xl;
  }

  &--center {
    text-align: center;
  }
  &--left {
    text-align: left;
  }
  &--right {
    text-align: right;
  }

  &--ellipsis {
    @include ellipsis(1);
  }
  &--ellipsis-2 {
    @include ellipsis(2);
  }
  &--ellipsis-3 {
    @include ellipsis(3);
  }
}

// 间距工具类
.m {
  &-0 {
    margin: 0;
  }
  &-xs {
    margin: $spacing-xs;
  }
  &-sm {
    margin: $spacing-sm;
  }
  &-md {
    margin: $spacing-md;
  }
  &-lg {
    margin: $spacing-lg;
  }
  &-xl {
    margin: $spacing-xl;
  }

  &t-0 {
    margin-top: 0;
  }
  &t-xs {
    margin-top: $spacing-xs;
  }
  &t-sm {
    margin-top: $spacing-sm;
  }
  &t-md {
    margin-top: $spacing-md;
  }
  &t-lg {
    margin-top: $spacing-lg;
  }
  &t-xl {
    margin-top: $spacing-xl;
  }

  &b-0 {
    margin-bottom: 0;
  }
  &b-xs {
    margin-bottom: $spacing-xs;
  }
  &b-sm {
    margin-bottom: $spacing-sm;
  }
  &b-md {
    margin-bottom: $spacing-md;
  }
  &b-lg {
    margin-bottom: $spacing-lg;
  }
  &b-xl {
    margin-bottom: $spacing-xl;
  }
}

.p {
  &-0 {
    padding: 0;
  }
  &-xs {
    padding: $spacing-xs;
  }
  &-sm {
    padding: $spacing-sm;
  }
  &-md {
    padding: $spacing-md;
  }
  &-lg {
    padding: $spacing-lg;
  }
  &-xl {
    padding: $spacing-xl;
  }

  &t-0 {
    padding-top: 0;
  }
  &t-xs {
    padding-top: $spacing-xs;
  }
  &t-sm {
    padding-top: $spacing-sm;
  }
  &t-md {
    padding-top: $spacing-md;
  }
  &t-lg {
    padding-top: $spacing-lg;
  }
  &t-xl {
    padding-top: $spacing-xl;
  }

  &b-0 {
    padding-bottom: 0;
  }
  &b-xs {
    padding-bottom: $spacing-xs;
  }
  &b-sm {
    padding-bottom: $spacing-sm;
  }
  &b-md {
    padding-bottom: $spacing-md;
  }
  &b-lg {
    padding-bottom: $spacing-lg;
  }
  &b-xl {
    padding-bottom: $spacing-xl;
  }
}

// Flex布局工具类
.flex {
  display: flex;

  &--column {
    flex-direction: column;
  }
  &--wrap {
    flex-wrap: wrap;
  }
  &--center {
    @include flex();
  }
  &--between {
    justify-content: space-between;
  }
  &--around {
    justify-content: space-around;
  }
  &--start {
    justify-content: flex-start;
  }
  &--end {
    justify-content: flex-end;
  }

  &-1 {
    flex: 1;
  }
}
