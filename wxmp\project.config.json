{"compileType": "miniprogram", "libVersion": "3.8.8", "packOptions": {"ignore": [], "include": []}, "setting": {"useCompilerPlugins": ["sass"], "coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "disableUseStrict": false}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "wx17dabf995070ae8f", "simulatorPluginLibVersion": {}}