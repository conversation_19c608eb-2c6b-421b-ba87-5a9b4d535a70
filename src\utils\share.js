import { isQQBrowser } from "./browser"

export function setWxShare(options = {}) {
  let { title, desc, link, imgUrl } = { ...options }
  wx.ready(function () {
    //需在用户可能点击分享按钮前就先调用
    let options = {
      title: title || "精品课程", // 分享标题
      desc: desc || "精品课程分享", // 分享描述
      link: link || window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
      imgUrl:
        imgUrl ||
        "https://appresource-1253756937.cos.ap-chengdu.myqcloud.com/skb/wv/pingtuan/share.png", // 分享图标
    }
    wx.updateAppMessageShareData(options)
    wx.updateTimelineShareData(options)
  })
}

export function setShare(param) {
  const wxParam = {}
  param?.title && (wxParam.title = param.title)
  param?.desc && (wxParam.desc = param.desc)
  param?.imgUrl && (wxParam.imgUrl = param.imgUrl)
  param?.link && (wxParam.link = param.link)
  setWxShare(wxParam)
  if (isQQBrowser()) {
    const qqParam = {}
    param?.title && (qqParam.title = param.title)
    param?.desc && (qqParam.summary = param.desc)
    param?.imgUrl && (qqParam.pic = param.imgUrl)
    param?.link && (qqParam.url = param.link)
    window.setShareInfo && window.setShareInfo(qqParam)
  }
  if (param?.title) {
    document.title = param.title

    const img = document.getElementById("share_img")
    // 修改 img 元素的 src 属性
    img.src = param.imgUrl

    // 获取meta标签
    var meta = document.querySelector('meta[name="description"]')
    // 修改meta标签的content属性
    meta.content = param?.desc
  }
}
