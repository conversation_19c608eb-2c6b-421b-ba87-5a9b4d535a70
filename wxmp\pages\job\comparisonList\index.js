const APP = getApp()
const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    IMAGE_PREFIX: APP.globalData.CONFIG.IMAGE_PREFIX,
    modalShow: false,
    deleteId: null, // 待删除的职位ID
    isAllSelected: false, // 是否全选
    jobList: [],
    pkListIds: [],
    fromDetail: false, // 是否从详情页过来的标记
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 接收从详情页传来的标记
    const fromDetail = options.fromDetail == "true"
    this.setData({
      fromDetail,
    })
  },
  async getList() {
    const pkListIds = wx.getStorageSync("pkListIds") || []
    this.setData({
      pkListIds,
    })
    const param = {
      ids: pkListIds,
    }
    const res = await UTIL.request(API.getCompareList, param)
    if (res && res?.error?.code == 0) {
      let arr = res.data.list || []
      if (arr.length) {
        // 获取当前已有的 jobList 数据
        const { jobList: currentJobList, fromDetail } = this.data

        // 如果是从详情页过来的，优先使用正在对比的职位ids来设置勾选状态
        let checkedStateMap = new Map()

        if (fromDetail) {
          // 从详情页过来，使用comparingJobIds设置勾选状态
          const comparingJobIds = wx.getStorageSync("comparingJobIds") || []
          comparingJobIds.forEach((id) => {
            checkedStateMap.set(id, true)
          })
        } else {
          // 正常情况，保存当前已有数据的 isChecked 状态
          currentJobList.forEach((item) => {
            checkedStateMap.set(item.id, item.isChecked)
          })
        }

        // 对新数据进行处理：设置勾选状态
        arr.forEach((item) => {
          if (checkedStateMap.has(item.id)) {
            item.isChecked = checkedStateMap.get(item.id)
          } else {
            item.isChecked = false
          }
        })
      }
      this.setData({
        jobList: arr,
      })
      // 初始化全选状态
      this.updateAllSelectedState()
    }
  },

  /**
   * 跳转到添加职位页面
   */
  onNavigateToAddJob() {
    wx.navigateTo({
      url: "/pages/job/addJob/index",
    })
  },

  /**
   * 更新全选状态
   */
  updateAllSelectedState() {
    const { jobList } = this.data
    const selectedCount = jobList.filter((item) => item.isChecked).length
    const isAllSelected = selectedCount === jobList.length && jobList.length > 0

    this.setData({
      isAllSelected,
    })
  },

  /**
   * 子组件选择事件
   */
  onCardSelect(e) {
    const { id, isChecked } = e.detail
    const { jobList } = this.data

    const newJobList = jobList.map((item) => {
      if (item.id === id) {
        return { ...item, isChecked }
      }
      return item
    })

    this.setData({
      jobList: newJobList,
    })

    // 更新全选状态
    this.updateAllSelectedState()
  },

  /**
   * 子组件删除事件
   */
  onCardDelete(e) {
    const { id } = e.detail
    this.setData({
      deleteId: id,
      modalShow: true,
    })
  },

  /**
   * 全选/取消全选
   */
  onToggleSelectAll() {
    const { jobList, isAllSelected } = this.data
    const newIsAllSelected = !isAllSelected

    const newJobList = jobList.map((item) => ({
      ...item,
      isChecked: newIsAllSelected,
    }))

    this.setData({
      jobList: newJobList,
      isAllSelected: newIsAllSelected,
    })
  },

  /**
   * 对比按钮点击
   */
  onCompare() {
    const selectedJobs = this.data.jobList.filter((item) => item.isChecked)
    if (selectedJobs.length === 0) {
      // wx.showToast({
      //   title: "请选择要对比的职位",
      //   icon: "none",
      // })
      return
    }
    if (selectedJobs.length === 1) {
      wx.showToast({
        title: "至少选择两个职位进行对比",
        icon: "none",
      })
      return
    }

    // 获取选中的职位ids
    const ids = selectedJobs.map((item) => item.id)

    // 将正在对比的职位ids存入缓存
    wx.setStorageSync("comparingJobIds", ids)

    // 检查是否是从详情页过来的（有fromDetail标记）
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const fromDetail = currentPage.data?.fromDetail || false

    if (fromDetail) {
      // 如果是从详情页过来的，使用navigateBack返回详情页
      wx.navigateBack()
    } else {
      // 否则正常跳转到详情页
      ROUTER.navigateTo({
        path: "/pages/job/comparisonDetail/index",
      })
    }
  },

  /**
   * 确认删除
   */
  confirmDelete() {
    const { deleteId, jobList, pkListIds } = this.data
    console.log("确认删除职位:", deleteId)

    const newIdsList = pkListIds.filter((item) => item != deleteId)
    const newJobList = jobList.filter((item) => item.id != deleteId)

    wx.setStorageSync("pkListIds", newIdsList)
    this.setData({
      jobList: newJobList,
      modalShow: false,
      deleteId: null,
      pkListIds: newIdsList,
    })

    // 更新全选状态
    this.updateAllSelectedState()
  },

  /**
   * 取消删除
   */
  cancelDelete() {
    this.setData({
      modalShow: false,
      deleteId: null,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
})
