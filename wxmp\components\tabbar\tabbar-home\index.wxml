<tabbar-box zIndex="100" wx:if="{{menus.length>0}}">
  <slot></slot>
  <view class="tabbar-list">
    <view class="tabbar-list-item" wx:for="{{menus}}" wx:key="index" data-item="{{item}}" data-index="{{index}}" catchtap="onChange">
      <view class="icon-box">
        <image wx:if="{{active===item.pathKey}}" src="{{item.selectedIconPath}}" mode="aspectFit" class="icon" />
        <image wx:else src="{{item.iconPath}}" mode="aspectFit" class="icon" />
      </view>
      <text class="tabbar-text {{active===item.pathKey?'active':''}}">{{item.text}}</text>
    </view>
  </view>
</tabbar-box>