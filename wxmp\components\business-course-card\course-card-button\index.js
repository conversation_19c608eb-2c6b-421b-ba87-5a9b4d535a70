// pages/course/components/list/course-card-button/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    text: "",
    usability: { type: String, value: "" },
    isQualification: { type: Boolean, value: false },
    // 是否参与拼团
    isJoinGroupon: {
      type: Boolean,
      value: false,
    },
    // 是否关联拼团
    isHasGroupon: {
      type: Boolean,
      value: false,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {},
})
