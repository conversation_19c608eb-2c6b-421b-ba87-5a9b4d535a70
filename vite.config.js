import { fileURLToPath, URL } from "node:url"

import { defineConfig, loadEnv } from "vite"
import vue from "@vitejs/plugin-vue"

import AutoImport from "unplugin-auto-import/vite"
import Components from "unplugin-vue-components/vite"
import { VantResolver } from "@vant/auto-import-resolver"

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), "")

  return {
    base: "/position/",
    plugins: [
      vue({
        template: {
          compilerOptions: {
            // 将微信开放标签视为自定义元素
            isCustomElement: (tag) => tag.startsWith("wx-"),
          },
        },
      }),
      AutoImport({
        resolvers: [VantResolver()],
      }),
      Components({
        resolvers: [VantResolver()],
      }),
    ],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    build: {
      target: "es2015", // 设置构建目标为 ES5
      outDir: "dist/position/",
      reportCompressedSize: false, // 启用构建报告
      sourcemap: false, // 构建后是否生成 source map 文件
      // emptyOutDir: true, // 构建时清空该目录
      chunkSizeWarningLimit: 550, // 单位kb  打包后文件大小警告的限制 (文件大于此此值会出现警告)
      assetsInlineLimit: 4096, // 单位字节（1024等于1kb） 小于此阈值的导入或引用资源将内联为 base64 编码，以避免额外的 http 请求。设置为 0 可以完全禁用此项。
    },
    // CSS配置
    css: {
      preprocessorOptions: {
        scss: {
          // 全局SCSS变量和混入
          additionalData: `
          @import "@/assets/styles/variables.scss";
          @import "@/assets/styles/mixins.scss";
        `,
          // 使用现代API
          api: "modern-compiler",
        },
      },
    },

    server: {
      open: true,
      proxy: {
        "/proxy": {
          target: env.VITE_PROXY_TARGET,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/proxy/, ""),
        },
      },
    },
  }
})
