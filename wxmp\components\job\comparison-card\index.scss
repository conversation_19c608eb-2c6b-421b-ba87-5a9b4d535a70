.card-item {
  padding: 32rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  width: 100%;
  overflow: hidden;

  .select-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 32rpx;
    flex-shrink: 0;
  }

  .right-box {
    flex: 1;
    min-width: 0; // 确保flex子元素可以收缩

    .top-area {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;

      .title {
        font-size: 32rpx;
        color: #22242e;
        font-weight: bold;
        flex: 1;
        margin-right: 16rpx;
        min-width: 0; // 确保标题也能正确省略
      }

      .icon {
        width: 32rpx;
        height: 32rpx;
        flex-shrink: 0;
      }
    }

    .main-area {
      width: 100%;

      .list-item {
        width: 100%;
        display: flex;
        align-items: flex-start;
        margin-bottom: 16rpx;

        .title {
          font-size: 24rpx;
          color: #919499;
          width: 100rpx;
          margin-right: 32rpx;
          flex-shrink: 0;
        }

        .text {
          flex: 1;
          font-size: 24rpx;
          color: #3c3d42;
          min-width: 0; // 确保文本可以收缩
          word-wrap: break-word;
          word-break: break-all;

          &.text-ellipsis-1 {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &.text-ellipsis-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.4;
          }
        }
      }

      .list-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16rpx;

        .text {
          font-size: 22rpx;
          color: #c2c5cc;
        }
      }
    }
  }
}
