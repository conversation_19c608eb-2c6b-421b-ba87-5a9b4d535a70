const ROUTER = require("@/services/mpRouter")
const APP = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 设备信息
    statusBarHeight: 0, // 状态栏高度
    showFixedHeader: false, // 是否显示固定头部

    // 查询结果相关
    showSearchResult: false, // 控制查询结果表格显示/隐藏
    searchResultList: [], // 查询结果数据

    // 选择框相关数据
    // 考试类型选择框
    examTypeVisible: false,
    examTypeOptions: [
      { name: "公务员考试" },
      { name: "事业单位考试" },
      { name: "教师招聘" },
      { name: "医疗卫生" },
      { name: "银行招聘" },
      { name: "国企招聘" },
    ],
    selectedExamType: "考试类型",

    // 所属地区选择框
    regionVisible: false,
    regionOptions: [
      { name: "重庆" },
      { name: "四川" },
      { name: "贵州" },
      { name: "云南" },
      { name: "广西" },
      { name: "湖南" },
    ],
    selectedRegion: "所属地区",

    // 具体考试选择框
    specificExamVisible: false,
    specificExamOptions: [
      { name: "重庆事业单位联考" },
      { name: "重庆公务员考试" },
      { name: "重庆教师招聘" },
      { name: "重庆医疗卫生" },
      { name: "重庆银行招聘" },
    ],
    selectedSpecificExam: "重庆事业单位联考",

    // 搜索框内选择器数据
    searchRegionVisible: false,
    searchRegionOptions: [
      { name: "重庆" },
      { name: "四川" },
      { name: "贵州" },
      { name: "内蒙古自治区" },
      { name: "广西" },
      { name: "湖南" },
    ],
    selectedSearchRegion: "内蒙古自治区",

    searchUnitVisible: false,
    searchUnitOptions: [
      { name: "全部" },
      { name: "市级机关" },
      { name: "区县机关" },
      { name: "乡镇机关" },
      { name: "事业单位" },
      { name: "国企单位" },
    ],
    selectedSearchUnit: "全部",

    // Tab相关数据
    tabList: [
      { name: "2025年", id: "year2025" },
      { name: "2024年上", id: "year2024_1" },
      { name: "2024一季度", id: "year2024_q1" },
      { name: "2023年", id: "year2023" },
      { name: "2022年", id: "year2022" },
    ],
    currentTab: 0,
    scrollLeft: 0,
    scrollBarWidth: 0,
    scrollBarLeft: 0,
    tabItemWidth: 0, // 初始化为0，后续获取实际宽度
    containerWidth: 0,

    // 数据统计内容
    tabDataList: [
      {
        announcement:
          "巫溪县事业单位2025念第二季度公开遴选工作人员27名公告遴选工作人员27名公告",
        totalApplicants: "298,390",
        maxUnitApplicants: "2678",
        maxCompetition: "6039:1",
        avgCompetition: "77:1",
        zeroApplicants: "27",
        lowCompetition: "95",
        updateTime: "2025年6月12日 15:35",
      },
      {
        announcement: "巫溪县事业单位2024年上半年公开遴选工作人员公告",
        totalApplicants: "256,420",
        maxUnitApplicants: "2156",
        maxCompetition: "4521:1",
        avgCompetition: "65:1",
        zeroApplicants: "18",
        lowCompetition: "82",
        updateTime: "2024年6月15日 16:20",
      },
      {
        announcement: "巫溪县事业单位2024年一季度公开遴选工作人员公告",
        totalApplicants: "189,350",
        maxUnitApplicants: "1832",
        maxCompetition: "3456:1",
        avgCompetition: "52:1",
        zeroApplicants: "12",
        lowCompetition: "67",
        updateTime: "2024年3月20日 14:30",
      },
      {
        announcement: "巫溪县事业单位2023年公开遴选工作人员公告",
        totalApplicants: "234,670",
        maxUnitApplicants: "2234",
        maxCompetition: "5123:1",
        avgCompetition: "68:1",
        zeroApplicants: "21",
        lowCompetition: "78",
        updateTime: "2023年12月10日 17:45",
      },
      {
        announcement: "巫溪县事业单位2022年公开遴选工作人员公告",
        totalApplicants: "198,450",
        maxUnitApplicants: "1987",
        maxCompetition: "4687:1",
        avgCompetition: "58:1",
        zeroApplicants: "15",
        lowCompetition: "71",
        updateTime: "2022年11月25日 16:15",
      },
    ],

    currentTabData: null, // 添加这个字段

    resultList: [
      {
        title: "文字综合",
        isFocus: false,
        zhaolu: "1人",
        baokao: "402人",
        jiaofei: "300人",
        jingzheng: "402:1",
        diqu: "兴安盟",
      },
      {
        title: "办公综合",
        isFocus: true,
        zhaolu: "1人",
        baokao: "402人",
        jiaofei: "300人",
        jingzheng: "402:1",
        diqu: "兴安盟",
      },
    ],

    // vant选择器状态
    optPopShow: false,
    optTitle: "",
    optList: [],
    optKeys: [], // 选项对应的key数组
    value: [],
    currentSelector: "", // 当前激活的选择器类型

    // 选择器数据配置
    selectorConfig: {
      examType: {
        title: "选择考试类型",
        options: [
          { key: "gwy", name: "国家公务员考试" },
          { key: "cqgwy", name: "重庆公务员考试" },
          { key: "sydw", name: "事业单位考试" },
          { key: "jszp", name: "教师招聘考试" },
          { key: "yhzp", name: "银行招聘考试" },
        ],
        currentKey: "",
      },
      region: {
        title: "选择所属地区",
        options: [
          { key: "", name: "全部" },
          { key: "cq", name: "重庆市" },
          { key: "wz", name: "万州区" },
          { key: "fl", name: "涪陵区" },
          { key: "yz", name: "渝中区" },
          { key: "ddk", name: "大渡口区" },
          { key: "jb", name: "江北区" },
          { key: "spb", name: "沙坪坝区" },
        ],
        currentKey: "",
      },
      specificExam: {
        title: "选择具体考试",
        options: [
          { key: "cqgwy2025", name: "2025年重庆市公务员考试" },
          { key: "cqsydw2024", name: "2024年重庆市事业单位考试" },
          { key: "cqjszp2024", name: "2024年重庆市教师招聘考试" },
        ],
        currentKey: "",
      },
      searchRegion: {
        title: "选择地区",
        options: [
          { key: "", name: "全部地区" },
          { key: "wz", name: "万州区" },
          { key: "fl", name: "涪陵区" },
          { key: "yz", name: "渝中区" },
          { key: "ddk", name: "大渡口区" },
        ],
        currentKey: "",
      },
      searchUnit: {
        title: "选择用人单位",
        options: [
          { key: "", name: "全部单位" },
          { key: "sjjg", name: "市级机关" },
          { key: "qxjg", name: "区县机关" },
          { key: "sydw", name: "事业单位" },
          { key: "gyqy", name: "国有企业" },
        ],
        currentKey: "",
      },
    },

    // 当前选中的key值
    selectedExamTypeKey: "",
    selectedRegionKey: "",
    selectedSpecificExamKey: "",
    selectedSearchRegionKey: "",
    selectedSearchUnitKey: "",

    // 显示用的文本值
    selectedExamTypeText: "考试类型",
    selectedRegionText: "所属地区",
    selectedSpecificExamText: "重庆事业单位联考",
    selectedSearchRegionText: "全部地区",
    selectedSearchUnitText: "全部",

    // 职位输入框的值
    positionInput: "",

    // 数据说明展开收起状态
    dataDescExpanded: false,

    // 图表数据
    scoreData: {
      bins: [
        {
          label: "重庆",
          minScore: 145,
          maxScore: 175,
          avgScore: 160,
          count: 160, // 用于柱状图高度
        },
        {
          label: "四川",
          minScore: 162,
          maxScore: 182,
          avgScore: 168,
          count: 168,
        },
        {
          label: "湖北",
          minScore: 138,
          maxScore: 168,
          avgScore: 155,
          count: 155,
        },
        {
          label: "湖南",
          minScore: 142,
          maxScore: 172,
          avgScore: 158,
          count: 158,
        },
        {
          label: "广东",
          minScore: 165,
          maxScore: 185,
          avgScore: 175,
          count: 175,
        },
        {
          label: "贵州",
          minScore: 135,
          maxScore: 165,
          avgScore: 152,
          count: 152,
        },
        {
          label: "新疆",
          minScore: 125,
          maxScore: 155,
          avgScore: 140,
          count: 140,
        },
      ],
      myBinIndex: -1,
      score_desired_number: 7,
    },

    isChartClicked: false, // 添加图表点击状态
  },

  /**
   * 计算属性 - 当前tab数据
   */
  get currentTabData() {
    return (
      this.data.tabDataList[this.data.currentTab] || this.data.tabDataList[0]
    )
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取设备信息
    this.getSystemInfo()

    // 初始化当前tab数据
    this.setData({
      currentTabData: this.data.tabDataList[0],
    })
  },

  /**
   * 获取设备信息
   */
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync()
    console.log("设备信息:", systemInfo)
    // 计算导航栏相关高度
    const statusBarHeight = systemInfo.statusBarHeight || 0

    console.log("状态栏高度:", statusBarHeight)

    this.setData({
      statusBarHeight: statusBarHeight,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 初始化滚动条
    setTimeout(() => {
      this.initScrollBar()
    }, 100)
  },

  /**
   * 初始化滚动条
   */
  initScrollBar() {
    console.log("开始初始化滚动条")
    const query = wx.createSelectorQuery()

    // 先获取容器宽度
    query
      .select(".tab-container")
      .boundingClientRect((containerRect) => {
        console.log("容器信息:", containerRect)
        if (containerRect) {
          // 再获取tab项宽度
          const itemQuery = wx.createSelectorQuery()
          itemQuery
            .select(".tab-item")
            .boundingClientRect((itemRect) => {
              console.log("tab项信息:", itemRect)
              if (itemRect) {
                this.setData(
                  {
                    containerWidth: containerRect.width,
                    tabItemWidth: itemRect.width,
                  },
                  () => {
                    console.log("滚动数据设置完成:", {
                      containerWidth: this.data.containerWidth,
                      tabItemWidth: this.data.tabItemWidth,
                    })
                    this.updateScrollBar()
                  }
                )
              } else {
                console.log("无法获取tab项宽度，使用默认值")
                // 如果无法获取，使用默认计算值
                const systemInfo = wx.getSystemInfoSync()
                const defaultItemWidth = (180 * systemInfo.windowWidth) / 750 // rpx转px
                this.setData(
                  {
                    containerWidth: containerRect.width,
                    tabItemWidth: defaultItemWidth,
                  },
                  () => {
                    this.updateScrollBar()
                  }
                )
              }
            })
            .exec()
        }
      })
      .exec()
  },

  /**
   * 更新滚动条位置 - 修复双向滚动问题
   */
  updateScrollBar() {
    const { currentTab, tabList, containerWidth, tabItemWidth } = this.data

    if (!containerWidth || !tabItemWidth) return

    const totalWidth = tabList.length * tabItemWidth

    // 只有当总宽度大于容器宽度时才需要滚动
    if (totalWidth > containerWidth) {
      // 计算当前tab的中心位置
      const currentTabCenter = currentTab * tabItemWidth + tabItemWidth / 2

      // 计算滚动位置，让当前tab居中
      let targetScrollLeft = currentTabCenter - containerWidth / 2

      // 边界处理
      const maxScrollLeft = totalWidth - containerWidth
      targetScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft))

      console.log("滚动调试信息:", {
        currentTab,
        currentTabCenter,
        containerWidth,
        tabItemWidth,
        totalWidth,
        targetScrollLeft,
        maxScrollLeft,
      })

      this.setData({
        scrollLeft: targetScrollLeft,
      })
    }
  },

  /**
   * 获取当前tab数据
   */
  getCurrentTabData() {
    return (
      this.data.tabDataList[this.data.currentTab] || this.data.tabDataList[0]
    )
  },

  /**
   * Tab点击事件
   */
  onTabClick(e) {
    const index = e.currentTarget.dataset.index
    console.log("点击tab:", index, "当前tab:", this.data.currentTab)

    this.setData({
      currentTab: index,
      currentTabData: this.data.tabDataList[index],
    })

    // 延迟执行滚动，确保数据更新完成
    setTimeout(() => {
      this.updateScrollBar()
    }, 50)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 初始化胶囊按钮颜色
    if (this.data.showFixedHeader) {
      // 白色背景时，胶囊按钮显示黑色
      wx.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff",
      })
    } else {
      // 深色背景时，胶囊按钮显示白色
      wx.setNavigationBarColor({
        frontColor: "#ffffff",
        backgroundColor: "#000000",
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 页面滚动事件
   */
  onPageScroll(e) {
    const scrollTop = e.scrollTop
    const showFixedHeader = scrollTop > 10 // 滚动超过10rpx显示固定头部

    console.log(
      "页面滚动:",
      scrollTop,
      "是否显示头部:",
      showFixedHeader,
      "当前状态:",
      this.data.showFixedHeader
    )

    if (showFixedHeader !== this.data.showFixedHeader) {
      if (!showFixedHeader) {
        wx.setNavigationBarColor({
          frontColor: "#ffffff",
          backgroundColor: "#000000",
        })
      } else {
        wx.setNavigationBarColor({
          frontColor: "#000000",
          backgroundColor: "#ffffff",
        })
      }
      this.setData({
        showFixedHeader: showFixedHeader,
      })
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  /**
   * 显示选择器
   */
  showSelector(type) {
    const config = this.data.selectorConfig[type]
    if (config) {
      // 将options转换为vant picker需要的格式
      const optList = config.options.map((item) => item.name)
      const optKeys = config.options.map((item) => item.key)

      // 获取当前选中的key并找到对应的索引
      let currentKey = ""
      switch (type) {
        case "examType":
          currentKey = this.data.selectedExamTypeKey
          break
        case "region":
          currentKey = this.data.selectedRegionKey
          break
        case "specificExam":
          currentKey = this.data.selectedSpecificExamKey
          break
        case "searchRegion":
          currentKey = this.data.selectedSearchRegionKey
          break
        case "searchUnit":
          currentKey = this.data.selectedSearchUnitKey
          break
      }

      // 根据key找到对应的索引
      const currentIndex = optKeys.findIndex((key) => key === currentKey)
      const value = currentIndex >= 0 ? [currentIndex] : [0] // 如果找不到则默认选中第一项

      console.log(
        "showSelector:",
        type,
        "当前key:",
        currentKey,
        "选项keys:",
        optKeys,
        "选项列表:",
        optList,
        "找到索引:",
        currentIndex
      )

      this.setData({
        currentSelector: type,
        optPopShow: true,
        optTitle: config.title,
        optList: optList,
        optKeys: optKeys, // 保存keys数组
        value: value,
      })
    }
  },

  /**
   * 考试类型点击事件
   */
  onExamTypeClick() {
    this.showSelector("examType")
  },

  /**
   * 所属地区点击事件
   */
  onRegionClick() {
    this.showSelector("region")
  },

  /**
   * 具体考试点击事件
   */
  onSpecificExamClick() {
    this.showSelector("specificExam")
  },

  /**
   * 搜索地区点击事件
   */
  onSearchRegionClick() {
    this.showSelector("searchRegion")
  },

  /**
   * 搜索单位点击事件
   */
  onSearchUnitClick() {
    this.showSelector("searchUnit")
  },

  /**
   * 职位输入框输入事件
   */
  onPositionInput(event) {
    const value = event.detail.value
    console.log("职位输入:", value)

    this.setData({
      positionInput: value,
    })
  },

  onConfirm(event) {
    const { value } = event.detail
    const type = this.data.currentSelector
    const optList = this.data.optList
    const optKeys = this.data.optKeys

    const selectedIndex = optList.findIndex((item) => item == value)
    const selectedKey = optKeys[selectedIndex]
    const selectedText = value

    // 验证选中值是否有效
    if (selectedKey === undefined || !selectedText) {
      console.error("选中值无效:", selectedIndex, optKeys, optList)
      return
    }

    // 根据当前选择器类型更新对应的数据
    const updateData = {
      optPopShow: false,
      currentSelector: "",
      optList: [], // 清空选项列表
      optKeys: [], // 清空keys列表
      value: [], // 清空选中值
    }

    switch (type) {
      case "examType":
        updateData.selectedExamTypeKey = selectedKey
        updateData.selectedExamTypeText = selectedText
        break
      case "region":
        updateData.selectedRegionKey = selectedKey
        updateData.selectedRegionText = selectedText
        break
      case "specificExam":
        updateData.selectedSpecificExamKey = selectedKey
        updateData.selectedSpecificExamText = selectedText
        break
      case "searchRegion":
        updateData.selectedSearchRegionKey = selectedKey
        updateData.selectedSearchRegionText = selectedText
        break
      case "searchUnit":
        updateData.selectedSearchUnitKey = selectedKey
        updateData.selectedSearchUnitText = selectedText
        break
      default:
        console.warn("未知的选择器类型:", type)
        return
    }

    console.log("最终更新数据:", updateData)
    this.setData(updateData)
  },

  /**
   * vant popup关闭
   */
  OptClose() {
    console.log("选择器关闭")
    this.setData({
      optPopShow: false,
      currentSelector: "",
    })
  },

  /**
   * 返回按钮点击事件
   */
  onBackClick() {
    console.log("返回按钮点击")
    APP.backPage()
  },

  /**
   * 查询按钮点击事件
   */
  onSearchClick() {
    console.log("查询按钮点击")

    // 模拟查询结果数据
    const mockSearchResults = [
      {
        positionName: "综合类事务工作人员",
        unitName: "人民银行湖州中心支行",
        competitionRatio: "149.5",
      },
      {
        positionName: "综合类事务工作人员",
        unitName: "人民银行湖州中心支行",
        competitionRatio: "149.5",
      },
      {
        positionName: "综合类事务工作人员",
        unitName: "人民银行湖州中心支行",
        competitionRatio: "149.5",
      },
      {
        positionName: "综合类事务工作人员",
        unitName: "人民银行湖州中心支行",
        competitionRatio: "149.5",
      },
      {
        positionName: "综合类事务工作人员",
        unitName: "人民银行湖州中心支行",
        competitionRatio: "149.5",
      },
      {
        positionName: "综合类事务工作人员",
        unitName: "人民银行湖州中心支行",
        competitionRatio: "149.5",
      },
      {
        positionName: "综合类事务工作人员",
        unitName: "人民银行湖州中心支行",
        competitionRatio: "149.5",
      },
    ]

    // 显示查询结果表格
    this.setData({
      showSearchResult: true,
      searchResultList: mockSearchResults,
    })
  },

  /**
   * 清除按钮点击事件
   */
  onClearClick() {
    console.log("清除筛选条件")
    this.setData({
      selectedSearchRegionKey: "",
      selectedSearchRegionText: "全部地区",
      selectedSearchUnitKey: "",
      selectedSearchUnitText: "全部",
      positionInput: "",
    })
  },

  goDetail() {
    ROUTER.navigateTo({
      path: "/pages/notice/detail/index",
    })
  },
  checkNews() {
    ROUTER.navigateTo({
      path: "/pages/notice/detail/index",
    })
  },
  goJobs() {
    ROUTER.navigateTo({
      path: "/pages/webview/activityCustomer/index",
      query: {
        event: "zKBr",
      },
    })
  },
  goWeb(e) {
    const query = wx.createSelectorQuery().in(this)
    query.select("#job-search-section").boundingClientRect()
    query.selectViewport().scrollOffset()
    query.select(".fixed-header").boundingClientRect()

    query.exec((res) => {
      if (res && res[0] && res[1] && res[2]) {
        const targetTop = res[0].top
        const scrollTop = res[1].scrollTop
        const headerRect = res[2]

        // 根据当前滚动位置判断头部是否应该显示
        const shouldShowHeader = scrollTop > 10
        let headerHeight = shouldShowHeader ? headerRect.height : 0

        // 如果头部不显示，需要减去状态栏高度 + 导航栏高度
        if (!shouldShowHeader) {
          // 导航栏高度 = 状态栏高度 + 44px（导航栏内容高度）
          headerHeight = this.data.statusBarHeight + 44
        }

        const finalScrollTop = scrollTop + targetTop - headerHeight

        wx.pageScrollTo({
          scrollTop: finalScrollTop,
          duration: 300,
        })
      }
    })
  },

  /**
   * 数据说明展开收起切换
   */
  onToggleDataDesc() {
    const expanded = this.data.dataDescExpanded
    this.setData({
      dataDescExpanded: !expanded,
    })
  },

  // 图表点击事件处理
  onChartClick(e) {
    // 移除旧的点击事件处理
  },

  // 隐藏tooltip
  hideTooltip() {
    // 移除旧的hideTooltip处理
  },

  onPageClick(e) {
    const path = e.currentTarget.dataset.path
    if (path === "root") {
      const chartComponent = this.selectComponent("#score-bar-id-multiple")
      if (chartComponent) {
        const chart = chartComponent.chart
        if (chart) {
          chart.dispatchAction({
            type: "hideTip",
          })
        }
      }
    }
  },
})
