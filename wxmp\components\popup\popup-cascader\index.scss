.popup-header {
  height: 130rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  color: #3c3d42;
}
.cascader-popup {
  .close-icon-class {
    top: 40rpx !important;
  }
}

.cascader {
  display: flex;
  flex-direction: column;
  height: 70vh;
}

.tabs {
  display: flex;
  white-space: nowrap;
  // border-bottom: 1px solid #e5e5e5;

  .tab-item {
    padding: 10px 20px;
    font-size: 16px;
    color: #333;
    &.active {
      color: #d62828;
      font-weight: bold;
    }
  }
}

.list {
  flex: 1;
  overflow-y: auto;
  background-color: #fff;
}

.option {
  padding: 10px 15px;
  font-size: 28rpx;
}
