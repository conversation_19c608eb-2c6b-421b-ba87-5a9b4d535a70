/* pages/select/select-job/index.wxss */
page {
  background-color: #fff;
  height: 100vh;
  box-sizing: border-box;
}

.lefts {
  padding-left: 32rpx;

  .left-arrow {
    width: 40rpx;
    height: 40rpx;
  }
}

.main-content {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .select-box {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
  }
}

/* 已选择筛选条件样式 */
.select-top {
  padding: 24rpx 160rpx 24rpx 32rpx;
  position: relative;
  border-bottom: 1rpx solid rgba(235, 236, 240, 1);

  .select-list {
    display: flex;
    align-items: center;
    white-space: nowrap;

    &-item {
      font-size: 22rpx;
      color: rgba(230, 0, 3, 1);
      background: rgba(230, 0, 3, 0.05);
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 16rpx 0 24rpx;
      height: 64rpx;
      border-radius: 12rpx;
      margin-right: 16rpx;

      .close {
        width: 24rpx;
        height: 24rpx;
        margin-left: 16rpx;
        transform: translateY(3rpx);
      }
    }
  }

  .clear-box {
    position: absolute;
    display: flex;
    align-items: center;
    font-size: 26rpx;
    color: rgba(60, 61, 66, 1);
    background: #fff;
    top: 0;
    right: 0;
    height: 100%;
    padding: 0 32rpx 0 44rpx;

    &::after {
      display: block;
      content: " ";
      width: 2rpx;
      height: 64rpx;
      background: rgba(235, 236, 240, 1);
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 22rpx;
    }

    .img {
      width: 32rpx;
      height: 32rpx;
      transform: translateY(1rpx);
      margin-right: 4rpx;
    }
  }
}

/* 主体内容样式 */
.main-container {
  flex: 1;
  min-height: 0;
  display: flex;
  height: calc(100vh - 200rpx);
  /* 减去顶部导航和底部按钮的高度 */
}

/* 左侧类别列表样式 */
.category-list {
  width: 240rpx;
  background-color: rgba(247, 248, 250, 0.5);
  // border-right: 1rpx solid rgba(235, 236, 240, 1);
  position: relative;

  &::after {
    position: absolute;
    content: "";
    display: block;
    width: 1rpx;
    height: 100%;
    background: rgba(235, 236, 240, 1);
    right: 1rpx;
    top: 0;
  }

  /* 一级分组标题样式 */
  .group-title {
    background-color: rgba(247, 248, 250, 0.5);
    height: 84rpx;
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    font-size: 28rpx;
    color: rgba(194, 197, 204, 1);
    font-weight: bold;
    // border-bottom: 1rpx solid rgba(235, 236, 240, 1);
  }

  /* 二级分类项样式 */
  .category-item {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    padding: 0 24rpx 0 44rpx;
    /* 左侧缩进 */
    // border-bottom: 1rpx solid rgba(235, 236, 240, 1);
    background-color: rgba(247, 248, 250, 0.5);

    &.active {
      background-color: #ffffff;

      .category-name {
        color: rgba(230, 0, 3, 1) !important;
        font-weight: bold;
      }

      &::after {
        display: block;
        position: absolute;
        content: " ";
        width: 1rpx;
        height: 100%;
        background-color: #fff;
        top: 0;
        right: 1rpx;
        z-index: 2;
      }
    }

    .category-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .category-name {
        font-size: 26rpx;
        color: #333333;
      }

      .category-count {
        min-width: 32rpx;
        min-height: 32rpx;
        background: rgba(230, 0, 3, 0.1);
        font-size: 20rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(230, 0, 3, 1);
        font-family: "DINGBold";
      }
    }
  }
}

/* 右侧选项容器样式 */
.options-container {
  flex: 1;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.options-list {
  flex: 1;
  padding: 0;
  height: 100%;

  .category-section {
    &.wp100 {
      .option-item {
        width: 100%;
        margin-right: 0;
      }
    }
    .category-title {
      // background-color: #f8f9fa;
      padding: 32rpx;
      font-size: 26rpx;
      font-weight: bold;
      color: rgba(60, 61, 66, 1);
      // border-bottom: 1rpx solid #f0f0f0;

      .multi-label {
        font-size: 22rpx;
        font-weight: normal;
        color: rgba(153, 153, 153, 1);
        margin-left: 8rpx;
      }
    }

    .options-group {
      padding: 0 32rpx;
      display: flex;
      flex-wrap: wrap;
    }
  }

  .option-item {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 214rpx;
    height: 72rpx;
    background: rgba(247, 248, 250, 1);
    border: 1rpx solid rgba(235, 236, 240, 1);
    border-radius: 8rpx;
    margin-bottom: 16rpx;
    margin-right: 16rpx;
    position: relative;

    &:nth-child(2n) {
      margin-right: 0;
    }

    &:last-child {
      border-bottom: none;
    }

    &.selected {
      .option-text {
        color: rgba(230, 0, 3, 1);
        font-weight: bold;
      }
      border-color: transparent;
      background: rgba(230, 0, 3, 0.05);
    }

    .option-text {
      color: rgba(60, 61, 66, 1);
      flex: 1;
      font-size: 26rpx;
      text-align: center;
    }

    .recommend {
      width: 62rpx;
      height: 26rpx;
      position: absolute;
      top: 0;
      right: 0;
    }

    .option-indicator {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .check-icon {
        font-size: 24rpx;
        color: rgba(230, 0, 3, 1);
        font-weight: bold;
      }
    }
  }

  .loading-text,
  .empty-text {
    padding: 60rpx 24rpx;
    text-align: center;
    color: #999;
    font-size: 24rpx;
  }

  .empty-text {
    color: #ccc;
  }
}

/* 底部操作区域样式 */
.action-bar-box {
  display: flex;
  height: 142rpx;
  z-index: 998;
  position: relative;
  box-sizing: border-box;
}

.action-bar {
  z-index: 1;
  // position: fixed;
  // bottom: 0;
  // left: 0;
  width: 100%;
  height: 142rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-top: 1rpx solid #ebecf0;
  padding: 24rpx 40rpx 34rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.confirm-btn {
  background: rgba(236, 62, 51, 1);
  font-size: 28rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

// 通用样式
.container {
  max-width: 750rpx;
  margin: 0 auto;
}

.flex-justify_between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
