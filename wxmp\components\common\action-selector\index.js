Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹出框
    visible: {
      type: Boolean,
      value: false,
    },
    // 弹出框标题
    title: {
      type: String,
      value: "请选择",
    },
    // 选项数组
    options: {
      type: Array,
      value: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 设置较高的z-index确保弹出框在最上层
    zIndex: 9999,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 选择选项
     */
    onSelect(event) {
      const { value } = event.detail

      // 向父组件传递选择事件
      this.triggerEvent("select", {
        value,
      })
    },

    /**
     * 关闭弹出框
     */
    onClose() {
      console.log("弹出框关闭事件")

      // 向父组件传递关闭事件
      this.triggerEvent("close")
    },

    /**
     * 取消按钮点击
     */
    onCancel() {
      console.log("弹出框取消事件")

      // 向父组件传递取消事件
      this.triggerEvent("cancel")
    },
  },
})
