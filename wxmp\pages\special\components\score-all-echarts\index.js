import { getLearnOverview } from "@/config/api"
import * as echarts from "../../plugins/ec-canvas/echarts"

Component({
  properties: {
    scoreData: {
      type: Object,
      value: null,
      observer(newVal) {
        if (newVal && newVal.length > 0) {
          // 属性更新时重新初始化
          this.init()
        }
      },
    },
    activeRound: {
      type: Number,
      value: 0,
    },
    chartType: {
      type: String,
      value: "big-data", // 默认为big-data模式，可选值：'big-data' | 'interview-score'
    },
  },

  data: {
    chartWidth: 750,
    ec: {
      lazyLoad: true,
    },
    isChartClicked: false,
  },

  lifetimes: {
    attached() {
      // 组件挂载时初始化
      this.init()
    },
  },

  methods: {
    // ... existing code ...
    change() {},
    hideTooltip() {
      console.log("组件hideTooltip被调用")
      if (this.data.isChartClicked) {
        console.log("图表刚被点击，不关闭tooltip")
        return
      }
      const component = this.selectComponent("#mycharts")
      if (component && component.chart) {
        console.log("找到图表实例，执行hideTip")
        component.chart.dispatchAction({
          type: "hideTip",
        })
      } else {
        console.log("未找到图表实例")
      }
    },
    init() {
      const { scoreData } = this.properties
      if (!scoreData || scoreData.length === 0) return

      // 计算所需的总宽度
      const dataLength = scoreData.length
      let totalWidth

      if (dataLength <= 6) {
        // 少于等于6条数据时，使用屏幕宽度
        totalWidth = 750
      } else {
        // 超过6条数据时，每个柱子100rpx（25px）
        const itemWidth = 100 // 柱子宽度
        const marginWidth = 80 // 左右边距各40rpx
        totalWidth = dataLength * itemWidth + marginWidth
      }

      this.setData({ chartWidth: totalWidth }, () => {
        const query = wx.createSelectorQuery().in(this)
        query
          .select(".chart-container-inner")
          .boundingClientRect((rect) => {
            if (rect) {
              console.log("容器尺寸:", rect)
              this.initChart(rect.width, rect.height)
            }
          })
          .exec()
      })
    },
    initChart(width, height) {
      const { scoreData } = this.properties
      const component = this.selectComponent("#mycharts")
      if (!component) return

      component.init((canvas, canvasWidth, canvasHeight, dpr) => {
        const chart = echarts.init(canvas, null, {
          width: canvasWidth,
          height: canvasHeight,
          devicePixelRatio: dpr,
        })
        canvas.setChart(chart)

        // 计算柱子宽度和间距
        const dataLength = scoreData.length
        let barWidth = 25 // 将柱子宽度从30px减小到25px，确保一屏能完整显示6个

        const option = {
          backgroundColor: "#ffffff",
          grid: {
            left: 0,
            right: 20,
            top: 30,
            bottom: 30,
            containLabel: true,
          },
          tooltip: {
            trigger: "item",
            confine: true,
            triggerOn: "click",
            enterable: true,
            hideDelay: 100,
            axisPointer: {
              type: "shadow",
              shadowStyle: {
                color: "rgba(0,0,0,0.05)",
                shadowBlur: 0,
              },
            },
            shadowBlur: 0,
            shadowColor: "transparent",
            textStyle: {
              testShadowColor: "transparent",
              textShadowBlur: 10,
            },
            backgroundColor: "#FFFFFF",
            borderColor: "#E1E3E5",
            borderWidth: 1,
            padding: [8, 12],
            formatter: (param) => {
              const data = scoreData[param.dataIndex]
              const chartType = this.properties.chartType

              if (chartType === "interview-score") {
                // 进面分数模式
                return [
                  `{titleStyle|${data.area_name}}`,
                  `{labelStyle|最低进面分数线}  {valueStyle|${
                    data.minScore || "--"
                  }}`,
                  `{labelStyle|最高进面分数线}  {valueStyle|${
                    data.maxScore || "--"
                  }}`,
                  `{labelStyle|平均进面分数线}  {valueStyle|${
                    data.avgScore || "--"
                  }}`,
                ].join("\n")
              } else {
                // big-data模式（默认）
                return [
                  `{titleStyle|${data.area_name}}`,
                  `{labelStyle|职位数}  {valueStyle|${data.job_num}}`,
                  `{labelStyle|报名人数}  {valueStyle|${data.need_num}}`,
                  `{labelStyle|招录人数}  {valueStyle|${data.apply_num}}`,
                  `{labelStyle|竞争比}  {valueStyle|${data.competitive_rate}}`,
                ].join("\n")
              }
            },
            rich: {
              titleStyle: {
                fontSize: 12,
                fontWeight: 400,
                color: "#3C3D42",
                padding: [0, 0, 8, 0],
              },
              labelStyle: {
                fontSize: 11,
                color: "#919499",
                fontWeight: 400,
                padding: [0, 0],
              },
              valueStyle: {
                fontSize: 11,
                fontWeight: 400,
                color: "#3C3D42",
                padding: [4, 0],
                align: "right",
              },
            },
          },
          xAxis: {
            type: "category",
            data: scoreData.map((item) => item.area_name),
            axisLine: { lineStyle: { color: "#E5E5E5" } },
            axisTick: { show: false },
            axisLabel: {
              color: "#999",
              fontSize: 12,
              interval: 0,
            },
          },
          yAxis: {
            type: "value",
            splitLine: { lineStyle: { color: "#E5E5E5", type: "dashed" } },
            axisLine: { show: false },
            axisTick: { show: false },
            axisLabel: {
              color: "#999",
              fontSize: 12,
              margin: 12, // 增加文字与轴线的距离
              formatter: (value) => {
                const chartType = this.properties.chartType
                if (chartType === "interview-score") {
                  // 进面分数模式：直接显示数字
                  return value
                } else {
                  // big-data模式：显示k单位
                  if (value >= 1000) {
                    return value / 1000 + "k"
                  }
                  return value
                }
              },
            },
            nameGap: 25,
            offset: 0,
            splitNumber: 6, // 减少分割段数，增加每段之间的间距
            boundaryGap: ["20%", "20%"], // 上下留白，拉伸显示区域
          },
          series: [
            {
              type: "bar",
              barWidth: barWidth,
              barCategoryGap: "35%", // 减小柱子之间的间距，确保能完整显示6个柱子
              data: scoreData.map((item) => {
                const chartType = this.properties.chartType
                if (chartType === "interview-score") {
                  // 进面分数模式：使用平均进面分数据
                  return item.平均进面分 || item.avgScore || item.apply_num || 0
                } else {
                  // big-data模式：使用count数据
                  return item.apply_num
                }
              }),
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(248, 78, 78, 1)" },
                  { offset: 1, color: "rgba(248, 78, 78, 0.60)" },
                ]),
                barBorderRadius: [6, 6, 0, 0],
              },
              label: {
                show: false,
                position: "top",
                fontSize: 10,
                color: "#666",
                backgroundColor: "#fff",
                borderColor: "#C2C5CC",
                // borderWidth: 1,
                borderRadius: 4,
                padding: [4, 4],
                formatter: function (params) {
                  return ""
                },
              },
              backgroundStyle: {
                color: "rgba(180, 180, 180, 0.2)",
              },
              barMinHeight: 5,
            },
          ],
        }

        chart.setOption(option)
        component.chart = chart
        this.chart = chart

        chart.on("click", (params) => {
          console.log(params, "dayin")
          this.setData({ isChartClicked: true })
          if (params.componentType === "series") {
            chart.dispatchAction({
              type: "showTip",
              seriesIndex: 0,
              dataIndex: params.dataIndex,
            })
          } else {
            chart.dispatchAction({
              type: "hideTip",
            })
          }
          setTimeout(() => {
            this.setData({ isChartClicked: false })
          }, 200)
        })
        return chart
      })
    },
    getMaxCountDividedAndCeil(arr, b) {
      if (
        !Array.isArray(arr) ||
        arr.length === 0 ||
        typeof b !== "number" ||
        b <= 0
      ) {
        throw new Error("参数不合法")
      }

      // 找出 apply_num 的最大值
      const maxCount = Math.max(...arr.map((item) => item.apply_num))

      // 向上取整 (maxCount / b)
      const result = Math.ceil(maxCount / b)

      return result
    },
    // ... existing code ...
  },
})
