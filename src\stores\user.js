import { defineStore } from "pinia"
import { userApi } from "@/api/modules/user"
import { getUserTokenCookie, setUserTokenCookie } from "@/utils/cookies/modules/user"

/**
 * 用户状态管理Store
 * 职责：用户信息、登录状态、认证管理
 *
 * @typedef {Object} UserInfo
 * @property {string} ucid - 用户唯一标识
 * @property {string} name - 用户姓名
 * @property {string} phone - 用户手机号
 * @property {string} email - 用户邮箱
 * @property {string} avatar - 用户头像
 * @property {number} status - 用户状态
 * @property {Date} lastLoginTime - 最后登录时间
 *
 * @typedef {Object} UserState
 * @property {string} token - 用户认证令牌
 * @property {UserInfo} userInfo - 用户信息对象
 * @property {boolean} isLogin - 是否已登录
 * @property {boolean} isTourist - 是否为游客模式
 */
export const useUserStore = defineStore("user", {
  state: () => ({
    // 用户认证信息
    token: getUserTokenCookie(),
    userInfo: {},
    isLogin: false,
    isTourist: true,
  }),

  getters: {
    // 用户ID
    userId: (state) => state.userInfo?.ucid || "",

    // 用户名称
    userName: (state) => state.userInfo?.name || "",

    // 用户手机号
    userPhone: (state) => state.userInfo?.phone || "",

    // 用户邮箱
    userEmail: (state) => state.userInfo?.email || "",

    // 用户头像
    userAvatar: (state) => state.userInfo?.avatar || "",

    // 是否已认证
    isAuthenticated: (state) => !!state.token && !!state.userInfo?.ucid,

    // 用户显示名称
    displayName: (state) => {
      if (state.userInfo?.name) return state.userInfo.name
      if (state.userInfo?.phone) {
        return state.userInfo.phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2")
      }
      if (state.userInfo?.email) {
        return state.userInfo.email.replace(/(.{2}).*(@.*)/, "$1***$2")
      }
      return "用户"
    },
  },

  actions: {
    /**
     * 用户登录
     * @param {Object} credentials - 登录凭据
     * @param {string} credentials.username - 用户名/手机号/邮箱
     * @param {string} credentials.password - 密码
     * @param {boolean} [credentials.remember] - 是否记住登录
     * @returns {Promise<{success: boolean, data?: any, error?: string}>}
     */
    async login(credentials) {
      try {
        const response = await userApi.login(credentials)
        const { token, userInfo } = response?.data || {}

        if (token) {
          this.setUserToken(token)
        }

        if (userInfo) {
          this.setUserInfo(userInfo)
        }

        return { success: true, data: response.data }
      } catch (error) {
        console.error("登录失败:", error)
        return { success: false, error: error.message }
      }
    },

    /**
     * 获取用户信息
     * @returns {Promise<Object>} API响应数据
     */
    async fetchUserInfo() {
      try {
        const response = await userApi.getUserInfo()
        const { info } = response?.data || {}

        if (!info?.ucid) {
          this.clearUserData()
          return response
        }

        this.setUserInfo(info)
        return response
      } catch (error) {
        console.error("获取用户信息失败:", error)
        this.clearUserData()
        throw error
      }
    },

    /**
     * 设置用户Token
     */
    setUserToken(newToken) {
      this.token = newToken
      setUserTokenCookie(newToken)
    },

    /**
     * 设置用户信息
     */
    setUserInfo(newInfo) {
      if (!newInfo) return

      // 更新登录状态
      if (newInfo.ucid) {
        this.isLogin = true
      }

      // 更新游客状态（有报班记录ID则不是游客）
      if (newInfo.id) {
        this.isTourist = false
      }

      this.userInfo = newInfo
    },

    /**
     * 清除用户数据
     */
    clearUserData() {
      this.token = ""
      this.userInfo = {}
      this.isLogin = false
      this.isTourist = true
      setUserTokenCookie("")
    },

    /**
     * 用户登出
     */
    logout() {
      this.clearUserData()
      // 可以在这里添加登出API调用
    },
  },
})
