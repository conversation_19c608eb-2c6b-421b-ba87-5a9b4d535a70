<navigation-bar back="{{true}}" background="#ffffff">
  <view slot="center" class="tab-title">
    <view class="text-ellipsis-1">浏览足迹</view>
  </view>
</navigation-bar>
<view class="footprint">
  <view class="tab-list {{ tabList.length < 3?'pd-style':''}}">
    <view wx:for="{{tabList}}" wx:key="index" class="tab-list-item {{ activeIndex == item.id?'active':'' }}" bind:tap="changeIndex" data-index="{{ item.id }}">
      <text class="text">{{item.title}}</text>
    </view>
  </view>
  <view class="tips-box" wx:if="{{tipsShow}}">
    <view class="contain-area">
      <view class="text">浏览足迹仅保存6个月</view>
      <image bind:tap="closetip" class="close-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/job/comparison/close.png" mode="" />
    </view>
  </view>

  <scroll-view scroll-y="{{true}}" class="scroll-list">
    <view class="list-area" wx:if="{{dataList.length}}">
      <view class="list-item" wx:for="{{ dataList }}" wx:key="index">
        <view class="time-text">{{item.time}}</view>
        <notice-card wx:if="{{activeIndex == 1}}"></notice-card>
        <job-card isNewCard="{{ true }}" wx:else></job-card>
      </view>
    </view>
    <empty-default wx:else imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/foot_no_data.png" text="暂无浏览足迹"></empty-default>
  </scroll-view>
</view>