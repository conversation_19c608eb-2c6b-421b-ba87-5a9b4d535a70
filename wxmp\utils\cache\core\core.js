/**
 * 设置多级缓存。
 *
 * 该函数接收一个点分隔的键路径和一个值，并将这个值存储在本地存储中指定的键路径下。
 * 如果中间路径中的某些键不存在，则会自动创建相应的嵌套对象。
 *
 * @param {string} key - 点分隔的键路径，例如 "user.profile.name"。
 * @param {*} value - 要存储的值。
 */
export function setCache(key, value) {
  try {
    // 将传入的键分割成数组，以便逐层访问/设置对象属性
    const keys = key.split(".")
    const keyName = keys.shift()
    // 从本地存储获取当前缓存数据，如果未找到则初始化为空对象
    let currentData = wx.getStorageSync(keyName) || {}

    // 内联递归函数用于构建多级键值对
    const setDeep = (obj, keyList, value) => {
      if (keyList.length === 1) {
        // 当到达最后一级时，直接设置值
        obj[keyList[0]] = value
      } else {
        // 获取当前层级的键
        const subKey = keyList.shift()
        // 如果当前层级的对象不存在，则初始化一个新的空对象
        if (!obj[subKey]) {
          obj[subKey] = {}
        }
        // 继续递归设置下一级
        setDeep(obj[subKey], keyList, value)
      }
    }

    if (keys.length === 0) {
      currentData = value
    } else {
      // 使用递归函数来更新当前数据
      setDeep(currentData, keys, value)
    }

    // 更新后的数据写回本地存储
    wx.setStorageSync(keyName, currentData)
  } catch (error) {
    // 捕获并记录可能发生的错误
    console.error("Failed to set cache:", error)
  }
}

/**
 * 获取多级缓存。
 *
 * 该函数接收一个点分隔的键路径，并返回本地存储中相应位置的值。
 * 如果键路径不存在，则返回默认值。
 *
 * @param {string} key - 点分隔的键路径，例如 "user.profile.name"。
 * @param {*} [defaultValue=null] - 如果找不到对应的键路径，默认返回的值。
 * @returns {*} - 存储在指定键路径下的值或默认值。
 */
export function getCache(key, defaultValue = null) {
  try {
    // 将传入的键分割成数组，以便逐层访问对象属性
    const keys = key.split(".")
    const name = keys.shift()
    // 从本地存储获取当前缓存数据，如果未找到则初始化为空对象
    let currentData = wx.getStorageSync(name)

    // 遍历键路径数组，逐步深入到指定位置
    for (const k of keys) {
      // 如果当前层级存在，则继续深入；否则返回默认值
      if (currentData[k] !== undefined) {
        currentData = currentData[k]
      } else {
        return defaultValue
      }
    }

    // 返回最终的值
    return currentData
  } catch (error) {
    // 捕获并记录可能发生的错误，同时返回默认值
    console.error("Failed to get cache:", error)
    return defaultValue
  }
}
