<view class="major-selector-inline">
  <!-- 搜索框 -->
  <view class="search-container">
    <view class="search-box">
      <input type="text" placeholder-style="color:rgba(194, 197, 204, 1)" placeholder="请输入专业名词关键词" value="{{ searchKeyword }}" bindinput="onSearchInput" />
      <image class="search-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_search.png"></image>
    </view>
  </view>

  <!-- 搜索结果显示区域 -->
  <view class="search-result" wx:if="{{ searchKeyword && searchResults.length > 0 }}">
    <view class="search-item" wx:for="{{ searchResults }}" wx:key="index" bindtap="onSelectSearchResult" data-item="{{ item }}">
      <view class="search-item-text">{{ item.fullPath }}</view>
    </view>
  </view>

  <!-- 无搜索结果 -->
  <empty-default wx:elif="{{ searchKeyword && searchResults.length === 0 }}" margin_top="160" imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/major_no_data.png" text="没有找到相关专业"></empty-default>

  <!-- 三级选择区域 -->
  <view class="major-content-center" wx:else>
    <scroll-view scroll-y class="select-item" show-scrollbar="{{false}}" enhanced>
      <view class="select-text {{ selectedFirstIndex === index ? 'active' : '' }}" wx:for="{{ firstLevelList }}" wx:key="index" bindtap="onFirstLevelSelect" data-index="{{ index }}">{{ item.name }}</view>
    </scroll-view>
    <scroll-view scroll-y class="select-item" show-scrollbar="{{false}}" enhanced>
      <view class="select-text {{ selectedSecondIndex === index ? 'active' : '' }}" wx:for="{{ secondLevelList }}" wx:key="index" bindtap="onSecondLevelSelect" data-index="{{ index }}">{{ item.name }}</view>
    </scroll-view>
    <scroll-view scroll-y class="select-item" show-scrollbar="{{false}}" enhanced>
      <view class="select-text {{ selectedThirdIndex === index ? 'active' : '' }}" wx:for="{{ thirdLevelList }}" wx:key="index" bindtap="onThirdLevelSelect" data-index="{{ index }}">{{ item.name }}</view>
    </scroll-view>
  </view>
</view>